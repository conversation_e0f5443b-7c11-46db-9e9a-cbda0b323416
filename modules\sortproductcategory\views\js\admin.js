

var SortProductCategory = {
    currentCategoryId: 0,
    adminUrl: '',
    dragDropEnabled: false,
    autoSaveEnabled: false,
    
    init: function(categoryId, adminUrl) {
        this.currentCategoryId = categoryId;
        this.adminUrl = adminUrl;
        this.autoSaveEnabled = document.querySelector('input[name="auto_save"]') ? 
                               document.querySelector('input[name="auto_save"]').checked : false;
        
        this.initDragDrop();
        this.initEventListeners();
        this.initAdvancedSearch();
    },
    
    initDragDrop: function() {
        // Drag & Drop disabled - using direct position input instead
        this.dragDropEnabled = false;
    },
    
    initEventListeners: function() {
        // Real-time search
        var searchInput = document.getElementById('search-input');
        if (searchInput) {
            var searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    SortProductCategory.performSearch();
                }, 500);
            });
        }
        
        // Auto-sort buttons
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('auto-sort-btn')) {
                var sortBy = e.target.getAttribute('data-sort-by');
                var direction = e.target.getAttribute('data-direction') || 'ASC';
                SortProductCategory.autoSort(sortBy, direction);
            }
        });
        
        // Advanced filters
        var advancedFilters = document.querySelectorAll('.advanced-filter');
        advancedFilters.forEach(function(filter) {
            filter.addEventListener('change', function() {
                SortProductCategory.performAdvancedSearch();
            });
        });
    },
    
    initAdvancedSearch: function() {
        // Add advanced search panel if not exists
        var searchPanel = document.getElementById('advanced-search-panel');
        if (!searchPanel) {
            this.createAdvancedSearchPanel();
        }
    },
    
    createAdvancedSearchPanel: function() {
        var panel = document.createElement('div');
        panel.id = 'advanced-search-panel';
        panel.className = 'panel panel-default';
        panel.style.display = 'none';
        
        panel.innerHTML = `
            <div class="panel-heading">
                <h4>Zaawansowane wyszukiwanie</h4>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-lg-3">
                        <label>Tylko aktywne produkty:</label>
                        <input type="checkbox" class="advanced-filter" name="active_only">
                    </div>
                    <div class="col-lg-3">
                        <label>Cena od:</label>
                        <input type="number" class="form-control advanced-filter" name="min_price" step="0.01">
                    </div>
                    <div class="col-lg-3">
                        <label>Cena do:</label>
                        <input type="number" class="form-control advanced-filter" name="max_price" step="0.01">
                    </div>
                    <div class="col-lg-3">
                        <label>Min. ilość:</label>
                        <input type="number" class="form-control advanced-filter" name="min_quantity">
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-lg-12">
                        <button type="button" class="btn btn-primary" onclick="SortProductCategory.performAdvancedSearch()">
                            Zastosuj filtry
                        </button>
                        <button type="button" class="btn btn-default" onclick="SortProductCategory.clearAdvancedFilters()">
                            Wyczyść
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        var searchRow = document.querySelector('.row');
        if (searchRow) {
            searchRow.parentNode.insertBefore(panel, searchRow.nextSibling);
        }
        
        // Add toggle button
        var toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'btn btn-info btn-sm';
        toggleBtn.innerHTML = '<i class="icon-filter"></i> Filtry zaawansowane';
        toggleBtn.onclick = function() {
            var panel = document.getElementById('advanced-search-panel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        };
        
        var searchGroup = document.querySelector('.form-group');
        if (searchGroup) {
            searchGroup.appendChild(toggleBtn);
        }
    },
    

    

    
    performSearch: function() {
        var search = document.getElementById('search-input').value;
        var orderBy = document.getElementById('order-by').value;
        var orderWay = document.getElementById('order-way').value;
        
        this.loadProducts({
            search: search,
            order_by: orderBy,
            order_way: orderWay,
            page: 1
        });
    },
    
    performAdvancedSearch: function() {
        var filters = this.getAdvancedFilters();
        this.loadProducts(filters);
    },
    
    getAdvancedFilters: function() {
        var filters = {
            search: document.getElementById('search-input').value,
            order_by: document.getElementById('order-by').value,
            order_way: document.getElementById('order-way').value,
            page: 1
        };
        
        var advancedFilters = document.querySelectorAll('.advanced-filter');
        advancedFilters.forEach(function(filter) {
            if (filter.type === 'checkbox') {
                filters[filter.name] = filter.checked;
            } else if (filter.value) {
                filters[filter.name] = filter.value;
            }
        });
        
        return filters;
    },
    
    clearAdvancedFilters: function() {
        var advancedFilters = document.querySelectorAll('.advanced-filter');
        advancedFilters.forEach(function(filter) {
            if (filter.type === 'checkbox') {
                filter.checked = false;
            } else {
                filter.value = '';
            }
        });
        
        this.performSearch();
    },
    
    loadProducts: function(filters) {
        var params = new URLSearchParams();
        params.append('id_category', this.currentCategoryId);
        
        Object.keys(filters).forEach(function(key) {
            if (filters[key] !== null && filters[key] !== '') {
                params.append(key, filters[key]);
            }
        });
        
        fetch(this.adminUrl + '&ajax=1&action=advancedSearch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.updateProductTable(data.products);
                this.updatePagination(data.page, data.total_pages);
            } else {
                alert(data.message || 'Błąd podczas wyszukiwania');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Błąd podczas wyszukiwania');
        });
    },
    
    updateProductTable: function(products) {
        var tbody = document.getElementById('sortable-products');
        if (!tbody) return;
        
        tbody.innerHTML = '';
        
        products.forEach(function(product, index) {
            var row = document.createElement('tr');
            row.setAttribute('data-product-id', product.id_product);
            row.setAttribute('data-position', product.position);
            
            row.innerHTML = `
                <td><input type="checkbox" class="product-checkbox" value="${product.id_product}"></td>
                <td class="position-cell">
                    <input type="number" class="form-control input-sm new-position"
                           value="${parseInt(product.position) + 1}" min="1"
                           onchange="SortProductCategory.updateSinglePosition(${product.id_product}, this.value - 1)">
                </td>
                <td>${product.id_product}</td>
                <td><strong>${product.name}</strong></td>
                <td>${product.reference || ''}</td>
                <td>${parseFloat(product.price).toFixed(2)} zł</td>
                <td>
                    ${product.active == 1 ?
                        '<span class="label label-success">Aktywny</span>' :
                        '<span class="label label-danger">Nieaktywny</span>'}
                </td>
                <td>
                    <button type="button" class="btn btn-xs btn-primary"
                            onclick="SortProductCategory.updateSinglePosition(${product.id_product}, document.querySelector('tr[data-product-id=\\'${product.id_product}\\'] .new-position').value - 1)"
                            title="Aktualizuj pozycję">
                        <i class="icon-check"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
        
        // Reinitialize drag and drop
        if (this.dragDropEnabled) {
            this.initDragDrop();
        }
    },
    
    updatePagination: function(currentPage, totalPages) {
        // Update pagination if exists
        var pagination = document.querySelector('.pagination');
        if (pagination && totalPages > 1) {
            // Rebuild pagination - simplified version
            pagination.innerHTML = '';

            for (var i = 1; i <= totalPages; i++) {
                var li = document.createElement('li');
                if (i === currentPage) {
                    li.className = 'active';
                }

                var a = document.createElement('a');
                a.href = '#';
                a.textContent = i;
                a.onclick = (function(page) {
                    return function(e) {
                        e.preventDefault();
                        var filters = SortProductCategory.getAdvancedFilters();
                        filters.page = page;
                        SortProductCategory.loadProducts(filters);
                    };
                })(i);

                li.appendChild(a);
                pagination.appendChild(li);
            }
        }
    },

    updateSinglePosition: function(productId, newPosition) {
        if (newPosition < 0) {
            alert('Pozycja nie może być mniejsza niż 0');
            return;
        }

        fetch(this.adminUrl + '&ajax=1&action=updatePosition', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id_product=' + productId + '&id_category=' + this.currentCategoryId + '&new_position=' + newPosition
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showSuccessMessage(data.message);
                // Optionally reload current view
                this.performSearch();
            } else {
                alert(data.message || 'Błąd podczas aktualizacji pozycji');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Błąd podczas aktualizacji pozycji');
        });
    },

    bulkUpdatePositions: function(positions) {
        fetch(this.adminUrl + '&ajax=1&action=bulkUpdatePositions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id_category=' + this.currentCategoryId + '&positions=' + encodeURIComponent(JSON.stringify(positions))
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showSuccessMessage(data.message);
            } else {
                alert(data.message || 'Błąd podczas aktualizacji pozycji');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Błąd podczas aktualizacji pozycji');
        });
    },

    autoSort: function(sortBy, direction) {
        if (confirm('Czy na pewno chcesz automatycznie posortować produkty według: ' + sortBy + '?')) {
            fetch(this.adminUrl + '&ajax=1&action=autoSort', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'id_category=' + this.currentCategoryId + '&sort_by=' + sortBy + '&direction=' + direction
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showSuccessMessage(data.message);
                    this.performSearch(); // Reload to show new order
                } else {
                    alert(data.message || 'Błąd podczas sortowania');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Błąd podczas sortowania');
            });
        }
    },

    cleanPositions: function() {
        if (confirm('Czy na pewno chcesz uporządkować pozycje? Produkty zostaną ponumerowane od 1.')) {
            fetch(this.adminUrl + '&ajax=1&action=cleanPositions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'id_category=' + this.currentCategoryId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showSuccessMessage(data.message);
                    this.performSearch();
                } else {
                    alert(data.message || 'Błąd podczas porządkowania pozycji');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Błąd podczas porządkowania pozycji');
            });
        }
    },

    bulkMoveToPosition: function() {
        var selectedProducts = [];
        var checkboxes = document.querySelectorAll('.product-checkbox:checked');
        var startPosition = parseInt(document.getElementById('bulk-start-position').value);

        if (checkboxes.length === 0) {
            alert('Proszę zaznaczyć produkty do przeniesienia');
            return;
        }

        if (isNaN(startPosition) || startPosition < 1) {
            alert('Proszę podać prawidłową pozycję początkową (min. 1)');
            return;
        }

        checkboxes.forEach(function(checkbox) {
            selectedProducts.push(parseInt(checkbox.value));
        });

        var positions = {};
        selectedProducts.forEach(function(productId, index) {
            positions[productId] = (startPosition - 1) + index;
        });

        this.bulkUpdatePositions(positions);
    },

    showSuccessMessage: function(message) {
        // Create or update success message
        var successDiv = document.getElementById('success-message');
        if (!successDiv) {
            successDiv = document.createElement('div');
            successDiv.id = 'success-message';
            successDiv.className = 'alert alert-success';
            successDiv.style.position = 'fixed';
            successDiv.style.top = '20px';
            successDiv.style.right = '20px';
            successDiv.style.zIndex = '9999';
            successDiv.style.minWidth = '300px';
            document.body.appendChild(successDiv);
        }

        successDiv.innerHTML = '<i class="icon-check"></i> ' + message;
        successDiv.style.display = 'block';

        // Auto-hide after 3 seconds
        setTimeout(function() {
            successDiv.style.display = 'none';
        }, 3000);
    },

    getPositionGaps: function() {
        fetch(this.adminUrl + '&ajax=1&action=getPositionGaps', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id_category=' + this.currentCategoryId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.count > 0) {
                    alert('Znaleziono ' + data.count + ' luk w pozycjach: ' + data.gaps.join(', '));
                } else {
                    alert('Nie znaleziono luk w pozycjach');
                }
            } else {
                alert(data.message || 'Błąd podczas sprawdzania luk');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Błąd podczas sprawdzania luk');
        });
    }
};

// Global functions for template compatibility
function updateSinglePosition(productId, newPosition) {
    SortProductCategory.updateSinglePosition(productId, newPosition);
}

function bulkMoveToPosition() {
    SortProductCategory.bulkMoveToPosition();
}

function cleanPositions() {
    SortProductCategory.cleanPositions();
}

function searchProducts() {
    SortProductCategory.performSearch();
}

function toggleSelectAll() {
    var selectAll = document.getElementById('select-all');
    var checkboxes = document.querySelectorAll('.product-checkbox');

    checkboxes.forEach(function(checkbox) {
        checkbox.checked = selectAll.checked;
    });
}
