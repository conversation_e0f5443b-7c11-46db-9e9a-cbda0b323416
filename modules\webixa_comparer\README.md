# Webixa Comparer

Moduł porównywania produktów dla PrestaShop 8.0+

## Opis

Moduł umożliwia klientom porównywanie produktów w sklepie. Klienci mogą dodawać produkty do porównania, prz<PERSON><PERSON><PERSON><PERSON><PERSON> je w tabeli porównawczej i usuwać z listy porównania.

## Funkcjonalności

- Dodawanie produktów do porównania z listy produktów
- Wyświetlanie licznika produktów w porównaniu
- Strona porównania z tabelą cech produktów
- Automatyczne czyszczenie starych porównań (CRON)
- Obsługa zarówno zalogowanych jak i niezalogowanych użytkowników

## Instalacja

1. Skopiuj moduł do katalogu `modules/webixa_comparer/`
2. Zainstaluj moduł w panelu administracyjnym PrestaShop
3. Moduł automatycznie utworzy wymaganą tabelę w bazie danych

## Konfiguracja CRON

Moduł zawiera zadanie CRON do automatycznego czyszczenia starych porównań (starszych niż 30 dni) dla niezalogowanych użytkowników.

### Uruchomienie zadania CRON

Aby skonfigurować automatyczne czyszczenie, dodaj następujące zadanie CRON do swojego serwera:

```bash
# Uruchamianie co 24 godziny o 2:00 w nocy
0 2 * * * curl -s "https://twoja-domena.pl/modules/webixa_comparer/cron.php?process_name=webixa_comparer_clean"
```

### Alternatywnie przez wget:

```bash
0 2 * * * wget -q -O /dev/null "https://twoja-domena.pl/modules/webixa_comparer/cron.php?process_name=webixa_comparer_clean"
```

### Parametry CRON:

- `process_name=webixa_comparer_clean` - nazwa procesu do uruchomienia
- Zadanie usuwa porównania starsze niż 30 dni dla niezalogowanych użytkowników (id_customer = 0)

### Testowanie CRON:

Możesz przetestować działanie zadania CRON odwiedzając URL w przeglądarce:
```
https://twoja-domena.pl/modules/webixa_comparer/cron.php?process_name=webixa_comparer_clean
```

## Użycie

### Dla klientów:

1. Na stronie produktu lub liście produktów kliknij przycisk "Dodaj do porównania"
2. Licznik w górnej części strony pokaże liczbę produktów w porównaniu
3. Kliknij na licznik lub przejdź do `/module/webixa_comparer/compare` aby zobaczyć porównanie
4. Na stronie porównania możesz usuwać pojedyncze produkty lub wyczyścić całe porównanie

### Limity:

- Maksymalnie 4 produkty można porównywać jednocześnie
- Porównania dla niezalogowanych użytkowników są automatycznie usuwane po 30 dniach

## Struktura plików

```
webixa_comparer/
├── webixa_comparer.php          # Główny plik modułu
├── controllers/
│   └── front/
│       └── compare.php          # Kontroler strony porównania
├── views/
│   ├── css/
│   │   └── tailwind.css         # Style CSS
│   ├── js/
│   │   └── comparer.js          # JavaScript
│   └── templates/
│       ├── front/
│       │   └── comparison_page.tpl  # Szablon strony porównania
│       └── hooks/
│           ├── button.tpl       # Przycisk dodawania do porównania
│           └── top.tpl          # Licznik w górnej części strony
├── config_pl.xml               # Konfiguracja modułu
└── README.md                   # Ten plik
```

## Wymagania

- PrestaShop 8.0+
- PHP 7.4+
- MySQL 5.7+

## Wsparcie

W przypadku problemów skontaktuj się z Webixa sp. z o.o.
