<?php
/**
 * AdminBlockBotController
 */

class AdminBlockBotController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->lang = false;

        parent::__construct();

        $this->meta_title = $this->l('Zarządzanie BlockBot');
    }

    public function init()
    {
        parent::init();

        // Ensure module is loaded
        if (!$this->module) {
            $this->module = Module::getInstanceByName('blockbot');
        }
    }

    public function initContent()
    {
        $this->content = $this->renderView();
        parent::initContent();
    }

    public function renderView()
    {
        $tab = Tools::getValue('tab', 'ip_stats');
        
        switch ($tab) {
            case 'ip_stats':
                return $this->renderIpStatsTab();
            case 'useragent_stats':
                return $this->renderUserAgentStatsTab();
            case 'settings':
                return $this->renderSettingsTab();
            default:
                return $this->renderIpStatsTab();
        }
    }

    private function renderIpStatsTab()
    {
        $ipStats = $this->module->getIpStats();
        $blockedIps = $this->module->getBlockedIps();
        
        $this->context->smarty->assign([
            'ip_stats' => $ipStats,
            'blocked_ips' => $blockedIps,
            'current_tab' => 'ip_stats',
            'module_dir' => $this->module->getLocalPath(),
            'admin_url' => $this->context->link->getAdminLink('AdminBlockBot'),
            'threshold_ip' => Configuration::get('BLOCKBOT_THRESHOLD_IP')
        ]);
        
        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/ip_stats.tpl');
    }

    private function renderUserAgentStatsTab()
    {
        $agentStats = $this->module->getUserAgentStats();
        $blockedAgents = $this->module->getBlockedUserAgents();

        // Mark standard browsers to avoid showing them as suspicious
        foreach ($agentStats as &$stat) {
            $stat['is_standard_browser'] = $this->module->isStandardBrowser($stat['user_agent']);
        }

        $this->context->smarty->assign([
            'agent_stats' => $agentStats,
            'blocked_agents' => $blockedAgents,
            'current_tab' => 'useragent_stats',
            'module_dir' => $this->module->getLocalPath(),
            'admin_url' => $this->context->link->getAdminLink('AdminBlockBot'),
            'threshold_useragent' => Configuration::get('BLOCKBOT_THRESHOLD_USERAGENT')
        ]);

        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/useragent_stats.tpl');
    }

    private function renderSettingsTab()
    {
        $this->context->smarty->assign([
            'current_tab' => 'settings',
            'module_dir' => $this->module->getLocalPath(),
            'admin_url' => $this->context->link->getAdminLink('AdminBlockBot'),
            'threshold_ip' => Configuration::get('BLOCKBOT_THRESHOLD_IP'),
            'threshold_useragent' => Configuration::get('BLOCKBOT_THRESHOLD_USERAGENT'),
            'rapid_fire_ip' => Configuration::get('BLOCKBOT_RAPID_FIRE_IP'),
            'rapid_fire_agent' => Configuration::get('BLOCKBOT_RAPID_FIRE_AGENT'),
            'enabled' => Configuration::get('BLOCKBOT_ENABLED'),
            'auto_block' => Configuration::get('BLOCKBOT_AUTO_BLOCK'),
            'block_empty_agent' => Configuration::get('BLOCKBOT_BLOCK_EMPTY_AGENT'),
            'cleanup_months' => Configuration::get('BLOCKBOT_CLEANUP_MONTHS'),
            'last_cleanup' => Configuration::get('BLOCKBOT_LAST_CLEANUP'),
            'cron_key' => Configuration::get('BLOCKBOT_CRON_KEY'),
            'email_notifications' => Configuration::get('BLOCKBOT_EMAIL_NOTIFICATIONS'),
            'email_addresses' => Configuration::get('BLOCKBOT_EMAIL_ADDRESSES'),
            'suspicious_alerts' => $this->module->getSuspiciousActivityAlert(),
            'log_stats' => $this->module->getLogStats()
        ]);
        
        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/settings.tpl');
    }

    public function postProcess()
    {
        // Handle standard grid actions
        $action = Tools::getValue('action');
        if ($action === 'delete') {
            $this->processDelete();
            return;
        } elseif ($action === 'block') {
            $this->processBlock();
            return;
        }

        if (Tools::isSubmit('blockIp')) {
            $ip = Tools::getValue('ip');
            if ($ip && filter_var($ip, FILTER_VALIDATE_IP)) {
                $currentIp = $this->module->getClientIp();
                if ($ip === $currentIp) {
                    $this->errors[] = $this->l('Nie można zablokować własnego adresu IP');
                } elseif ($this->module->blockIp($ip)) {
                    $this->confirmations[] = $this->l('Adres IP został pomyślnie zablokowany');
                } else {
                    $this->errors[] = $this->l('Błąd podczas blokowania adresu IP');
                }
            } else {
                $this->errors[] = $this->l('Nieprawidłowy adres IP');
            }
        }

        if (Tools::isSubmit('blockUserAgent')) {
            $userAgent = Tools::getValue('user_agent');
            if ($userAgent) {
                // Check if it's a good bot
                $botCheck = $this->module->getGoodBotWarning($userAgent);
                if ($botCheck['is_good_bot']) {
                    $this->warnings[] = $botCheck['warning'];
                    $this->errors[] = $this->l('Blokowanie anulowane - to wydaje się być legalny bot');
                } else {
                    if ($this->module->blockUserAgent($userAgent)) {
                        $this->confirmations[] = $this->l('User Agent został pomyślnie zablokowany');
                    } else {
                        $this->errors[] = $this->l('Błąd podczas blokowania User Agent');
                    }
                }
            } else {
                $this->errors[] = $this->l('Nieprawidłowy User Agent');
            }
        }

        if (Tools::isSubmit('blockSelectedIps')) {
            $selectedIps = Tools::getValue('selected_ips', []);
            $blocked = 0;
            $skipped = 0;
            $currentIp = $this->module->getClientIp();

            foreach ($selectedIps as $ip) {
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    if ($ip === $currentIp) {
                        $skipped++;
                        continue;
                    }
                    if ($this->module->blockIp($ip)) {
                        $blocked++;
                    }
                }
            }
            if ($blocked > 0) {
                $this->confirmations[] = sprintf($this->l('%d adresów IP zostało pomyślnie zablokowanych'), $blocked);
            }
            if ($skipped > 0) {
                $this->warnings[] = $this->l('Twój własny adres IP został pominięty aby zapobiec samo-blokowaniu');
            }
        }

        if (Tools::isSubmit('deleteSelectedIpLogs')) {
            $selectedIps = Tools::getValue('selected_ips', []);
            $deleted = 0;

            foreach ($selectedIps as $ip) {
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    if ($this->module->deleteLogsByIp($ip)) {
                        $deleted++;
                    }
                }
            }
            if ($deleted > 0) {
                $this->confirmations[] = sprintf($this->l('Logi usunięte dla %d adresów IP'), $deleted);
            }
        }

        if (Tools::isSubmit('blockSelectedAgents') || Tools::isSubmit('blockSelectedUserAgents')) {
            $selectedAgents = Tools::getValue('selected_agents', []);
            $blocked = 0;
            $skipped = 0;
            foreach ($selectedAgents as $agent) {
                if ($agent) {
                    // Check if it's a good bot
                    $botCheck = $this->module->getGoodBotWarning($agent);
                    if ($botCheck['is_good_bot']) {
                        $skipped++;
                        continue;
                    }

                    if ($this->module->blockUserAgent($agent)) {
                        $blocked++;
                    }
                }
            }
            if ($blocked > 0) {
                $this->confirmations[] = sprintf($this->l('%d User Agents zostało pomyślnie zablokowanych'), $blocked);
            }
            if ($skipped > 0) {
                $this->warnings[] = sprintf($this->l('%d legalnych botów zostało pominiętych w celu ochrony SEO'), $skipped);
            }
        }

        if (Tools::isSubmit('deleteSelectedUserAgentLogs')) {
            $selectedAgents = Tools::getValue('selected_agents', []);
            $deleted = 0;

            foreach ($selectedAgents as $agent) {
                if ($agent) {
                    if ($this->module->deleteLogsByUserAgent($agent)) {
                        $deleted++;
                    }
                }
            }
            if ($deleted > 0) {
                $this->confirmations[] = sprintf($this->l('Logi usunięte dla %d User Agents'), $deleted);
            }
        }

        if (Tools::isSubmit('clearLog')) {
            if ($this->module->clearLogs()) {
                $this->confirmations[] = $this->l('Logi zostały pomyślnie wyczyszczone');
            } else {
                $this->errors[] = $this->l('Błąd podczas czyszczenia logów');
            }
        }

        if (Tools::isSubmit('saveSettings')) {
            $thresholdIp = (int)Tools::getValue('threshold_ip');
            $thresholdUserAgent = (int)Tools::getValue('threshold_useragent');
            $rapidFireIp = (int)Tools::getValue('rapid_fire_ip');
            $rapidFireAgent = (int)Tools::getValue('rapid_fire_agent');
            $enabled = (bool)Tools::getValue('enabled');
            $autoBlock = (bool)Tools::getValue('auto_block');
            $blockEmptyAgent = (bool)Tools::getValue('block_empty_agent');
            $cleanupMonths = (int)Tools::getValue('cleanup_months');
            $emailNotifications = (bool)Tools::getValue('email_notifications');
            $emailAddresses = Tools::getValue('email_addresses');

            Configuration::updateValue('BLOCKBOT_THRESHOLD_IP', $thresholdIp);
            Configuration::updateValue('BLOCKBOT_THRESHOLD_USERAGENT', $thresholdUserAgent);
            Configuration::updateValue('BLOCKBOT_RAPID_FIRE_IP', $rapidFireIp);
            Configuration::updateValue('BLOCKBOT_RAPID_FIRE_AGENT', $rapidFireAgent);
            Configuration::updateValue('BLOCKBOT_ENABLED', $enabled);
            Configuration::updateValue('BLOCKBOT_AUTO_BLOCK', $autoBlock);
            Configuration::updateValue('BLOCKBOT_BLOCK_EMPTY_AGENT', $blockEmptyAgent);
            Configuration::updateValue('BLOCKBOT_CLEANUP_MONTHS', $cleanupMonths);
            Configuration::updateValue('BLOCKBOT_EMAIL_NOTIFICATIONS', $emailNotifications);
            Configuration::updateValue('BLOCKBOT_EMAIL_ADDRESSES', $emailAddresses);

            $this->confirmations[] = $this->l('Ustawienia zostały pomyślnie zapisane');
        }

        if (Tools::isSubmit('autoBlockSuspicious')) {
            $blocked = $this->module->autoBlockSuspiciousTraffic();
            if ($blocked > 0) {
                $this->confirmations[] = sprintf($this->l('%d podejrzanych wpisów zostało automatycznie zablokowanych'), $blocked);
            } else {
                $this->info[] = $this->l('Nie znaleziono podejrzanych wpisów do zablokowania');
            }
        }

        if (Tools::isSubmit('cleanOldLogs')) {
            $months = (int)Configuration::get('BLOCKBOT_CLEANUP_MONTHS');
            $deleted = $this->module->cleanOldLogs($months);
            if ($deleted !== false) {
                Configuration::updateValue('BLOCKBOT_LAST_CLEANUP', date('Y-m-d H:i:s'));
                $this->confirmations[] = sprintf($this->l('Stare logi zostały pomyślnie wyczyszczone (starsze niż %d miesięcy)'), $months);
            } else {
                $this->errors[] = $this->l('Błąd podczas czyszczenia starych logów');
            }
        }

        if (Tools::isSubmit('generateCronKey')) {
            $cronKey = Tools::passwdGen(32);
            Configuration::updateValue('BLOCKBOT_CRON_KEY', $cronKey);
            $this->confirmations[] = $this->l('Nowy klucz cron został pomyślnie wygenerowany');
        }

        if (Tools::isSubmit('unblockIp')) {
            $ip = Tools::getValue('ip');
            if ($this->module->unblockIp($ip)) {
                $this->confirmations[] = $this->l('Adres IP został pomyślnie odblokowany');
            } else {
                $this->errors[] = $this->l('Błąd podczas odblokowywania adresu IP');
            }
        }

        if (Tools::isSubmit('unblockUserAgent')) {
            $userAgent = Tools::getValue('user_agent');
            if ($this->module->unblockUserAgent($userAgent)) {
                $this->confirmations[] = $this->l('User Agent został pomyślnie odblokowany');
            } else {
                $this->errors[] = $this->l('Błąd podczas odblokowywania User Agent');
            }
        }

        if (Tools::isSubmit('deleteLogsByIp')) {
            $ip = Tools::getValue('ip');
            if ($this->module->deleteLogsByIp($ip)) {
                $this->confirmations[] = $this->l('Wszystkie logi dla tego adresu IP zostały pomyślnie usunięte');
            } else {
                $this->errors[] = $this->l('Błąd podczas usuwania logów dla tego adresu IP');
            }
        }

        if (Tools::isSubmit('deleteLogsByUserAgent')) {
            $userAgent = Tools::getValue('user_agent');
            if ($this->module->deleteLogsByUserAgent($userAgent)) {
                $this->confirmations[] = $this->l('Wszystkie logi dla tego User Agent zostały pomyślnie usunięte');
            } else {
                $this->errors[] = $this->l('Błąd podczas usuwania logów dla tego User Agent');
            }
        }

        parent::postProcess();
    }

    public function processDelete()
    {
        $tab = Tools::getValue('tab', 'ip_stats');

        if ($tab === 'ip_stats') {
            $ip = Tools::getValue('ip');
            if ($this->module->deleteLogsByIp($ip)) {
                $this->confirmations[] = $this->l('Wszystkie logi dla tego adresu IP zostały pomyślnie usunięte');
            } else {
                $this->errors[] = $this->l('Błąd podczas usuwania logów dla tego adresu IP');
            }
        } elseif ($tab === 'useragent_stats') {
            $userAgent = Tools::getValue('user_agent');
            if ($this->module->deleteLogsByUserAgent($userAgent)) {
                $this->confirmations[] = $this->l('Wszystkie logi dla tego User Agent zostały pomyślnie usunięte');
            } else {
                $this->errors[] = $this->l('Błąd podczas usuwania logów dla tego User Agent');
            }
        }
    }

    public function processBlock()
    {
        $tab = Tools::getValue('tab', 'ip_stats');

        if ($tab === 'ip_stats') {
            $ip = Tools::getValue('ip');
            if ($ip && filter_var($ip, FILTER_VALIDATE_IP)) {
                $currentIp = $this->module->getClientIp();
                if ($ip === $currentIp) {
                    $this->errors[] = $this->l('Cannot block your own IP address');
                } elseif ($this->module->blockIp($ip)) {
                    $this->confirmations[] = $this->l('IP address blocked successfully');
                } else {
                    $this->errors[] = $this->l('Error blocking IP address');
                }
            } else {
                $this->errors[] = $this->l('Invalid IP address');
            }
        } elseif ($tab === 'useragent_stats') {
            $userAgent = Tools::getValue('user_agent');
            if ($userAgent) {
                $botCheck = $this->module->getGoodBotWarning($userAgent);
                if ($botCheck['is_good_bot']) {
                    $this->warnings[] = $botCheck['warning'];
                    $this->errors[] = $this->l('Blocking cancelled - this appears to be a legitimate bot');
                } else {
                    if ($this->module->blockUserAgent($userAgent)) {
                        $this->confirmations[] = $this->l('User Agent blocked successfully');
                    } else {
                        $this->errors[] = $this->l('Error blocking User Agent');
                    }
                }
            } else {
                $this->errors[] = $this->l('Invalid User Agent');
            }
        }
    }



    public function setMedia($isNewTheme = false)
    {
        parent::setMedia();
    }
}
