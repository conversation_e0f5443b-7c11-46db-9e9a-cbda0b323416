const inspirationModule = {
    state: {
        inspirations: [],
        categories: [],
        selectedCategory: null,
        inspiration_products: [],
    },

    async init() {
        try {
            this.bindEvents();
        } catch (error) {
            console.error('Failed to initialize inspiration module:', error);
        }
    },

    bindEvents() {
        document.addEventListener('mouseover', (e) => {
            if (e.target.classList.contains('inspiration-hotspot')) {
                this.showProductTooltip(e.target);
            }
        });
    },

    showProductTooltip(markerElement) {        
        const productId = markerElement.getAttribute('data-id-product');                
        let product = null;
        if (inspirationModule.state.inspiration_products && inspirationModule.state.inspiration_products.length > 0) {
            product = inspirationModule.state.inspiration_products.find(p => p.id_product == productId);            
        }              
        if (!product) {            
            return;
        }              
    },
};

document.addEventListener('DOMContentLoaded', () => {
    inspirationModule.init();
});
