<?php

/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateDescriptionProductsModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateDescriptionProductsModel');
        parent::__construct($module, $object, $context);
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product) {
                $result[$id_product] = array(
                    'error' => true,
                    'message' => ''
                );

                $id_shop = Tools::getValue('shop');

                $product = new Product($id_product, false, null, $id_shop);
                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                $is_short = !!(int)Tools::getValue('is_short', 0);
                $is_desc = !!(int)Tools::getValue('is_desc', 0);
                $type_change = (int)Tools::getValue('type_change', 0);
                $short = Tools::getValue('short', array());
                $desc = Tools::getValue('desc', array());

                if ($is_short)
                    foreach ($this->object->languages as $language) switch ($type_change) {
                            case 1:
                                $product->description_short[$language['id_lang']] = $short[$language['id_lang']].$product->description_short[$language['id_lang']];
                                break;
                            case 2:
                                $product->description_short[$language['id_lang']] .= $short[$language['id_lang']];
                                break;
                            default:
                                $product->description_short[$language['id_lang']] = $short[$language['id_lang']];
                                break;
                        }

                if ($is_desc)
                    foreach ($this->object->languages as $language) switch ($type_change) {
                            case 1:
                                $product->description[$language['id_lang']] = $desc[$language['id_lang']].$product->description[$language['id_lang']];
                                break;
                            case 2:
                                $product->description[$language['id_lang']] .= $desc[$language['id_lang']];
                                break;
                            default:
                                $product->description[$language['id_lang']] = $desc[$language['id_lang']];
                                break;
                        }

                $errors = $product->validateFields(false, true);
                $errors2 = $product->validateFieldsLang(false, true);
                if ($errors !== true || $errors2 !== true) {
                    if ($errors !== true)
                        $result[$id_product]['message'] = '<p style="color: #FFF;">'.(is_bool($errors) ?
                                        $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)).'</p>';
                    if ($errors2 !== true)
                        $result[$id_product]['message'] = '<p style="color: #FFF;">'.(is_bool($errors2) ?
                                        $this->module->l('Validate error', $this->languages_name) : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)).'</p>';
                    continue;
                }
                else {
                    if ($product->update()) {
                        $result[$id_product]['message'] = $this->module->l('Product saved', $this->languages_name).': '.$product->name[$this->context->language->id];
                        $result[$id_product]['error'] = false;
                    } else {
                        $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                        continue;
                    }
                }
            } else
            $end = true;

        return array(
            'result' => $result,
            'end' => $end
        );
    }

    public function displayCombination(&$product, &$combination)
    {
        if (!parent::displayCombination($product, $combination))
            return '';
    }

    public function display($result)
    {
        $return = array();
        $return['table'] = false;
        $return['product_count'] = $result['datas']['product_count'];
        return $result;
    }

    public function extra()
    {
        $helper = new HelperForm();

        $helper->module = $this->module;
        $helper->name_controller = $this->module->name;
        $helper->token = Tools::getAdminTokenLite('AdminMassUpdateProducts');
        $helper->currentIndex = AdminController::$currentIndex.'&configure='.$this->module->name;
        $helper->languages = $this->object->languages;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = true;

        $helper->title = '';
        $helper->show_toolbar = false;
        $helper->toolbar_scroll = false;
        $helper->submit_action = $this->module->name;

        $options_short = array();
        $options_short[] = array(
            'id_option' => 0,
            'name' => $this->module->l('Change short description', $this->languages_name)
        );

        $options_desc = array();
        $options_desc[] = array(
            'id_option' => 0,
            'name' => $this->module->l('Change description', $this->languages_name)
        );

        $fields_form = array();
        $fields_form[0]['form'] = array(
            'input' => array(
                array(
                    'type' => 'checkbox',
                    'name' => 'change_short_desc',
                    'class' => 'change-short-desc t',
                    //'label' => $this->module->l('Change short description', $this->languages_name),
                    'values' => array(
                        'query' => $options_short,
                        'id' => 'id_option',
                        'name' => 'name'
                    )
                ),
                array(
                    'type' => 'textarea',
                    'name' => 'short_desc',
                    'label' => $this->module->l('Short description', $this->languages_name),
                    'lang' => true,
                    'cols' => 100,
                    'rows' => 5,
                    'class' => 'rte t',
                    'autoload_rte' => true,
                ),
                array(
                    'type' => 'checkbox',
                    'name' => 'change_desc',
                    'class' => 'change-desc t',
                    //'label' => $this->module->l('Change description', $this->languages_name),
                    'values' => array(
                        'query' => $options_desc,
                        'id' => 'id_option',
                        'name' => 'name'
                    )
                ),
                array(
                    'type' => 'textarea',
                    'name' => 'desc',
                    'label' => $this->module->l('Description', $this->languages_name),
                    'lang' => true,
                    'cols' => 100,
                    'rows' => 5,
                    'class' => 'rte t',
                    'autoload_rte' => true,
                ),
                array(
                    'type' => 'radio',
                    'name' => 'change_check',
                    'class' => 'change-check t',
                    'label' => $this->module->l('Type of change description', $this->languages_name),
                    'values' => array(
                        array(
                            'id' => 'full',
                            'value' => 0,
                            'label' => $this->module->l('Full description', $this->languages_name)
                        ),
                        array(
                            'id' => 'prefix',
                            'value' => 1,
                            'label' => $this->module->l('Prefix description', $this->languages_name)
                        ),
                        array(
                            'id' => 'suffix',
                            'value' => 2,
                            'label' => $this->module->l('Suffix description', $this->languages_name)
                        )
                    )
                )
            )
        );

        $langs = array();

        foreach ($this->object->languages as $language) $langs[$language['id_lang']] = '';

        $helper->fields_value = array_merge(array(
            'change_short_desc' => false,
            'change_desc' => false,
            'short_desc' => $langs,
            'desc' => $langs,
            'change_check' => 0
                ), $helper->fields_value);

        return $helper->generateForm($fields_form).Context::getContext()->smarty->createTemplate($this->module->getLocalPath().'views/templates/admin/mass_update_products/fields/change_description.tpl')->fetch();;
    }

}
