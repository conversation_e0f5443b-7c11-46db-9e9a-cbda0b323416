<?php
/**
 * Known Bad Bots List
 * 
 * This file contains a list of known malicious or unwanted bots
 * that should be blocked by the BlockBot module.
 * 
 * <AUTHOR> Name
 * @copyright 2025
 * @license   Commercial
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

return [
    // Malicious crawlers and scrapers
    'MJ12bot/v1.4.8',
    '^SemrushBot',
    'Twiceler',
    'Mail.ru',
    'VoilaBot BETA 1.2',
    'libwww-perl/5.805',
    'Java/1.5.0_11',
    'Sogou web spider/3.0',
    'psbot',
    'Exabot',
    'Charlotte/1.0b',
    'boitho.com-dc',
    'ajSitemap',
    'bot/1.0',
    'panscient.com',
    'Java/1.6.0_11',
    'WebDataCentreBot/1.0',
    'Java',
    'SapphireWebCrawler',
    'Yandex',
    'Baiduspider',
    'Rankivabot',
    'DBLBot/1.0',
    'Black Hole',
    'Titan',
    'WebStripper',
    'NetMechanic',
    'CherryPicker',
    'EmailCollector',
    'EmailSiphon',
    'WebBandit',
    'EmailWolf',
    'ExtractorPro',
    'CopyRightCheck',
    'Crescent',
    'Wget',
    'SiteSnagger',
    'ProWebWalker',
    'CheeseBot',
    'Teleport',
    'TeleportPro',
    'MIIxpc',
    'Telesoft',
    'Website Quester',
    'WebZip',
    'moget/2.1',
    'WebZip/4.0',
    'WebSauger',
    'WebCopier',
    'NetAnts',
    'Mister PiX',
    'WebAuto',
    'TheNomad',
    'WWW-Collector-E',
    'RMA',
    'libWeb/clsHTTP',
    'asterias',
    'httplib',
    'turingos',
    'spanner',
    'InfoNaviRobot',
    'Harvest/1.5',
    'Bullseye/1.0',
    'Mozilla/4.0 (compatible; BullsEye; Windows 95)',
    'Crescent Internet ToolPak HTTP OLE Control v.1.0',
    'CherryPickerSE/1.0',
    'CherryPicker /1.0',
    'WebBandit/3.50',
    'NICErsPRO',
    'Microsoft URL Control - 5.01.4511',
    'DittoSpyder',
    'Foobot',
    'WebmasterWorldForumBot',
    'SpankBot',
    'BotALot',
    'lwp-trivial/1.34',
    'lwp-trivial',
    'Wget/1.6',
    'BunnySlippers',
    'Microsoft URL Control - 6.00.8169',
    'URLy Warning',
    'Wget/1.5.3',
    'LinkWalker',
    'cosmos',
    'moget',
    'hloader',
    'humanlinks',
    'LinkextractorPro',
    'Offline Explorer',
    'Mata Hari',
    'LexiBot',
    'Web Image Collector',
    'The Intraformant',
    'True_Robot/1.0',
    'True_Robot',
    'BlowFish/1.0',
    'JennyBot',
    'MIIxpc/4.2',
    'BuiltBotTough',
    'ProPowerBot/2.14',
    'BackDoorBot/1.0',
    'toCrawl/UrlDispatcher',
    'WebEnhancer',
    'TightTwatBot',
    'suzuran',
    'VCI WebViewer VCI WebViewer Win32',
    'VCI',
    'Szukacz/1.4',
    'QueryN Metasearch',
    'Openfind data gathere',
    'Openfind',
    'Xenu\'s Link Sleuth 1.1c',
    'Xenu\'s',
    'Zeus',
    'RepoMonkey Bait & Tackle/v1.01',
    'RepoMonkey',
    'Zeus 32297 Webster Pro V2.9 Win32',
    'Webster Pro',
    'EroCrawler',
    'LinkScan/8.1a Unix',
    'Keyword Density/0.9',
    'Kenjin Spider',
    'Cegbfeieh',
    '360Spider',
    'BUbiNG',
    'YandexBot',
    'CCBot',
    '2ip bot',
    'GPTBot',
    'nekst',
    
    // Additional known bad bots
    'AhrefsBot',
    'MegaIndex.ru',
    'DotBot',
    'SeznamBot',
    'PetalBot',
    'DataForSeoBot',
    'serpstatbot',
    'MJ12bot',
    'DomainStatsBot',
    'LinkpadBot',
    'Go-http-client',
    'python-requests',
    'curl',
    'libcurl',
    'scrapy',
    'HeadlessChrome',
    'PhantomJS',
    'SiteAuditBot',
    'Screaming Frog',
    'Nutch',
    'Apache-HttpClient',
    'okhttp',
    'Riddler',
    'ZoominfoBot',
    'Clickagy',
    'MauiBot',
    'SurdotlyBot',
    'Cliqzbot',
    
    // Aggressive scrapers
    'SemrushBot',
    'AhrefsBot',
    'MJ12bot',
    'DotBot',
    'BLEXBot',
    'DataForSeoBot',
    'serpstatbot',
    'LinkpadBot',
    'MegaIndex',
    'SiteAuditBot',
    'Screaming Frog SEO Spider',
    'Netcraft Web Server Survey',
    'Wappalyzer',
    'BuiltWith',
    'WhatCMS',
    'CMSmap',
    'Nikto',
    'sqlmap',
    'w3af',
    'OWASP ZAP',
    'Burp Suite',
    'Acunetix',
    'Nessus',
    'OpenVAS',
    'Qualys',
    'Rapid7',
    'Tenable',
    'Veracode',
    'WhiteHat Security',
    'IBM Security AppScan',
    'HP WebInspect',
    'Checkmarx',
    'Fortify',
    'SonarQube',
    'RIPS',
    'Bandit',
    'Brakeman',
    'ESLint',
    'JSHint',
    'TSLint',
    'Pylint',
    'Flake8',
    'mypy',
    'black',
    'isort',
    'autopep8',
    'yapf',
    'bandit',
    'safety',
    'pipenv check',
    'npm audit',
    'yarn audit',
    'bundle audit',
    'composer audit',
    'cargo audit',
    'go mod audit',
    'dotnet list package --vulnerable',
    'nuget audit',
    'pip-audit',
    'safety check',
    'snyk test',
    'OWASP Dependency Check',
    'RetireJS',
    'NSP',
    'Node Security Platform',
    'Gemnasium',
    'VersionEye',
    'Libraries.io',
    'David-dm',
    'Greenkeeper',
    'Renovate',
    'Dependabot',
    'WhiteSource',
    'Black Duck',
    'FOSSA',
    'Sonatype Nexus',
    'JFrog Xray',
    'Twistlock',
    'Aqua Security',
    'Sysdig',
    'Falco',
    'Cilium',
    'Istio',
    'Linkerd',
    'Consul Connect',
    'Vault',
    'Terraform',
    'Ansible',
    'Chef',
    'Puppet',
    'SaltStack',
    'Kubernetes',
    'Docker',
    'Podman',
    'containerd',
    'CRI-O',
    'rkt',
    'LXC',
    'LXD',
    'systemd-nspawn',
    'OpenVZ',
    'Proxmox',
    'VMware',
    'VirtualBox',
    'QEMU',
    'KVM',
    'Xen',
    'Hyper-V',
    'Parallels',
    'Vagrant',
    'Packer',
    'Nomad',
    'Consul',
    'Vault',
    'Boundary',
    'Waypoint'
];
