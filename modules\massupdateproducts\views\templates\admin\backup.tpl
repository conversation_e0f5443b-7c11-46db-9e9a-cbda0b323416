<div class="panel">
    <div class="panel-heading">
        Lista kopii zapasowych
    </div>
    <div class="row">
        <form method="post">
            <button class="pull-right btn btn-primary" type="submit" name="submitBackUp"><i class="material-icons">save</i> <span style="top: -7px;position: relative;">Wykonaj kopię zapasową</span></button>
        </form>
        {if empty($bqpList)}
            <br />
            <br />
            <br />
            <br />
            <p class="alert alert-warning">Brak kopii zapasowych</p>
        {else}
            <form method="post">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Lp.</th>
                            <th>Data kopii zapasowej</th>
                            <th>Wiek</th>
                            <th>Nazwa pliku</th>
                            <th>Rozmiar pliku</th>
                            <th>Ostatnia aktualizacja</th>
                            <th>Przywrócono?</th>
                            <th>Ak<PERSON><PERSON></th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $bqpList as $b}
                            <tr>
                                <td>{$b@iteration}.</td>
                                <td>{$b.date_add}</td>
                                <td>{$b.diff}</td>
                                <td>{$b.file}</td>
                                <td>{$b.size} MB</td>
                                <td>{$b.date_upd}</td>
                                <td>
                                    {if empty($b.date_restore)}
                                        --
                                    {else}
                                        {$b.date_restore}
                                    {/if}
                                </td>
                                <td>
                                    <button type="submit" name="submitResetBase" value="{$b.id_backup}" class="btn btn-danger" onclick="return confirm('Czy na pewno chcesz przywrócić tę kopię?');"><i class="material-icons">refresh</i> <span style="top: -7px;position:relative;">Przywroć kopię bazy</span></button>
                                    <button type="submit" name="SubmitDeleteBackUp" value="{$b.id_backup}" class="btn btn-warning" onclick="return confirm('Czy na pewno chcesz usunąć tę kopię?');"><i class="material-icons">delete</i> <span style="top: -7px;position:relative;">Usuń kopię</span></button>
                                </td>
                            </tr>
                        {/foreach}
                    </tbody>
                </table>
            </form>
        {/if}
    </div>
</div>
