<?php

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/Inspiration.php';
require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/InspirationCategory.php';

class AdminInspirationController extends ModuleAdminController
{
    public function __construct()
    {
        $this->className = 'Inspiration';
        $this->table = 'inspiration';
        $this->identifier = 'id_inspiration';
        $this->lang = true;
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->module = Module::getInstanceByName('webixa_preview');

        parent::__construct();

        $this->fields_list = array(
            'id_inspiration' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'width' => 25
            ),
            'title' => array(
                'title' => $this->l('Title'),
                'width' => 'auto',
                'filter_key' => 'b!title'
            ),
            'display_on_homepage' => array(
                'title' => $this->l('Show on Homepage'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'orderby' => false,
                'width' => 25
            ),

            'active' => array(
                'title' => $this->l('Active'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'orderby' => false,
                'width' => 25
            ),
            'date_add' => array(
                'title' => $this->l('Date Add'),
                'width' => 100,
                'type' => 'date',
                'align' => 'right'
            )
        );

        $this->bulk_actions = array(
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?')
            ),
            'enableSelection' => array('text' => $this->l('Enable selection')),
            'disableSelection' => array('text' => $this->l('Disable selection'))
        );


         if (Shop::isFeatureActive()) {
             $this->context->smarty->assign('has_shop_restriction', true);
         }
    }

    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);


        $this->addJqueryUI('ui.autocomplete');



        $this->addCSS([
            _MODULE_DIR_ . 'webixa_preview/views/css/admin/inspiration.css',
        ]);
    }

    public function renderForm()
    {
        $id_inspiration = (int)Tools::getValue('id_inspiration');

        if ($id_inspiration) {
            $inspiration = new Inspiration($id_inspiration, $this->context->language->id);
            if (Validate::isLoadedObject($inspiration)) {
            } else {
            }
        }


        Media::addJsDef([
            'inspiration_ajax_url' => $this->context->link->getAdminLink('AdminInspiration'),
            'inspiration_img_dir' => $this->module->getPathUri() . 'views/img/',
            'inspiration_id' => $id_inspiration,
        ]);

        $this->fields_form = array(
            'legend' => array(
                'title' => $this->l('Inspiration Information'),
                'icon' => 'icon-list-alt'
            ),
            'input' => array(
                array(
                    'type' => 'text',
                    'label' => $this->l('Title'),
                    'name' => 'title',
                    'lang' => true,
                    'required' => true,
                    'hint' => $this->l('Invalid characters:').' <>;=#{}',
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Short Description'),
                    'name' => 'short_description',
                    'lang' => true,
                    'hint' => $this->l('Short description for homepage display'),
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'lang' => true,
                    'autoload_rte' => true,
                    'hint' => $this->l('Invalid characters:').' <>;=#{}'
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->l('Show on Homepage'),
                    'name' => 'display_on_homepage',
                    'required' => false,
                    'class' => 't',
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        )
                    ),
                ),
                array(
                    'type' => 'color',
                    'label' => $this->l('Background Color (RGB)'),
                    'name' => 'rgb_color',
                    'hint' => $this->l('Choose a background color for homepage display'),
                ),
                array(
                    'type' => 'free',
                    'label' => $this->l('Images'),
                    'name' => 'inspiration_images_container',
                    'desc' => $this->l('Drag and drop images or click to select files.'),
                ),
                array(
                    'type' => 'free',
                    'label' => $this->l('Categories'),
                    'name' => 'inspiration_categories',
                    'desc' => $this->l('Select the categories for this inspiration'),
                ),



                array(
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'class' => 't',
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        )
                    ),
                ),
            ),
            'submit' => array(
                'title' => $this->l('Save'),
            ),
        );


        $inspirationCategories = $this->getInspirationCategories();
        $this->context->smarty->assign('inspiration_categories', $inspirationCategories);


        $selectedCategories = [];
        $id_inspiration = (int)Tools::getValue('id_inspiration');
        if ($id_inspiration) {


            $inspiration = new Inspiration($id_inspiration);
            if (Validate::isLoadedObject($inspiration)) {
                $categories = $inspiration->getCategories($this->context->language->id);
                foreach ($categories as $category) {
                    $selectedCategories[] = $category['id_inspiration_category'];
                }

            }
        }
        $this->context->smarty->assign('selected_categories', $selectedCategories);


        $this->context->smarty->assign('id_inspiration', (int)Tools::getValue('id_inspiration'));
        $this->context->smarty->assign('admin_products_link', $this->context->link->getAdminLink('AdminProducts'));
        $this->context->smarty->assign('id_lang', (int)$this->context->language->id);


        $products = [];
        $id_inspiration = (int)Tools::getValue('id_inspiration');
        if ($id_inspiration) {


            $products = $this->getInspirationProducts($id_inspiration);
        }
        $this->context->smarty->assign('inspiration_products', $products);


        $images = [];
        $id_inspiration = (int)Tools::getValue('id_inspiration');
        if ($id_inspiration) {


            $images = $this->getInspirationImages($id_inspiration);


        }
        $this->context->smarty->assign('inspiration_images', $images);


        $main_image_id = 0;
        if (!empty($images)) {
            foreach ($images as &$image) {
                if ($image['main']) {
                    $main_image_id = $image['id_inspiration_image'];
                }


                $image['products'] = $this->getProductsForImage($image['id_inspiration_image']);
            }
        }
        $this->context->smarty->assign('main_image_id', $main_image_id);
        $this->context->smarty->assign('id_lang', $this->context->language->id);


        $id_inspiration = (int)Tools::getValue('id_inspiration');



        $this->context->smarty->assign('id_inspiration', $id_inspiration);







        $this->fields_value['inspiration_categories'] = $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/category_tree.tpl');
        $this->fields_value['inspiration_images_container'] = $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/inspiration_images.tpl');

        return parent::renderForm();
    }

    protected function getSelectedCategoryIds()
    {
        $inspiration = new Inspiration((int)Tools::getValue('id_inspiration'));
        $categories = $inspiration->getCategories($this->context->language->id);
        $ids = array();
        foreach ($categories as $category) {
            $ids[] = $category['id_inspiration_category'];
        }
        return $ids;
    }

    protected function getInspirationCategories()
    {
        $sql = 'SELECT ic.*, icl.name, icl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'inspiration_category` ic
                LEFT JOIN `' . _DB_PREFIX_ . 'inspiration_category_lang` icl
                    ON (ic.id_inspiration_category = icl.id_inspiration_category AND icl.id_lang = ' . (int)$this->context->language->id . ')
                ORDER BY ic.position ASC';

        $categories = Db::getInstance()->executeS($sql);

        // Organize categories into a tree structure
        $categoriesById = [];
        $rootCategories = [];

        if ($categories) {
            // First pass: index by ID
            foreach ($categories as $category) {
                $categoriesById[$category['id_inspiration_category']] = $category;
                $categoriesById[$category['id_inspiration_category']]['children'] = [];
            }

            // Second pass: build tree
            foreach ($categories as $category) {
                if ($category['id_parent'] == 0) {
                    $rootCategories[] = &$categoriesById[$category['id_inspiration_category']];
                } else {
                    if (isset($categoriesById[$category['id_parent']])) {
                        $categoriesById[$category['id_parent']]['children'][] = &$categoriesById[$category['id_inspiration_category']];
                    }
                }
            }
        }

        return $rootCategories;
    }

    protected function getImage($obj)
    {
        if (Validate::isLoadedObject($obj)) {
            $sql = 'SELECT `image` FROM `' . _DB_PREFIX_ . 'inspiration_image` WHERE `id_inspiration` = ' . (int)$obj->id . ' AND `main` = 1';
            $image = Db::getInstance()->getValue($sql);
            if ($image) {
                $imagePath = _MODULE_DIR_ . 'webixa_preview/views/img/' . $image;
                if (file_exists($imagePath)) {
                    $imageLink = $this->context->link->getMediaLink($this->module->getPathUri() . 'views/img/' . $image);
                    return '<img src="' . $imageLink . '" alt="' . $obj->title . '" class="imgm img-thumbnail" />';
                }
            }
        }
        return '';
    }

    public function renderList()
    {
        return parent::renderList();
    }

    public function renderView()
    {
        $id_inspiration = (int)Tools::getValue('id_inspiration');
        $inspiration = new Inspiration($id_inspiration, $this->context->language->id);

        if (!Validate::isLoadedObject($inspiration)) {
            $this->errors[] = $this->l('The inspiration does not exist.');
            return parent::renderView();
        }

        // Get all images for this inspiration
        $images = $this->getInspirationImages($id_inspiration);

        // Get all products for this inspiration
        $products = $this->getInspirationProducts($id_inspiration);

        // Get categories for this inspiration
        $categories = $inspiration->getCategories($this->context->language->id);

        $this->context->smarty->assign(array(
            'inspiration' => $inspiration,
            'images' => $images,
            'products' => $products,
            'categories' => $categories,
        ));

        return parent::renderView();
    }

    protected function getInspirationImages($id_inspiration)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'inspiration_image` WHERE `id_inspiration` = ' . (int)$id_inspiration . ' ORDER BY `position` ASC';
        $images = Db::getInstance()->executeS($sql);

        if ($images) {
            foreach ($images as &$image) {
                $image['url'] = $this->context->link->getMediaLink($this->module->getPathUri() . 'views/img/' . $image['image']);

                // Get products for this image
                $image['products'] = $this->getProductsForImage($image['id_inspiration_image']);
            }
        }

        return $images;
    }

    protected function getProductsForImage($id_inspiration_image)
    {
        $sql = 'SELECT iip.*, pl.name
                FROM `' . _DB_PREFIX_ . 'inspiration_image_product` iip
                LEFT JOIN `' . _DB_PREFIX_ . 'product` p ON (iip.id_product = p.id_product)
                LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE iip.`id_inspiration_image` = ' . (int)$id_inspiration_image;

        $products = Db::getInstance()->executeS($sql);
        return $products;
    }

    protected function getInspirationProducts($id_inspiration)
    {
        $sql = 'SELECT ip.*, pl.name, i.id_image, i.id_product as image_product_id, iip.id_inspiration_image_assoc as id_inspiration_image
                FROM `' . _DB_PREFIX_ . 'inspiration_product` ip
                LEFT JOIN `' . _DB_PREFIX_ . 'product` p ON (ip.id_product = p.id_product)
                LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                LEFT JOIN `' . _DB_PREFIX_ . 'image` i ON (p.id_product = i.id_product AND i.cover = 1)
                LEFT JOIN (
                    SELECT iip.id_product, iip.id_inspiration_image as id_inspiration_image_assoc
                    FROM `' . _DB_PREFIX_ . 'inspiration_image_product` iip
                    INNER JOIN `' . _DB_PREFIX_ . 'inspiration_image` ii ON iip.id_inspiration_image = ii.id_inspiration_image
                    WHERE ii.id_inspiration = ' . (int)$id_inspiration . '
                ) iip ON ip.id_product = iip.id_product
                WHERE ip.`id_inspiration` = ' . (int)$id_inspiration;                      

        $products = Db::getInstance()->executeS($sql);

        if ($products) {
            foreach ($products as &$product) {
                if (!empty($product['id_image'])) {
                    $product['image_url'] = $this->context->link->getImageLink($product['id_product'], $product['id_image'], 'home_default');
                }
            }
        }

        return $products;
    }

    public function ajaxProcessGetProducts()
    {
        $query = Tools::getValue('q', '');
        $limit = 20;

        $sql = 'SELECT p.id_product, pl.name
                FROM `' . _DB_PREFIX_ . 'product` p
                LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE pl.name LIKE \'%' . pSQL($query) . '%\'
                LIMIT ' . (int)$limit;

        $products = Db::getInstance()->executeS($sql);

        die(json_encode($products));
    }

    public function ajaxProcessSearchProducts()
    {
        $query = Tools::getValue('q', '');
        $excludeIds = Tools::getValue('excludeIds', '');
        $limit = 20;

        $excludeProducts = array();
        if ($excludeIds) {
            $excludeProducts = explode(',', $excludeIds);
        }

        $sql = new DbQuery();
        $sql->select('p.id_product, pl.name, p.reference');
        $sql->from('product', 'p');
        $sql->leftJoin('product_lang', 'pl', 'p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id);
        $sql->where('pl.name LIKE \'%' . pSQL($query) . '%\' OR p.reference LIKE \'%' . pSQL($query) . '%\'');

        if (!empty($excludeProducts)) {
            $sql->where('p.id_product NOT IN (' . implode(',', array_map('intval', $excludeProducts)) . ')');
        }

        // Fix: Use proper limit syntax
        $sql->limit((int)$limit);

        $results = Db::getInstance()->executeS($sql);
        $products = array();

        if ($results) {
            foreach ($results as $product) {
                $products[] = array(
                    'id_product' => $product['id_product'],
                    'name' => $product['name'] . ' (REF: ' . $product['reference'] . ')'
                );
            }
        }

        die(json_encode($products));
    }

    public function ajaxProcessDeleteImage()
    {
        $id_inspiration_image = (int)Tools::getValue('id_inspiration_image');

        $sql = 'SELECT `image` FROM `' . _DB_PREFIX_ . 'inspiration_image` WHERE `id_inspiration_image` = ' . (int)$id_inspiration_image;
        $image = Db::getInstance()->getValue($sql);

        if ($image) {
            $imagePath = _PS_MODULE_DIR_ . 'webixa_preview/views/img/' . $image;
            if (file_exists($imagePath)) {
                @unlink($imagePath);
            }

            Db::getInstance()->delete('inspiration_image', 'id_inspiration_image = ' . (int)$id_inspiration_image);
            Db::getInstance()->delete('inspiration_image_product', 'id_inspiration_image = ' . (int)$id_inspiration_image);

            die(json_encode(array('success' => true)));
        }

        die(json_encode(array('success' => false)));
    }

    public function ajaxProcessUploadImage()
    {
        $id_inspiration = (int)Tools::getValue('id_inspiration');
        if (!$id_inspiration) {
            $error_message = $this->l('Invalid inspiration ID');
            die(json_encode(array('success' => false, 'message' => $error_message)));
        }
		
        if (!isset($_FILES['images']) || empty($_FILES['images']['name'][0])) {
            die(json_encode(array('success' => false, 'message' => $this->l('No files uploaded'))));
        }

        // Make sure the upload directory exists
        $uploadDir = _PS_MODULE_DIR_ . 'webixa_preview/views/img/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $uploadedImages = [];
        $errors = [];

        // Handle multiple files
        $fileCount = count($_FILES['images']['name']);
        for ($i = 0; $i < $fileCount; $i++) {
            if (!empty($_FILES['images']['name'][$i])) {
                $file = [
                    'name' => $_FILES['images']['name'][$i],
                    'type' => $_FILES['images']['type'][$i],
                    'tmp_name' => $_FILES['images']['tmp_name'][$i],
                    'error' => $_FILES['images']['error'][$i],
                    'size' => $_FILES['images']['size'][$i]
                ];

                $allowedExtensions = array('jpg', 'jpeg', 'png', 'gif', 'webp');
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                if (!in_array($extension, $allowedExtensions)) {
                    $errors[] = $this->l('Invalid image format for file') . ' ' . $file['name'] . '. ' . $this->l('Allowed formats: jpg, jpeg, png, gif, webp');
                    continue;
                }

                $imageName = uniqid('inspiration_') . '.' . $extension;
                $destination = $uploadDir . $imageName;

                if (!move_uploaded_file($file['tmp_name'], $destination)) {
                    $errors[] = $this->l('Failed to upload file') . ' ' . $file['name'];
                    continue;
                }

                // Get highest position
                $sql = 'SELECT MAX(`position`) FROM `' . _DB_PREFIX_ . 'inspiration_image` WHERE `id_inspiration` = ' . (int)$id_inspiration;
                $position = (int)Db::getInstance()->getValue($sql) + 1;

                // Save image to database
                $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'inspiration_image` (`id_inspiration`, `image`, `position`, `main`)
                        VALUES (' . (int)$id_inspiration . ', \'' . pSQL($imageName) . '\', ' . (int)$position . ', 0)';
                $result = Db::getInstance()->execute($sql);

                if ($result) {
                    $id_inspiration_image = (int)Db::getInstance()->Insert_ID();
                    $uploadedImages[] = [
                        'id_inspiration_image' => $id_inspiration_image,
                        'image' => $imageName,
                        'position' => $position,
                        'main' => 0
                    ];

                    // If this is the first image, set it as main
                    $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'inspiration_image` WHERE `id_inspiration` = ' . (int)$id_inspiration;
                    $count = (int)Db::getInstance()->getValue($sql);

                    if ($count === 1) {
                        $sql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration_image` SET `main` = 1 WHERE `id_inspiration_image` = ' . (int)$id_inspiration_image;
                        Db::getInstance()->execute($sql);
                        $uploadedImages[count($uploadedImages) - 1]['main'] = 1;
                    }
                } else {
                    $errors[] = $this->l('Failed to save image to database') . ' ' . $file['name'];
                    // Remove the uploaded file if we couldn't save to database
                    @unlink($destination);
                }
            }
        }

        $response = [
            'success' => count($uploadedImages) > 0,
            'uploaded_images' => $uploadedImages,
            'errors' => $errors
        ];

        if (count($errors) > 0) {
            $response['message'] = implode('<br>', $errors);
        }

        die(json_encode($response));
    }

    public function ajaxProcessGetImages()
    {        
        $id_inspiration = (int)Tools::getValue('id_inspiration');
        if (!$id_inspiration) {
            $error_message = $this->l('Invalid inspiration ID');
            die(json_encode(array('success' => false, 'message' => $error_message)));
        }

        $images = $this->getInspirationImages($id_inspiration);

        // Generate HTML for the images list
        $this->context->smarty->assign('inspiration_images', $images);
        $html = $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/image_list.tpl');

        die(json_encode(array(
            'success' => true,
            'images' => $images,
            'html' => $html
        )));
    }

    public function processSave()
    {
        // Validate required fields
        $errors = [];

        // Check if title is provided for default language
        $defaultLangId = (int)Configuration::get('PS_LANG_DEFAULT');
        if (empty(Tools::getValue('title_' . $defaultLangId))) {
            $errors[] = $this->l('Title is required for default language');
        }

        // If there are errors, display them and return
        if (!empty($errors)) {
            foreach ($errors as $error) {
                $this->errors[] = $error;
            }
            return false;
        }

        // Generate link_rewrite if empty
        foreach (Language::getLanguages(true) as $lang) {
            $id_lang = $lang['id_lang'];
            if (empty(Tools::getValue('link_rewrite_' . $id_lang))) {
                $_POST['link_rewrite_' . $id_lang] = Tools::link_rewrite(Tools::getValue('title_' . $id_lang));
            }
        }

        // Add id_shop
        $_POST['id_shop'] = Context::getContext()->shop->id;

        // If editing an existing inspiration, handle it manually to avoid duplicate key errors
        if ($id_inspiration = (int)Tools::getValue('id_inspiration')) {
            // Get the existing inspiration
            $inspiration = new Inspiration($id_inspiration);
            if (Validate::isLoadedObject($inspiration)) {
                // Update basic fields
                $inspiration->active = (int)Tools::getValue('active');
                $inspiration->position = (int)Tools::getValue('position');
                $inspiration->date_upd = date('Y-m-d H:i:s');
                $inspiration->rgb_color = Tools::getValue('rgb_color');
                $inspiration->display_on_homepage = (int)Tools::getValue('display_on_homepage');
                $inspiration->id_shop = (int)Context::getContext()->shop->id;

                // Update multilang fields
                foreach (Language::getLanguages(true) as $lang) {
                    $id_lang = $lang['id_lang'];
                    $inspiration->title[$id_lang] = Tools::getValue('title_' . $id_lang);
                    $inspiration->link_rewrite[$id_lang] = Tools::getValue('link_rewrite_' . $id_lang);
                    $inspiration->description[$id_lang] = Tools::getValue('description_' . $id_lang);
                    $inspiration->short_description[$id_lang] = Tools::getValue('short_description_' . $id_lang);
                }                
                $return = $inspiration->update();
            } else {
                $this->errors[] = $this->l('Cannot load inspiration for editing');
                return false;
            }
        } else {            
            $return = parent::processSave();
            $id_inspiration = (int)$this->object->id;
        }

        if ($return) {
            $id_inspiration = (int)Tools::getValue('id_inspiration');
            if (!$id_inspiration) {
                $id_inspiration = (int)$this->object->id;
            }

            $inspiration = new Inspiration($id_inspiration);
            if (Validate::isLoadedObject($inspiration)) {
                // Handle category associations
                $category_ids = Tools::getValue('categoryBox');
                if (is_array($category_ids)) {
                    // Clear existing category associations
                    Db::getInstance()->delete('inspiration_to_category', 'id_inspiration = ' . (int)$id_inspiration);

                    // Add new category associations
                    foreach ($category_ids as $id_category) {
                        Db::getInstance()->insert('inspiration_to_category', [
                            'id_inspiration' => (int)$id_inspiration,
                            'id_inspiration_category' => (int)$id_category
                        ]);
                    }
                }
                
                $mainImageId = (int)Tools::getValue('main_image_id');
                if ($mainImageId) {
                    // Check if the image exists and belongs to this inspiration
                    $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'inspiration_image`
                            WHERE `id_inspiration_image` = ' . (int)$mainImageId . '
                            AND `id_inspiration` = ' . (int)$id_inspiration;
                    $imageExists = (int)Db::getInstance()->getValue($sql);

                    if ($imageExists) {
                        // Reset all images to not main
                        $sql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration_image` SET `main` = 0 WHERE `id_inspiration` = ' . (int)$id_inspiration;
                        $result1 = Db::getInstance()->execute($sql);

                        // Set the selected image as main
                        $sql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration_image` SET `main` = 1 WHERE `id_inspiration_image` = ' . (int)$mainImageId;
                        $result2 = Db::getInstance()->execute($sql);
                    }
                } else {
                    // If no main image is selected but we have images, set the first one as main
                    $sql = 'SELECT id_inspiration_image FROM `' . _DB_PREFIX_ . 'inspiration_image`
                            WHERE `id_inspiration` = ' . (int)$id_inspiration . '
                            ORDER BY position ASC LIMIT 1';
                    $firstImageId = (int)Db::getInstance()->getValue($sql);

                    if ($firstImageId) {
                        $sql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration_image` SET `main` = 0 WHERE `id_inspiration` = ' . (int)$id_inspiration;
                        Db::getInstance()->execute($sql);

                        $sql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration_image` SET `main` = 1 WHERE `id_inspiration_image` = ' . (int)$firstImageId;
                        Db::getInstance()->execute($sql);
                    }
                }

                // Handle product associations from JSON
                $productAssociationsJson = Tools::getValue('product_associations');
                if ($productAssociationsJson) {
                    try {
                        $productAssociations = json_decode($productAssociationsJson, true);
                        if (is_array($productAssociations)) {
                            // Clear existing product associations
                            $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'inspiration_product` WHERE `id_inspiration` = ' . (int)$id_inspiration;
                            Db::getInstance()->execute($sql);

                            // Clear existing image-product associations for this inspiration
                            $sql = 'DELETE ip.* FROM `' . _DB_PREFIX_ . 'inspiration_image_product` ip
                                    INNER JOIN `' . _DB_PREFIX_ . 'inspiration_image` ii ON ip.id_inspiration_image = ii.id_inspiration_image
                                    WHERE ii.id_inspiration = ' . (int)$id_inspiration;
                            Db::getInstance()->execute($sql);

                            // Process each image's products
                            foreach ($productAssociations as $id_inspiration_image => $products) {
                                if (is_array($products)) {
                                    foreach ($products as $product) {
                                        if (isset($product['id_product'])) {
                                            $id_product = (int)$product['id_product'];
                                            $position_x = isset($product['position_x']) ? (int)$product['position_x'] : 50;
                                            $position_y = isset($product['position_y']) ? (int)$product['position_y'] : 50;

                                            // Add to inspiration_product table
                                            $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'inspiration_product`
                                                    (`id_inspiration`, `id_product`, `position_x`, `position_y`)
                                                    VALUES (' . (int)$id_inspiration . ', ' . (int)$id_product . ', ' . $position_x . ', ' . $position_y . ')';
                                            Db::getInstance()->execute($sql);

                                            // Add to inspiration_image_product table
                                            $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'inspiration_image_product`
                                                    (`id_inspiration_image`, `id_product`, `position_x`, `position_y`)
                                                    VALUES (' . (int)$id_inspiration_image . ', ' . (int)$id_product . ', ' . $position_x . ', ' . $position_y . ')';
                                            Db::getInstance()->execute($sql);
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception $e) {
                    }
                }

                // For backward compatibility, also handle the old format
                $associatedProducts = Tools::getValue('associated_products');
                if (is_array($associatedProducts)) {
                    foreach ($associatedProducts as $id_product => $data) {
                        $position_x = isset($data['x']) ? (int)$data['x'] : 50;
                        $position_y = isset($data['y']) ? (int)$data['y'] : 50;
                        $id_inspiration_image = isset($data['image_id']) ? (int)$data['image_id'] : 0;

                        // Check if this product is already added through the new method
                        $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'inspiration_product`
                                WHERE `id_inspiration` = ' . (int)$id_inspiration . '
                                AND `id_product` = ' . (int)$id_product;
                        $exists = (int)Db::getInstance()->getValue($sql);

                        if (!$exists) {
                            // Add to inspiration_product table
                            $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'inspiration_product`
                                    (`id_inspiration`, `id_product`, `position_x`, `position_y`)
                                    VALUES (' . (int)$id_inspiration . ', ' . (int)$id_product . ', ' . $position_x . ', ' . $position_y . ')';
                            Db::getInstance()->execute($sql);

                            // If an image is selected, add to inspiration_image_product table
                            if ($id_inspiration_image > 0) {
                                $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'inspiration_image_product`
                                        (`id_inspiration_image`, `id_product`, `position_x`, `position_y`)
                                        VALUES (' . (int)$id_inspiration_image . ', ' . (int)$id_product . ', ' . $position_x . ', ' . $position_y . ')';
                                Db::getInstance()->execute($sql);
                            }
                        }
                    }
                }


            }
        }

        return $return;
    }

}