<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

$autoloadPath = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoloadPath)) {
    require_once $autoloadPath;
}

use Webixa\Content\Module\WebixaConfig;
use Webixa\Content\Module\WidgetInterface;
use Webixa\Content\Search\CategoryProductSearchProvider;

use PrestaShop\PrestaShop\Adapter\Image\ImageRetriever;
use PrestaShop\PrestaShop\Adapter\Product\PriceFormatter;
use PrestaShop\PrestaShop\Adapter\Product\ProductColorsRetriever;
use PrestaShop\PrestaShop\Core\Product\Search\ProductSearchContext;
use PrestaShop\PrestaShop\Core\Product\Search\ProductSearchQuery;

class Webixa_Content extends Module implements WidgetInterface
{
    public $version;
    public $secure_key;
    public $modernBackOfficeLayout = false;
    public $availableTemplates = [
        [
            'id' => 'simple',
            'name' => 'Simple'
        ]
    ];

    public static $debug = false;

    public function __construct()
    {
        $this->name = 'webixa_content';
        $this->tab = 'front_office_features';
        $this->version = '1.0.7';
        $this->author = 'Webixa sp. z o.o.';
        $this->need_instance = 0;
        $this->secure_key = Tools::encrypt($this->name);
        $this->ps_versions_compliancy = [
            'min' => '1.7',
            'max' => '8.99.99',
        ];

        $this->ps_version = substr(_PS_VERSION_, 0, 3);
        /**
         * Set $this->bootstrap to true if your module is compliant with bootstrap (PrestaShop 1.6)
         */
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Webixa content');
        $this->description = $this->l('Allows to configure Blocks with many content types for example Banners, Links, Slides etc.');

        if (version_compare(_PS_VERSION_, '1.7.6', '>=')) {
            $this->modernBackOfficeLayout = true;
        }

        Shop::addTableAssociation(WebixaContentBlock::$definition['table'], ['type' => 'shop']);
        Shop::addTableAssociation(WebixaContentItem::$definition['table'], ['type' => 'shop']);
        Shop::addTableAssociation(WebixaContentSlide::$definition['table'], ['type' => 'shop']);
        Shop::addTableAssociation(WebixaContentLink::$definition['table'], ['type' => 'shop']);
        Shop::addTableAssociation(WebixaContentCategoryProducts::$definition['table'], ['type' => 'shop']);
        Shop::addTableAssociation(WebixaContentBanner::$definition['table'], ['type' => 'shop']);

        if (file_exists(_PS_MODULE_DIR_ . $this->name . '/dev')) {
            self::$debug = true;
        }
    }

    public function install()
    {
        if (Shop::isFeatureActive()) {
            Shop::setContext(Shop::CONTEXT_ALL);
        }

        $return = parent::install() &&
            (include dirname(__FILE__) . '/sql/install.php') &&
            $this->registerHook('displayHeader') &&
            $this->registerHook('displayWebixaContent') &&
            $this->registerHook('displayBackOfficeHeader') &&
            $this->registerHook('actionObjectWebixaContentHookAddAfter') &&
            $this->registerHook('actionObjectWebixaContentHookUpdateAfter') &&
            $this->registerHook('actionObjectWebixaContentHookDeleteAfter') &&
            $this->installAdminTabs();

        return $return;
    }

    public function uninstall()
    {
        $return = parent::uninstall() &&
            (include dirname(__FILE__) . '/sql/uninstall.php') &&
            $this->uninstallAdminTabs();

        return $return;
    }


    public function installAdminTabs()
    {
        $parentId = Tab::getIdFromClassName('AdminParentWebixa');
        if (!$parentId) {
            $parentTab = new Tab();
            $parentTab->id_parent = 1;
            foreach (Language::getLanguages(false) as $lang) {
                $parentTab->name[$lang['id_lang']] = 'Webixa';
            }
            $parentTab->class_name = 'AdminParentWebixa';
            $parentTab->save();
            $parentId = $parentTab->id;
        }

        $tabsToInstall = [
            'AdminWebixaContentBlock' => [
                'title' => [
                    'pl' => 'Treści - Bloki',
                    'en' => 'Content Blocks',
                ],
                'id_parent' => $parentId,
            ],
            'AdminWebixaContentHook' => [
                'title' => [
                    'pl' => 'Treści - hook',
                    'en' => 'Content Hooks',
                ],
                'id_parent' => $parentId,
            ],
            'AdminWebixaContentItem' => [
                'title' => [
                    'pl' => 'Treści w Blokach',
                    'en' => 'Items in Blocks',
                ],
                'id_parent' => -1,
            ],
            'AdminWebixaContentLink' => [
                'title' => [
                    'pl' => 'Linki w Blokach',
                    'en' => 'Links in Blocks',
                ],
                'id_parent' => -1,
            ],
            'AdminWebixaContentSlide' => [
                'title' => [
                    'pl' => 'Slajdy w Blockach',
                    'en' => 'Slides in Blocks',
                ],
                'id_parent' => -1,
            ],
            'AdminWebixaContentCategoryProducts' => [
                'title' => [
                    'pl' => 'Kategoria produktów w Blockach',
                    'en' => 'Category products in Blocks',
                ],
                'id_parent' => -1,
            ],
            'AdminWebixaContentBanner' => [
                'title' => [
                    'pl' => 'Banery w Blockach',
                    'en' => 'Banners in Blocks',
                ],
                'id_parent' => -1,
            ],
        ];

        $return = true;
        foreach ($tabsToInstall as $controller => $tabInfo) {
            if (Tab::getIdFromClassName($controller)) {
                continue;
            }
            $tab = new Tab();
            $tab->id_parent = $tabInfo['id_parent'];
            foreach (Language::getLanguages(false) as $lang) {
                $tab->name[$lang['id_lang']] = isset($tabInfo['title'][$lang['iso_code']]) ? $tabInfo['title'][$lang['iso_code']] : $tabInfo['title']['en'];
            }
            $tab->class_name = $controller;
            $tab->module = $this->name;

            $return &= $tab->save();
        }

        return $return;
    }

    public function uninstallAdminTabs()
    {
        $tabs = Tab::getCollectionFromModule($this->name);
        foreach ($tabs as $tab) {
            $tab->delete();
        }

        return true;
    }

    public function hookDisplayHeader($params)
    {
        $this->context->controller->addJS(($this->_path) . 'views/js/front.js', 'all');
        $this->context->controller->addCSS(($this->_path) . 'views/css/front.css');
    }

    public function renderWidget($hookName, array $configuration)
    {
        $webixaContentHook = WebixaContentHook::getByHookName($hookName);
        if (!Validate::isLoadedObject($webixaContentHook)) {
            return;
        }

        $configuration['contentHook'] = $webixaContentHook;

        $configuration['id'] = !empty($configuration['id']) ? (int) $configuration['id'] : 0;

        $cacheKey = $this->name . '|' . $hookName . '|' . $configuration['id'];

        if ($this->ps_version < 1.7) {
            $template = 'hook.tpl';
        } else {
            $template = 'module:' . $this->name . '/views/templates/hook/hook.tpl';
        }

        if (!$this->isCached($template, $this->getCacheId($cacheKey))) {
            if ($variables = $this->getWidgetVariables($hookName, $configuration)) {
                $this->context->smarty->assign(
                    $variables
                );
            } else {
                return;
            }
        }
        if ($this->ps_version < 1.7) {
            return $this->display(__DIR__ . '/' . $this->name . '.php', $template, $this->getCacheId($cacheKey));
        }

        return $this->fetch($template, $this->getCacheId($cacheKey));
    }

    public function getWidgetVariables($hookName, array $configuration)
    {
        if (!Validate::isLoadedObject($configuration['contentHook'])) {
            return false;
        }
        $idLang = $this->context->language->id;
        $idShop = $this->context->shop->id;

        $data = [
            'is17' => $this->ps_version >= '1.7',
            'hookName' => $hookName,
            'contentHook' => $configuration['contentHook'],
            'contentBlockList' => [],
        ];
        if (!empty($configuration['id']) && Validate::isUnsignedId($configuration['id'])) {
            $blocks = WebixaContentBlock::getActiveByWebixaContentHookId($configuration['contentHook']->id, $idLang, $idShop, $configuration['id']);
        } else {
            $blocks = WebixaContentBlock::getActiveByWebixaContentHookId($configuration['contentHook']->id, $idLang, $idShop);
        }
        if (empty($blocks)) {
            return false;
        }

        foreach ($blocks as $block) {
            switch ($block['webixaContentBlock']->type) {
                case WebixaConfig::BLOCK_TYPE_LINKS:
                    $templates = WebixaConfig::AVAILABLE_LINKS_TEMPLATES;
                    $list = WebixaContentLink::getActiveByWebixaContentBlockId($block['webixaContentBlock']->id, $idLang, $idShop);

                    break;
                case WebixaConfig::BLOCK_TYPE_SLIDES:
                    $templates = WebixaConfig::AVAILABLE_SLIDES_TEMPLATES;
                    $list = WebixaContentSlide::getActiveByWebixaContentBlockId($block['webixaContentBlock']->id, $idLang, $idShop);
                    break;
                case WebixaConfig::BLOCK_TYPE_ITEMS:
                    $templates = WebixaConfig::AVAILABLE_ITEMS_TEMPLATES;
                    $list = WebixaContentItem::getActiveByWebixaContentBlockId($block['webixaContentBlock']->id, $idLang, $idShop);
                    break;
                case WebixaConfig::BLOCK_TYPE_CATEGORY_PRODUCTS:
                    $templates = WebixaConfig::AVAILABLE_CATEGORY_PRODUCTS_TEMPLATES;
                    $categoryProductsConfiguration = WebixaContentCategoryProducts::getBlockConfigurationByContext($block['webixaContentBlock']->id, $this->context);
                    if (
                        !$categoryProductsConfiguration
                    ) {
                        continue 2;
                    }

                    $category = new Category((int) $categoryProductsConfiguration->id_category);

                    if (!Validate::isLoadedObject($category) || !$category->active) {
                        continue 2;
                    }

                    $searchProvider = new CategoryProductSearchProvider(
                        $this->getTranslator(),
                        $categoryProductsConfiguration->search_subcategories
                    );
                    $products = $this->getCategoryProducts($searchProvider, $categoryProductsConfiguration);
                    $allLink = $this->context->link->getCategoryLink($category);

                    $imageDesktop = $categoryProductsConfiguration->image;
                    $imageMobile = $categoryProductsConfiguration->image_mobile;


                    if (empty($products)) {
                        continue 2;
                    }

                    $list = [
                        'products' => $products,
                        'allLink' => $allLink,
                        'images' => [
                            'desktop' => $imageDesktop,
                            'mobile' => $imageMobile,
                        ],
                    ];

                    break;
                case WebixaConfig::BLOCK_TYPE_BANNERS:
                    $templates = WebixaConfig::AVAILABLE_BANNERS_TEMPLATES;
                    $list = WebixaContentBanner::getActiveByWebixaContentBlockIdAndContext($block['webixaContentBlock']->id, $this->context);
                    break;
                case WebixaConfig::BLOCK_TYPE_HTML:
                    $templates = WebixaConfig::AVAILABLE_HTML_TEMPLATES;
                    $list = [];
                    break;
            }

            if (empty($templates[$block['template']])) {
                continue;
            }

            $tpl = $this->context->smarty->createTemplate('module:' . $this->name .  '/views/templates/hook/' . $templates[$block['template']]['path']);
            $tpl->assign(
                [
                    'is17' => $this->ps_version >= '1.7',
                    'hookName' => $hookName,
                    'contentHook' => $configuration['contentHook'],
                    'contentBlock' => $block['webixaContentBlock'],
                    'contentList' => $list,
                ],
            );

            $data['contentBlockList'][] = [
                'renderedContent' => $tpl->fetch(),
            ];
        }

        return $data;
    }

    private function getCategoryProducts($searchProvider, WebixaContentCategoryProducts $categoryProductsConfiguration)
    {
        $nbProducts = $categoryProductsConfiguration->nbr_products;
        $idCategory = $categoryProductsConfiguration->id_category;

        $context = new ProductSearchContext($this->context);

        $query = new ProductSearchQuery();

        $query
            ->setResultsPerPage($nbProducts)
            ->setIdCategory($idCategory)
            ->setPage(1);

        $result = $searchProvider->runQuery(
            $context,
            $query
        );

        $assembler = new ProductAssembler($this->context);
        $presenterFactory = new ProductPresenterFactory($this->context);
        $presentationSettings = $presenterFactory->getPresentationSettings();
        if (version_compare(_PS_VERSION_, '1.7.5', '>=')) {
            $presenter = new \PrestaShop\PrestaShop\Adapter\Presenter\Product\ProductListingPresenter(
                new ImageRetriever(
                    $this->context->link
                ),
                $this->context->link,
                new PriceFormatter(),
                new ProductColorsRetriever(),
                $this->context->getTranslator()
            );
        } else {
            $presenter = new \PrestaShop\PrestaShop\Core\Product\ProductListingPresenter(
                new ImageRetriever(
                    $this->context->link
                ),
                $this->context->link,
                new PriceFormatter(),
                new ProductColorsRetriever(),
                $this->context->getTranslator()
            );
        }

        $productsForTemplate = [];

        $presentationSettings->showPrices = true;

        $products = $result->getProducts();

        foreach ($products as $rawProduct) {
            $productsForTemplate[] = $presenter->present(
                $presentationSettings,
                $assembler->assembleProduct($rawProduct),
                $this->context->language
            );
        }

        return $productsForTemplate;
    }

    public function clearModuleCache($template = '*', $cacheKey = null)
    {
        if ('*' == $template) {
            if ($this->ps_version < 1.7) {
                $template = 'hook.tpl';
            } else {
                $template = 'module:' . $this->name . '/views/templates/hook/hook.tpl';
            }
            parent::_clearCache($template, $cacheKey);
        } else {
            parent::_clearCache($template, $cacheKey);
        }
    }

    public function hookActionObjectWebixaContentHookAddAfter($params)
    {
        $this->registerHook($params['object']->hook);
        // todo: call clearCache
    }

    public function hookActionObjectWebixaContentHookUpdateAfter($params)
    {
        // todo: call clearCache
    }

    public function hookActionObjectWebixaContentHookDeleteAfter($params)
    {
        $this->unregisterHook($params['object']->hook);
        // todo: call clearCache
    }

    public function hookDisplayBackOfficeHeader($params)
    {
        if (!defined('_PS_ADMIN_DIR_') || Tools::getValue('ajax', 0)) {
            return;
        }

        if (Tools::getValue('configure') !== $this->name && (!isset($this->context->controller->module) || $this->context->controller->module->name != $this->name)) {
            return;
        }
        if (!$this->active) {
            $this->context->controller->warnings[] = $this->prepareAdminMessage(
                sprintf($this->l('Module %s is disabled. To display it on front don\'t forget to Enable it'), $this->displayName)
            );
        }

        $this->context->controller->addJquery();
        $this->context->controller->addJS($this->_path . 'views/js/back.js');
        $this->context->controller->addCSS($this->_path . 'views/css/back.css');

        $jsVariables = [
            // 'name' => $value
        ];
        $jsLangVariables = [
            // 'name' => $this->l('text')
        ];
        if (empty($jsVariables) && empty($jsLangVariables)) {
            return;
        }

        if (Tools::version_compare(_PS_VERSION_, '1.6.0.11', '>=')) {
            foreach ($jsLangVariables as $key => $value) {
                Media::addJsDefL($key, $value);
            }
            Media::addJsDef($jsVariables);
        } else {
            $tpl = $this->context->smarty->createTemplate($this->getLocalPath() . 'views/templates/admin/js/variables.tpl');
            $tpl->assign(
                [
                    'webixa_js_variables' => array_merge($jsLangVariables, $jsVariables),
                ],
            );

            return $tpl->fetch();
        }
    }

    public function prepareAdminMessage($message, $className = 'warning')
    {
        if (is_array($message)) {
            $message = implode('<br>', $message);
        }

        $content = str_replace($this->displayName, '<span class="badge badge-' . $className . '"><b>' . $this->displayName . '</b></span>', $message);

        return $content;
    }

    public function prepareAdminInfoBanner()
    {
        $tpl = $this->context->smarty->createTemplate($this->getLocalPath() . 'views/templates/admin/banner/info.tpl');
        return $tpl->fetch();
    }

    public function codeToMessage($code)
    {
        switch ($code) {
            case UPLOAD_ERR_INI_SIZE:
                $message = $this->l('The uploaded file exceeds the upload_max_filesize directive in php.ini');
                break;
            case UPLOAD_ERR_FORM_SIZE:
                $message = $this->l('The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form');
                break;
            case UPLOAD_ERR_PARTIAL:
                $message = $this->l('The uploaded file was only partially uploaded');
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $message = $this->l('Missing a temporary folder');
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $message = $this->l('Failed to write file to disk');
                break;
            case UPLOAD_ERR_EXTENSION:
                $message = $this->l('File upload stopped by extension');
                break;
            default:
                $message = $this->l('Unknown upload error');
                break;
        }
        return $message;
    }

    public function getAvailableBlockTypes()
    {
        return [
            [
                'id' => WebixaConfig::BLOCK_TYPE_BANNERS,
                'name' => $this->l('Banners'),
            ],
            [
                'id' => WebixaConfig::BLOCK_TYPE_HTML,
                'name' => $this->l('HTML'),
            ],
            [
                'id' => WebixaConfig::BLOCK_TYPE_ITEMS,
                'name' => $this->l('Items'),
            ],
            [
                'id' => WebixaConfig::BLOCK_TYPE_SLIDES,
                'name' => $this->l('Slides'),
            ],
            [
                'id' => WebixaConfig::BLOCK_TYPE_LINKS,
                'name' => $this->l('Links'),
            ],
            [
                'id' => WebixaConfig::BLOCK_TYPE_CATEGORY_PRODUCTS,
                'name' => $this->l('Category Products'),
            ],
        ];
    }

    public function getAvailableTemplatesByBlockType($type)
    {
        switch ($type) {
            case WebixaConfig::BLOCK_TYPE_LINKS:
                return WebixaConfig::AVAILABLE_LINKS_TEMPLATES;
            case WebixaConfig::BLOCK_TYPE_SLIDES:
                return WebixaConfig::AVAILABLE_SLIDES_TEMPLATES;
            case WebixaConfig::BLOCK_TYPE_ITEMS:
                return WebixaConfig::AVAILABLE_ITEMS_TEMPLATES;
            case WebixaConfig::BLOCK_TYPE_CATEGORY_PRODUCTS:
                return WebixaConfig::AVAILABLE_CATEGORY_PRODUCTS_TEMPLATES;
            case WebixaConfig::BLOCK_TYPE_BANNERS:
                return WebixaConfig::AVAILABLE_BANNERS_TEMPLATES;
            case WebixaConfig::BLOCK_TYPE_HTML:
                return WebixaConfig::AVAILABLE_HTML_TEMPLATES;
        }
        return [];
    }

    public function getContent()
    {
        Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentBlock'));

        // return $this->prepareAdminInfoBanner();
    }
}
