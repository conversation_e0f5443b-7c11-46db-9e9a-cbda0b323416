<?php

class AdminWebixaContentCategoryProductsController extends ModuleAdminController
{
    public $bootstrap = true;
    protected $block_identifier;
    /** @var WebixaContentBlock */
    protected $webixaContentBlock;

    public function __construct()
    {
        $this->bootstrap = true;
        parent::__construct();

        if (version_compare(_PS_VERSION_, '1.7', '>=')) {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
            $this->tabAccess['view'] = Module::getPermissionStatic($this->module->id, 'view');

            $configAccess = Module::getPermissionStatic($this->module->id, 'configure');
            $this->tabAccess['add'] = $configAccess;
            $this->tabAccess['edit'] = $configAccess;
            $this->tabAccess['delete'] = $configAccess;
        } else {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
        }
    }

    public function initProcess()
    {
        $this->initList();

        parent::initProcess();
    }

    public function initList()
    {
        $this->fieldImageSettings = [
            [
                'name' => 'image',
                'dir' => '../modules/' . $this->module->name . '/' . WebixaContentCategoryProducts::_TYPE_IMG_DIR_,
            ],
            [
                'name' => 'image_mobile',
                'dir' => '../modules/' . $this->module->name . '/' . WebixaContentCategoryProducts::_TYPE_IMG_DIR_,
            ],
        ];
        $this->table = WebixaContentCategoryProducts::$definition['table'];
        $this->className = 'WebixaContentCategoryProducts';
        $this->identifier = WebixaContentCategoryProducts::$definition['primary'];
        $this->block_identifier = WebixaContentBlock::$definition['primary'];
        $this->_defaultOrderBy = $this->identifier;
        $this->_defaultOrderWay = 'ASC';

        $this->lang = false;
        if (Shop::isFeatureActive()) {
            Shop::addTableAssociation($this->table, ['type' => 'shop']);
        }
        $this->webixaContentBlock = new WebixaContentBlock((int)Tools::getValue($this->block_identifier));

        if (!Validate::isLoadedObject($this->webixaContentBlock)) {
            Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentBlock'));
        }

        self::$currentIndex .= '&' . $this->block_identifier . '=' . (int)$this->webixaContentBlock->id;

        $this->_select = 'cl.name as `category_name`, gl.name as `group_name`, CONCAT(LEFT(cu.`firstname`, 1), \'. \', cu.`lastname`) AS `customer_name`';

        $this->_join = '
            LEFT JOIN `' . _DB_PREFIX_ . Category::$definition['table'] . '_lang` cl ON (a.' . Category::$definition['primary'] . '=cl.' . Category::$definition['primary'] . ' AND cl.id_lang=' . $this->context->language->id . ')
            LEFT JOIN `' . _DB_PREFIX_ . Customer::$definition['table'] . '` cu ON (a.' . Customer::$definition['primary'] . '=cu.' . Customer::$definition['primary'] . ')
            LEFT JOIN `' . _DB_PREFIX_ . Group::$definition['table'] . '_lang` gl ON (a.' . Group::$definition['primary'] . '=gl.' . Group::$definition['primary'] . ' AND gl.id_lang=' . $this->context->language->id . ')
        ';

        $this->_where = 'AND ' . $this->block_identifier . '=' . (int)$this->webixaContentBlock->id;

        $this->fields_list = [
            $this->identifier => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
            ],
            'image' => [
                'title' => $this->l('Image'),
                'orderby' => false,
                'search' => false,
                'align' => 'center',
                'callback' => 'displayImageDefaultOnList',
            ],
            'category_name' => [
                'title' => $this->l('Category'),
                'filter_key' => 'cl!name',
            ],
            'customer_name' => [
                'title' => $this->l('Customer'),
                'havingFilter' => true,
            ],
            'group_name' => [
                'title' => $this->l('Group'),
                'filter_key' => 'gl!name',
            ],
            'nbr_products' => [
                'title' => $this->l('Product limit'),
            ],
            'search_subcategories' => [
                'title' => $this->l('Search subcategories'),
                'align' => 'center',
                'active' => 'search',
                'class' => 'fixed-width-sm',
                'ajax' => true,
                'type' => 'bool',
                'orderby' => false,
            ],
            'active' => [
                'title' => $this->l('Displayed'),
                'align' => 'center',
                'active' => 'status',
                'class' => 'fixed-width-sm',
                'ajax' => true,
                'type' => 'bool',
                'orderby' => false,
            ],
        ];

        $this->addRowAction('edit');
        $this->addRowAction('delete');

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected category products?'),
            ],
        ];
    }

    public function renderForm()
    {
        /** @var WebixaContentCategoryProducts $obj */
        if (!($obj = $this->loadObject(true))) {
            return;
        }

        $image = WebixaContentCategoryProducts::_IMG_DIR_ . $obj->id . '.' . $obj->image_format;

        if (file_exists($image)) {
            $image_url = ImageManager::thumbnail(
                $image,
                $this->table . '_' . (int)$obj->id . '.' . $obj->image_format,
                350,
                $obj->image_format,
                true,
                true
            );
            $image_size = file_exists($image) ? filesize($image) / 1000 : false;
            $image_delete_url = file_exists($image) ? self::$currentIndex . '&' . $this->identifier . '=' . (int)$this->object->id . '&action=deleteImage&field=image&token=' . $this->token : false;
        }

        $image_mobile = WebixaContentCategoryProducts::_IMG_DIR_MOBILE_ . $obj->id . '.' . $obj->image_format_mobile;

        if (file_exists($image_mobile)) {
            $image_mobile_url = ImageManager::thumbnail(
                $image_mobile,
                $this->table . '_mobile_' . (int) $obj->id . '.' . $obj->image_format_mobile,
                350,
                $obj->image_format_mobile,
                true,
                true
            );
            $image_mobile_size = file_exists($image_mobile) ? filesize($image_mobile) / 1000 : false;
            $image_mobile_delete_url = file_exists($image_mobile) ? self::$currentIndex . '&' . $this->identifier . '=' . (int)$this->object->id . '&action=deleteImage&field=image_mobile&token=' . $this->token : false;
        }

        $this->show_form_cancel_button = false;
        $this->fields_form = [
            'tinymce' => true,
            'legend' => [
                'title' => Validate::isLoadedObject($obj) ? $this->l('Update content Category Products') : $this->l('Add content Category Products'),
                'icon' => 'icon-cogs',
            ],
            'input' => [
                [
                    'type' => 'hidden',
                    'name' => 'id_webixa_content_block',
                    'value' => $this->webixaContentBlock->id,
                ],
                [
                    'type' => 'file',
                    'label' => $this->l('Image'),
                    'name' => 'image',
                    'image' => !empty($image_url) ? $image_url : false,
                    'size' => $image_size ?? 0,
                    'delete_url' => !empty($image_delete_url) ? $image_delete_url : '',
                    'display_image' => true,
                    'col' => 6,
                    'hint' => $this->l('Upload image from your computer.'),
                ],
                [
                    'type' => 'file',
                    'label' => $this->l('Image for mobile'),
                    'name' => 'image_mobile',
                    'image' => !empty($image_mobile_url) ? $image_mobile_url : false,
                    'size' => $image_mobile_size ?? 0,
                    'delete_url' => !empty($image_mobile_delete_url) ? $image_mobile_delete_url . '&field=image_mobile' : '',
                    'display_image' => true,
                    'col' => 6,
                    'hint' => $this->l('Upload image from your computer.'),
                ],
                [
                    'type' => 'categories',
                    'label' => $this->l('Category'),
                    'name' => Category::$definition['primary'],
                    'id' => Category::$definition['primary'],
                    'tree' => array(
                        'root_category' => (int)Configuration::get('PS_ROOT_CATEGORY'),
                        'id' => 'id_category',
                        'name' => 'name_category',
                        'selected_categories' => (!$this->object->id_category ? [] : [$this->object->id_category]),
                        'use_checkbox' => false,
                    ),
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Customer Id'),
                    'name' => 'id_customer',
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Group'),
                    'name' => Group::$definition['primary'],
                    'class' => 'input fixed-width-xxl',
                    'options' => [
                        'query' => array_merge([['id_group' => NULL, 'name' => '---']], Group::getGroups($this->context->language->id)),
                        'id' => Group::$definition['primary'],
                        'name' => 'name',
                    ],
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Product limit'),
                    'name' => 'nbr_products',
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Search subcategories'),
                    'name' => 'search_subcategories',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'search_subcategories_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'search_subcategories_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Displayed'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
            ],
            'buttons' => [
                [
                    'title' => $this->l('Cancel'),
                    'id' => $this->table . '_form_cancel_btn',
                    'href' => self::$currentIndex . '&token=' . $this->token,
                    'icon' => 'process-icon-cancel',
                ],
            ],
        ];
        if (Shop::isFeatureActive()) {
            $this->fields_form['input'][] = [
                'type' => 'shop',
                'label' => $this->l('Shop association:'),
                'name' => 'checkBoxShopAsso',
            ];
        }

        return parent::renderForm();
    }

    public function renderList()
    {
        return $this->module->prepareAdminInfoBanner() . parent::renderList();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Category products in') . ' ' . $this->webixaContentBlock->title[$this->context->language->id];
    }

    public function initToolbar()
    {
        parent::initToolbar();

        if ('edit' == $this->display || 'add' == $this->display) {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => self::$currentIndex . '&token=' . $this->token,
                'desc' => $this->l('Back'),
                'icon' => 'process-icon-back',
            ];
        } else {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => $this->context->link->getAdminLink('AdminWebixaContentBlock'),
                'desc' => $this->l('Back to Blocks'),
                'icon' => 'process-icon-back',
            ];
        }
    }

    public function setMedia($isNewTheme = false)
    {
        if (!$this->module->active && version_compare(_PS_VERSION_, '8.0.2', '>=')) {
            $this->module->hookDisplayBackOfficeHeader([]);
        }
        parent::setMedia($isNewTheme);
    }

    public function postProcess()
    {
        $ret = parent::postProcess();
        if (!$this->ajax) {
            if (!empty($this->action) && method_exists($this, 'process' . ucfirst(Tools::toCamelCase($this->action)))) {
                $this->module->clearModuleCache('*');
            }
        }

        return $ret;
    }

    protected function _childValidation()
    {
        $id = (int) Tools::getValue($this->identifier);

        $WebixaContentBlockId = (int) Tools::getValue('id_webixa_content_block');
        $CustomerId = Tools::getValue('id_customer', NULL);
        $GroupId = Tools::getValue('id_group', NULL);

        $duplicateId = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue('
            SELECT ' . WebixaContentCategoryProducts::$definition['primary'] . '
            FROM `' . _DB_PREFIX_ . WebixaContentCategoryProducts::$definition['table'] . '`
            WHERE
                id_webixa_content_block = ' . $WebixaContentBlockId . ' AND
                id_customer' . (empty($CustomerId) ? ' IS NULL' : '=' . (int)$CustomerId) . ' AND
                id_group' . (empty($GroupId) ? ' IS NULL' : '=' . (int)$GroupId) . '
        ');

        if ($duplicateId && (empty($id) || $id !== (int) $duplicateId)) {
            $this->errors['id_group'] = $this->trans(
                'Setup already exist for chosen %1$s and %2$s',
                [WebixaContentCategoryProducts::displayFieldName('id_customer', get_class($this)), WebixaContentCategoryProducts::displayFieldName('id_group', get_class($this))],
                'Admin.Notifications.Error'
            );
        }
    }

    public function ajaxProcessSearchWebixaContentCategoryProducts()
    {
        $id_object = (int) Tools::getValue($this->identifier);

        $sql = 'UPDATE ' . _DB_PREFIX_ . $this->table . ' SET `search_subcategories`= NOT `search_subcategories` WHERE ' . $this->identifier . '=' . $id_object;
        $result = Db::getInstance()->execute($sql);

        if ($result) {
            $this->module->clearModuleCache('*');
            $response = json_encode(['success' => 1, 'text' => $this->l('The status has been updated successfully.')]);
        } else {
            $response = json_encode(['success' => 0, 'text' => $this->l('An error occurred while updating the status.')]);
        }
        $this->ajaxDie($response);
    }

    public function ajaxProcessStatusWebixaContentCategoryProducts()
    {
        $id_object = (int) Tools::getValue($this->identifier);

        $sql = 'UPDATE ' . _DB_PREFIX_ . $this->table . ' SET `active`= NOT `active` WHERE ' . $this->identifier . '=' . $id_object;
        $result = Db::getInstance()->execute($sql);

        if ($result) {
            $this->module->clearModuleCache('*');
            $response = json_encode(['success' => 1, 'text' => $this->l('The status has been updated successfully.')]);
        } else {
            $response = json_encode(['success' => 0, 'text' => $this->l('An error occurred while updating the status.')]);
        }
        $this->ajaxDie($response);
    }

    protected function ajaxDie($value = null, $controller = null, $method = null)
    {
        if (ob_get_length() > 0) {
            ob_end_clean();
        }
        header('Content-Type: application/json');

        if (version_compare(_PS_VERSION_, '1.6.1', '>=')) {
            return parent::ajaxDie($value, $controller, $method);
        }

        header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        echo $value;
        exit;
    }
    protected function postImage($id)
    {
        parent::postImage($id);

        return !count($this->errors) ? true : false;
    }

    protected function uploadImage($id, $name, $dir, $ext = false, $width = null, $height = null)
    {
        $imageNamePrefix = 'image_mobile' == $name ? 'mobile_' : '';

        if (isset($_FILES[$name]['error']) && !in_array($_FILES[$name]['error'], [UPLOAD_ERR_OK, UPLOAD_ERR_NO_FILE])) {
            $this->errors[] = $this->module->codeToMessage($_FILES[$name]['error']);
            return false;
        }

        if (ImageManager::isCorrectImageFileExt($_FILES[$name]['name'])) {
            $imageNameParts = explode('.', $_FILES[$name]['name']);
            $this->imageType = end($imageNameParts);
        }

        if (isset($_FILES[$name]['tmp_name']) && !empty($_FILES[$name]['tmp_name'])) {
            // Delete old image
            if (Validate::isLoadedObject($object = $this->loadObject())) {
                $object->deleteImage(true, $name);
            } else {
                return false;
            }

            // Check image validity
            $max_size = isset($this->max_image_size) ? $this->max_image_size : 0;
            if ($error = ImageManager::validateUpload($_FILES[$name], Tools::getMaxUploadSize($max_size))) {
                $this->errors[] = $error;
            }

            if (!move_uploaded_file($_FILES[$name]['tmp_name'], _PS_IMG_DIR_ . $dir . $imageNamePrefix . $id . '.' . $this->imageType)) {
                $this->errors[] = $this->trans('An error occurred while uploading the image.', [], 'Admin.Notifications.Error');
            }

            if (count($this->errors)) {
                return false;
            }

            return $this->afterImageUpload();
        }

        return true;
    }

    public function ajaxProcessDeleteImage()
    {
        $field = Tools::getValue('field');
        $object = $this->loadObject();
        if (!Validate::isLoadedObject($object) || !$object->deleteImage(true, $field)) {
            $this->errors[] = $this->l('Unable to delete image');
        }

        $this->module->clearModuleCache('*');
        $this->ajaxDie(json_encode([
            'success' => empty($this->errors),
            'msg' => !empty($this->errors) ? implode('<br>', $this->errors) : $this->l('Image successfully deleted'),
        ]));
    }

    public function processDeleteImage()
    {
        $field = Tools::getValue('field');
        $object = $this->loadObject();
        if (!Validate::isLoadedObject($object) || !$object->deleteImage(true, $field)) {
            $this->errors[] = $this->l('Unable to delete image');
        } else {
            $this->confirmations[] = $this->l('Image successfully deleted');
        }

        $this->module->clearModuleCache('*');

        Tools::redirectAdmin(
            self::$currentIndex.'&'.$this->identifier.'='.$object->id.'&update'.$this->table.'&token='.$this->token
        );
    }
    public static function displayImageDefaultOnList($id, $row = null)
    {
        $img_path = WebixaContentCategoryProducts::_IMG_DIR_ . $id . '.jpg';

        $path_to_image = false;
        if (file_exists($img_path)) {
            $path_to_image = $img_path;
        }

        $context = Context::getContext();
        return ImageManager::thumbnail($path_to_image, WebixaContentCategoryProducts::$definition['table'] . '_mini_' . $id . '_' . $context->shop->id . '.jpg', 45, 'jpg');
    }
}
