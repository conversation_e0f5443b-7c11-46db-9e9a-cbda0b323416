<?php
$sql = array();

// Drop tables in reverse order of creation (optional, but good practice)
$sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'inspiration_product`;';
$sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'inspiration_to_category`;';
$sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'inspiration_category_lang`;';
$sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'inspiration_category`;';
$sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'inspiration_lang`;';
$sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'inspiration`;';

$success = true;
foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        // Log error or display message if possible
        error_log('Error executing SQL query during ' . $this->name . ' uninstallation: ' . Db::getInstance()->getMsgError() . ' - Query: ' . $query);
        $success = false;
        // Decide if we should stop or continue dropping other tables
    }
}

// Return true if all queries succeeded (or if we decide to ignore errors)
return $success;

?>
