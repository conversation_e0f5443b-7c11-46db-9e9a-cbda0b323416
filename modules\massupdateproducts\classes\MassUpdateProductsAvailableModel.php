<?php

class MassUpdateProductsAvailableModel extends MassUpdateProductsAbstract
{
    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateProductsAvailableModel');
        parent::__construct($module, $object, $context);

        $this->fields['available_now'] = array(
            'display_name' => $this->module->l('Available now', $this->languages_name),
            'name' => 'available_now',
            'active' => true,
            'type' => self::TYPE_STR,
            'lang' => true
        );
        $this->fields['available_later'] = array(
            'display_name' => $this->module->l('Available later', $this->languages_name),
            'name' => 'available_later',
            'active' => true,
            'type' => self::TYPE_STR,
            'lang' => true
        );
        $this->fields['out_of_stock'] = array(
            'display_name' => $this->module->l('Out of stock', $this->languages_name),
            'name' => 'out_of_stock',
            'active' => true,
            'type' => self::TYPE_CAT_REC,
        );

        $default = Configuration::get('PS_ORDER_OUT_OF_STOCK');
        $default_name = $this->module->l("Allow to order", $this->languages_name);
        if ($default == 0) {
            $default_name = $this->module->l("Disallow to order", $this->languages_name);
        }

        $outOfStock = array(
            '0' => array('display_name' => $this->module->l("Disallow to order", $this->languages_name), 'id' => 0, 'level_depth' => 0),
            '1' => array('display_name' => $this->module->l("Allow to order", $this->languages_name), 'id' => 1, 'level_depth' => 0),
            '2' => array('display_name' => $this->module->l("Use the default", $this->languages_name).' ('.$default_name.')', 'id' => 2, 'level_depth' => 0),
        );

        $this->fields['out_of_stock']['select'] = $outOfStock;
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;

        if ($data) {
            foreach ($data as $id_product => $d) {
                $result[$id_product] = array(
                    'error' => true,
                    'message' => ''
                );

                $dataProduct = $d['data'];
                $id_shop = (int)Tools::getValue('shop', $this->context->shop->id);
                $product = new Product($id_product, false, null, $id_shop);
                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }
                foreach ($this->object->languages as $language) {
                    $product->available_now[$language['id_lang']] = $dataProduct['available_now_'.$language['iso_code']];
                }

                foreach ($this->object->languages as $language) {
                    $product->available_later[$language['id_lang']] = $dataProduct['available_later_'.$language['iso_code']];
                }
                $product->out_of_stock = (int)$dataProduct['out_of_stock'];

                $errors = $product->validateFields(false, true);
                $errors2 = $product->validateFieldsLang(false, true);
                if ($errors !== true || $errors2 !== true) {
                    if ($errors !== true)
                        $result[$id_product]['message'] = '<p style="color: #FFF;">' . (is_bool($errors) ?
                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)) . '</p>';
                    if ($errors2 !== true)
                        $result[$id_product]['message'] = '<p style="color: #FFF;">' . (is_bool($errors2) ?
                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)) . '</p>';
                    continue;
                } else {
                    if ($product->update()) {
                        StockAvailable::setProductOutOfStock((int)$product->id, (int)$product->out_of_stock, (int)$id_shop);
                        Db::getInstance()->update('product', array(
                            'out_of_stock' => (int)$dataProduct['out_of_stock']
                        ), 'id_product = '.(int)$id_product);
                        Db::getInstance()->update('stock_available', array(
                            'out_of_stock' => (int)$dataProduct['out_of_stock']
                        ), 'id_product = '.(int)$id_product);
                        $result[$id_product]['message'] = $this->module->l('Product saved', $this->languages_name) . ': ' . $product->name[$this->context->language->id];
                        $result[$id_product]['error'] = false;
                    } else {
                        $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                        continue;
                    }
                }
            }
        } else {
            $end = true;
        }

        return array(
            'raport' => $result,
            'end' => $end
        );
    }

    public function display($result)
    {
        $ids_product = $result['result'];
        $result['result'] = '';
        $result['table'] = true;

        if ($ids_product)
            foreach ($ids_product as $product_arr) {
                $product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['dates']['products_count'] --;
                    continue;
                }
                $product->id_shop_object = $product_arr['id_shop'];
                $result['result'] .= $this->displayProduct($product);
            }

        return $result;
    }

    public function displayProduct(&$product)
    {
        parent::displayProduct($product);
        $product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
            $this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
//        $product->quantity = StockAvailable::getQuantityAvailableByProduct($product->id);
//        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $product->full_name = $product->name[$this->context->language->id];
        $product->has_combination = false;//!!$product->getAttributeCombinations($this->context->language->id);
//        echo $this->context->language->id;
//echo "<pre>";print_r($product);
        $this->fields['name']['value'] = $product->name;

        $this->fields['available_now']['value'] = $product->available_now;

        $this->fields['available_later']['value'] = $product->available_later;

        $this->fields['out_of_stock']['value'] = $product->out_of_stock;

        $this->context->smarty->assign(array(
            'product' => $product,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
        ));

        return $this->object->createTemplate('tr-product.tpl')->fetch();
    }

    public function extra()
    {

    }

    public function displayCombination(&$product, &$combination)
    {
        if (!parent::displayCombination($product, $combination)) {
            return '';
        }
        $cover = $combination->getWsImages();
        $combination->image_link = isset($cover[0]['id']) && $cover[0]['id'] ? $this->context->link->getImageLink(
            $this->object->img_type, isset($cover[0]['id']) ? $cover[0]['id'] : 0, $this->object->img_type) : '';
        $combination->quantity = StockAvailable::getQuantityAvailableByProduct($product->id, $combination->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $names = $combination->getAttributesName($this->context->cookie->id_lang);
        $tmp_n = array();
        if ($names)
            foreach ($names as $name) $tmp_n[] = $name['name'];
        $combination->full_name = implode(' - ', $tmp_n);

        $this->fields['price']['value'] = array(
            0 => round($combination->price, 2),
            1 => round($combination->price * (1 + ($product->rate / 100)), 2)
        );
        $this->fields['code']['value'] = $combination->reference;
        $this->fields['quantity']['value'] = $combination->quantity;
        $this->fields['minimal_quantity']['value'] = $combination->minimal_quantity;
        $this->fields['minimal_quantity']['display'] = true;
        $this->fields['weight']['value'] = $combination->weight;
        $this->fields['wholesale_price']['value'] = $combination->wholesale_price;
        $this->fields['unit_price']['value'] = $combination->unit_price;
        $this->fields['ean']['value'] = $combination->ean13;

        $this->context->smarty->assign(array(
            'product' => $product,
            'combination' => $combination,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
            'widths' => MassUpdateProductsAbstract::getWidths()
        ));

        return $this->object->createTemplate('tr-combination.tpl')->fetch();
    }

}
