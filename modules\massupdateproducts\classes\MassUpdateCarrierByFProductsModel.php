<?php

/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateCarrierByFProductsModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        parent::__construct($module, $object, $context);
        $this->settings_name = Tools::strtoupper('MassUpdateCarrierByFProductsModel');
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data) {
            foreach ($data as $id_product) {
                $result[$id_product] = array(
                    'error' => true,
                    'message' => ''
                );
                $product = new Product($id_product);
                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found');
                    continue;
                }
                $carriers = Tools::getValue('carriers', array());
                $carriers_add = array();
                if ($carriers) {
                    foreach ($carriers as $carrier) $carriers_add[] = $carrier;
                }
                $errors = $product->validateFields(false, true);
                $errors2 = $product->validateFieldsLang(false, true);
                if ($errors !== true || $errors2 !== true) {
                    if ($errors !== true) {
                        $result[$id_product]['message'] = '<p style="color: #FFF;">' . (is_bool($errors) ?
                                $this->module->l('Validate error') : (is_array($errors) ? implode(' | ', $errors) : $errors)) . '</p>';
                    }
                    if ($errors2 !== true) {
                        $result[$id_product]['message'] = '<p style="color: #FFF;">' . (is_bool($errors2) ?
                                $this->module->l('Validate error') : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)) . '</p>';
                    }
                    continue;
                } else {
                    $product->setCarriers($carriers_add);
                    if ($product->update()) {
                        $result[$id_product]['message'] = $this->module->l('Product saved') . ': ' . $product->name[$this->context->language->id];
                        $result[$id_product]['error'] = false;
                    } else {
                        $result[$id_product]['message'] = $this->module->l('Problem with update');
                        continue;
                    }
                }
            }
        } else {
            $end = true;
        }
        return array(
            'result' => $result,
            'end' => $end
        );
    }

    public function displayCombination(&$product, &$combination)
    {
        if (!parent::displayCombination($product, $combination)) {
            return '';
        }
    }

    public function display($result)
    {
        $return = array();
        $return['table'] = false;
        $return['product_count'] = $result['datas']['product_count'];
        return $result;
    }

    public function extra()
    {
        $carriers = array();
        if (($carriers_arr = Carrier::getCarriers($this->context->language->id))) {
            foreach ($carriers_arr as $carrier) {
                $carriers[] = array(
                    'id_option' => $carrier['id_reference'],
                    'name' => $carrier['name']
                );
            }
        }
        $data = Context::getContext()->smarty->createData();
        $data->assign(array(
            'carriers' => $carriers
        ));
        return Context::getContext()->smarty->createTemplate($this->module->getLocalPath().'views/templates/admin/mass_update_products/fields/change_carrier.tpl', $data)->fetch();
        /*
        $helper = new HelperForm();
        $helper->module = $this->module;
        $helper->name_controller = $this->module->name;
        $helper->token = Tools::getAdminTokenLite('AdminMassUpdateProducts');
        $helper->currentIndex = AdminController::$currentIndex.'&configure='.$this->module->name;
        $helper->languages = $this->object->languages;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = true;
        $helper->title = '';
        $helper->show_toolbar = false;
        $helper->toolbar_scroll = false;
        $helper->submit_action = $this->module->name;
        $carriers = array();
        if (($carriers_arr = Carrier::getCarriers($this->context->language->id))) {
            foreach ($carriers_arr as $carrier) {
                $carriers[] = array(
                    'id_option' => $carrier['id_reference'],
                    'name' => $carrier['name']
                );
            }
        }
        $fields_form = array();
        $fields_form[0]['form'] = array(
            'input' => array(
                array(
                    'type' => 'checkbox',
                    'name' => 'carriers',
                    'class' => 'carriers',
                    'label' => $this->module->l('Select carriers'),
                    'values' => array(
                        'query' => $carriers,
                        'id' => 'id_option',
                        'name' => 'name'
                    )
                )
            )
        );
        return $helper->generateForm($fields_form);*/
    }

}
