<?php

/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
abstract class MassUpdateProductsAbstract
{

    const TYPE_STR = 0;
    const TYPE_INT = 1;
    const TYPE_BOOL = 2;
    const TYPE_SEL = 3;
    const TYPE_FIL = 4;
    const TYPE_SEL_CUST = 5;
    const TYPE_FLOAT = 6;
    const TYPE_PRI = 7;
    const TYPE_MUL_SEL = 8;
    const TYPE_CAT_REC = 9;
    const TYPE_REM = 10;
    const TYPE_FIL_DOWN = 11;
    const TYPE_TINY = 12;
    const TYPE_ACCESSORIES_OFF = 13;
    const TYPE_ACCESSORIES_ON = 14;

    private static $widths = array(
        self::TYPE_STR => 114,
        self::TYPE_INT => 74,
        self::TYPE_BOOL => 74,
        self::TYPE_SEL => 100,
        self::TYPE_FIL => 423,
        self::TYPE_SEL_CUST => 200,
        self::TYPE_FLOAT => 74,
        self::TYPE_PRI => 100,
        self::TYPE_MUL_SEL => 400,
        self::TYPE_CAT_REC => 300,
        self::TYPE_REM => 100,
        self::TYPE_FIL_DOWN => 423,
        self::TYPE_TINY => 700,
        self::TYPE_ACCESSORIES_OFF => 500,
        self::TYPE_ACCESSORIES_ON => 500
    );
    protected $all_active = false;
    protected $settings_name = '';
    protected $languages_name = '';
    protected $module = null;
    protected $object = null;
    protected $context = null;
    protected $schema = array(
        'display_name' => '',
        'name' => '',
        'active' => false,
        'type' => 0,
        'lang' => false,
        'multiple' => false,
        'select' => array(),
        'extra' => null,
        'combination' => false,
        'validate' => '',
        'value' => '',
        'display' => true,
        'attr' => '',
        'none-copy' => false,
        'class' => 'to-send',
        'sort' => false,
        'base_name' => ''
    );
    protected $fields = array();

    public static function getWidths()
    {
        return self::$widths;
    }

    public function __construct(&$module, &$object, &$context)
    {
        $this->module = $module;
        $this->object = $object;
        $this->context = $context;
        $this->languages_name = Tools::strtolower($this->settings_name);

        if (Shop::isFeatureActive())
            $this->all_active = !Context::getContext()->cookie->shopContext;
    }

    public function getFields()
    {
        $fields = unserialize(Configuration::get($this->settings_name));
        $fields_position = unserialize(Configuration::get($this->settings_name.'_POSITION'));

        if ($this->fields) {
            foreach ($this->fields as &$field) {
                if (isset($fields[$field['name']]))
                    $field['active'] = !!$fields[$field['name']];
                $field = array_merge($this->schema, $field);
            }

            $tmp = array();
            if (is_array($fields_position) && $fields_position) {
                $fields_position = array_flip($fields_position);
                ksort($fields_position);
                foreach ($fields_position as $position => $f_name) {
                    $tmp[$f_name] = isset($this->fields[$f_name]) ? $this->fields[$f_name] : array();
                    $tmp[$f_name]['position'] = $position;
                }

                foreach ($this->fields as $f_name => $rest) {
                    if (!isset($tmp[$f_name])) {
                        $tmp[$f_name] = $rest;
                        $tmp[$f_name]['position'] = count($tmp);
                    }
                }

                $this->fields = $tmp;
            } else {
                $i = 0;
                foreach ($this->fields as $f_name => $rest) {
                    $tmp[$f_name] = $rest;
                    $tmp[$f_name]['position'] = $i++;
                }
                $this->fields = $tmp;
            }
        }

        return $this->fields;
    }

    public function setFields($fields)
    {
        $result = array(
            'error' => true,
            'message' => ''
        );
        if (!is_array($fields))
            $result['message'] = $this->module->l('Saving failed', $this->languages_name);
        else {
            $field_view = array();
            $fields_position = array();
            foreach ($fields as $f_name => &$field) {
                $field_view[$f_name] = !!(int)$field['view'];
                $fields_position[$f_name] = $field['position'];
            }

            foreach ($this->fields as $f_name => $field) {
                if (!isset($fields_position[$f_name]))
                    $fields_position[$f_name] = count($fields_position);

                if (!isset($field_view[$f_name]))
                    $field_view[$f_name] = false;
            }

            Configuration::updateValue($this->settings_name, serialize($field_view));
            Configuration::updateValue($this->settings_name.'_POSITION', serialize($fields_position));
            $result['error'] = false;
            $result['messagge'] = $this->module->l('Settings saved', $this->languages_name);
        }
        return $result;
    }

    public function save(array $data)
    {
        return $data;
    }

    public function display($result)
    {
        return $result;
    }

    public function extra()
    {
        return null;
    }

    protected function reset()
    {
        foreach ($this->fields as &$field) $field['value'] = '';
    }

    public function displayCombination(&$product, &$combination)
    {
        self::reset();
        $default_currency = new Currency(Configuration::get('PS_CURRENCY_DEFAULT'), false, $this->context->shop->id);
        $ids = array();
        if (($attributes = $combination->getAttributesName($this->context->language->id))) {
            foreach ($attributes as $attribute) {
                $ids[] = $attribute['id_attribute'];
            }
        }

        sort($ids);
        $combination->ids_attribute = implode('-', $ids);
        $promotion = (int)Tools::getValue('promotion', 0);
        if ($promotion == 1 || $promotion == 2) {
            $is_specific_price = Db::getInstance()->getValue('SELECT id_specific_price FROM `'
                    ._DB_PREFIX_.'specific_price` WHERE id_product = '.
                    ((int)$product->id).' AND id_product_attribute = '.((int)$combination->id));
            self::promotion($promotion, !!$is_specific_price);

            if (!((!$is_specific_price && $promotion == 2) || ($is_specific_price && $promotion == 1)))
                return false;
        }

        $this->context->smarty->assign(array(
            'default_currency' => ValidateCore::isLoadedObject($default_currency) ? $default_currency : null
        ));

        return true;
    }

    public function displayProduct(&$product)
    {
        $default_currency = new Currency(Configuration::get('PS_CURRENCY_DEFAULT'), false, $this->context->shop->id);
        $this->context->smarty->assign(array(
            'all_active' => $this->all_active,
            'widths' => MassUpdateProductsAbstract::getWidths(),
            'tax_rate_product' => $this->object->tax_rates,
            'default_currency' => ValidateCore::isLoadedObject($default_currency) ? $default_currency : null,
            'iso' => file_exists(_PS_ROOT_DIR_.'/js/tinymce/langs/'.$this->context->language->iso_code.'.js') ? $this->context->language->iso_code : 'en',
            'path_css' => _THEME_CSS_DIR_,
            'ad' => dirname($_SERVER['PHP_SELF']),
            'per_page' => (int)Configuration::get('MASSUPDATEPRODUCT_PAGER'),
        ));
        self::reset();
        $promotion = (int)Tools::getValue('promotion', 0);
        if ($promotion == 1 || $promotion == 2) {
            $is_specific_price = Db::getInstance()->getValue('SELECT id_specific_price FROM `'.
                    _DB_PREFIX_.'specific_price` WHERE id_product = '.
                    (int)$product->id.' AND id_product_attribute = 0');
            self::promotion($promotion, !!$is_specific_price);

            if (!$is_specific_price)
                return false;
        }

        return true;
    }

    protected function promotion($promotion = 0, $specific = false)
    {
        if ($this->fields)
            foreach ($this->fields as &$field) {
                if ($promotion == 1) {
                    if (!$specific)
                        $field['display'] = false;
                    else
                        $field['display'] = $this->schema['display'];
                }
                elseif ($promotion == 2)
                    if ($specific)
                        $field['display'] = false;
                    else
                        $field['display'] = $this->schema['display'];
            }
    }

    public function removeMass(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product => $params) {
                $result[$id_product] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );

                $product_params = array_key_exists('data', $params) ? $params['data'] : null;
                $combinations = array_key_exists('combinations', $params) ? $params['combinations'] : null;

                $product = new Product($id_product);

                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                if ($product_params) {

                    if ($product->delete()) {
                        $result[$id_product]['error'] = false;
                        $result[$id_product]['message'] = '';
                    } else {
                        $result[$id_product]['message'] = $this->module->l('Problem with delete', $this->languages_name);
                        continue;
                    }
                } else {
                    $result[$id_product]['error'] = false;
                    $result[$id_product]['message'] = self::displayProduct($product);
                }

                if ($combinations)
                    foreach (array_keys($combinations) as $id_combination) {
                        $result[$id_product]['combinations'][$id_combination] = array(
                            'error' => true,
                            'message' => ''
                        );
                        $combination = new Combination($id_combination);

                        if (!Validate::isLoadedObject($combination)) {
                            $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Combination not found', $this->languages_name);
                            continue;
                        }

                        $result[$id_product]['combinations'][$id_combination]['error'] = false;
                    }
            } else
            $end = true;

        return array(
            'raport' => $result,
            'end' => $end
        );
    }

    public function filter($count = false, $ids = array())
    {
        if (Tools::isSubmit('filter')) {
            $categories = Tools::getValue('categories', null);
            $categories_default = Tools::getValue('categories_default', null);
            $manufacturer = Tools::getValue('manufacturer', null);
            $help_arr = explode(' - ', Tools::getValue('price', ''));
            $price_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $price_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('quantity', ''));
            $quantity_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[0]) : null;
            $quantity_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('weight', ''));
            $weight_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $weight_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $date_add_from = Tools::getValue('date_add_from', null);
            $date_add_to = Tools::getValue('date_add_to', null);
            $date_upd_from = Tools::getValue('date_update_from', null);
            $date_upd_to = Tools::getValue('date_update_to', null);
            $active = (int)Tools::getValue('active', null);
            $promotion = (int)Tools::getValue('promotion', null);
            $show_empty = (int)Tools::getValue('show_empty', null);
            $page = (int)Tools::getValue('page', 0);
            $elements = (int)Tools::getValue('elements', (int)Configuration::get('MASSUPDATEPRODUCT_PAGER'));
            Configuration::updateValue('MASSUPDATEPRODUCT_PAGER', (int)$elements);
            $product_name = pSQL(Tools::getValue('product', null));
            $ean_code = pSQL(Tools::getValue('filter_ean', null));
            $sorted_type = (int)Tools::getValue('sorted_type', 0);
            $sorted_by = pSQL(Tools::getValue('sorted_by', ''));

            $select = '';
            $limit = '';
            $method = '';

            $from = 'FROM `'._DB_PREFIX_.'product` as p ';
            $join = 'JOIN `'._DB_PREFIX_.'product_shop` as ps ON p.id_product = ps.id_product AND ps.id_shop = '.pSQL($this->context->shop->id).' ';
            $join .= 'JOIN `'._DB_PREFIX_.'product_lang` as pl ON p.id_product = pl.id_product AND ps.id_shop = pl.id_shop AND pl.id_lang = '
                    .(int)$this->context->language->id.' ';

            $where = 'WHERE 1=1 ';
            $where_e = '';
            $where_e .= $price_from ? ('AND ps.price >= '.$price_from.' ') : '';
            $where_e .= $price_to ? ('AND ps.price <= '.$price_to.' ') : '';
            $where .= $categories_default ? ('AND ps.id_category_default IN ('.implode(',', $categories_default).') ') : '';
            $where_e .= $weight_from ? ('AND p.weight >= '.$weight_from.' ') : '';
            $where_e .= $weight_to ? ('AND p.weight <= '.$weight_to.' ') : '';
            $where .= $product_name ? ('AND (pl.name LIKE \'%'.$product_name.'%\' OR p.reference LIKE \'%'.$product_name.'%\') ') : '';
            $where .= $ean_code ? ('AND p.ean13 LIKE \'%'.$ean_code.'%\' ') : '';
            $where .= $date_add_from ? ('AND ps.date_add >= \''.$date_add_from.'\' ') : '';
            $where .= $date_add_to ? ('AND ps.date_add <= \''.$date_add_to.'\' ') : '';
            $where .= $date_upd_from ? ('AND ps.date_upd >= \''.$date_upd_from.'\' ') : '';
            $where .= $date_upd_to ? ('AND ps.date_upd <= \''.$date_upd_to.'\' ') : '';
            $where .= $ids ? 'AND ps.id_product IN ('.implode(',', $ids).') ' : '';
            $where .= $active ? ($active == 1 ? 'AND ps.active = 1 ' : 'AND ps.active =  0 ') : '';
            $where .= $manufacturer ? ('AND p.id_manufacturer IN ('.implode(',', $manufacturer).') ') : '';
            $where .= $categories ? ('AND ps.id_product IN (SELECT cp.id_product FROM `'
                    ._DB_PREFIX_.'category_product` as cp WHERE cp.id_category IN ('.
                    implode(',', $categories).'))') : '';

            if (!(int)$this->object->shop_group->share_stock) {
                $join .= 'JOIN `'._DB_PREFIX_
                        .'stock_available` as sa ON p.id_product = sa.id_product AND sa.id_product_attribute = 0 AND sa.id_shop = ps.id_shop ';
                $where_e .= $quantity_from ? ('AND sa.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND sa.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND sa.quantity = 0 ' : ($show_empty == 2 ? ' AND sa.quantity != 0 ' : '') );
            } else {
                $where_e .= $quantity_from ? ('AND p.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND p.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND p.quantity = 0 ' : ($show_empty == 2 ? ' AND p.quantity != 0 ' : '') );
            }

            if ($promotion == 1)
                $where .= ' AND p.id_product IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp) ';
            elseif ($promotion == 2)
                $where .= ' AND p.id_product NOT IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp WHERE sp.id_product_attribute = 0) ';

            $sort = ' ORDER BY '.($sorted_by ? $sorted_by.' ' : 'ps.id_product ');

            if ($sorted_type)
                $sort .= 'DESC ';
            else
                $sort .= 'ASC ';

            if (!$count) {
                if ($page < 1)
                    $page = 1;
                $select = 'SELECT p.id_product, ps.id_shop ';
                $limit = 'LIMIT '.(($page - 1) * $elements).','.$elements;
                $method = 'executeS';
            }
            else {
                $select = 'SELECT COUNT(p.id_product) as q ';
                $limit = '';
                $method = 'getValue';
            }
	
            return array(
                'datas' => array_merge($this->object->getDatas($from.$join.$where.$where_e), array('product_count' =>
                    Db::getInstance()->getValue('SELECT COUNT(p.id_product) as product_count '.
                            $from.$join.$where.$where_e))),
                'result' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort.$limit),
                'result2' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort),
                //'sql' => $select.$from.$join.$where.$where_e.$sort.$limit,
                'per_page' => Configuration::get('MASSUPDATEPRODUCT_PAGER'),
            );
        }
        return null;
    }

    public function hasCombination()
    {
        return false;
    }

}
