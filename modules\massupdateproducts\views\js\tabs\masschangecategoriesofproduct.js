/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function() {
    var $products_list_body = $('#products_list').find('.main_body');
    var $header_row_multi = $('.header-row-multi');

    $products_list_body.on('change', '.to-send[send-name="category_def"]', function() {
	var $element = $(this);
	var $tr = $element.closest('.element-row');
	var $value = parseInt($element.val());
        var $old = $element.data('default');
        var $uncheck = $tr.find('.uncheck').prop('checked');
        $element.data('default', $value);

        if ($uncheck) {
            $tr.find('.to-send[value="' + $old + '"]').prop('checked', false);
        }

	if ($value) {
	    $tr.find('.to-send[value="' + $value + '"]').prop('checked', true);
        }
    });

    $header_row_multi.on('change', '.uncheck', function() {
        var $scope = $(this);
        $products_list_body.find('.uncheck').each(function() {
            var $uncheck = $(this);
            if ($uncheck.closest('.product-row').find('.check_single').prop('checked')) {
                $uncheck.prop('checked', $scope.prop('checked'));
            }
        });
    });
});