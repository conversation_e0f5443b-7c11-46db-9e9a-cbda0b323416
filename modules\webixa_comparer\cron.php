<?php
/**
 * CRON script for Webixa Comparer module
 * 
 * This script handles automated cleanup of old comparison data
 * 
 * Usage: 
 * curl -s "https://yourdomain.com/modules/webixa_comparer/cron.php?process_name=webixa_comparer_clean"
 * 
 * <AUTHOR> sp. z o.o.
 * @copyright 2024 Webixa sp. z o.o.
 * @license http://opensource.org/licenses/afl-3.0.php Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    require_once dirname(__FILE__) . '/../../config/config.inc.php';
}

// Security check - only allow specific process names
$allowedProcesses = ['webixa_comparer_clean'];
$processName = Tools::getValue('process_name');

if (!in_array($processName, $allowedProcesses)) {
    http_response_code(400);
    die('Invalid process name');
}

try {
    // Load the module
    $module = Module::getInstanceByName('webixa_comparer');
    
    if (!$module || !$module->active) {
        http_response_code(404);
        die('Module not found or not active');
    }
    
    // Execute the CRON job
    $result = $module->hookActionCronJob(['controller' => (object)['process_name' => $processName]]);
    
    if ($result) {
        http_response_code(200);
        echo "CRON job completed successfully: " . $result;
    } else {
        http_response_code(500);
        echo "CRON job failed or returned no result";
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo "Error executing CRON job: " . $e->getMessage();
}
