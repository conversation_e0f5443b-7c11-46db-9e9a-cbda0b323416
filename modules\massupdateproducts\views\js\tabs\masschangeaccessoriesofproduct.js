$(function () {
    var $products_list_body = $('#products_list').find('.main_body');
    var $header_row_multi = $('.header-row-multi');
    var $main = $('#massupdateproducts-filters');

    $header_row_multi.on('click', '.filter_off', function () {
        $products_list_body.find('.filter_off').each(function () {
            var $scope = $(this);
            if ($scope.closest('.product-row').find('.check_single').prop('checked')) {
                $scope.trigger('click');
            }
        });
    });

    $header_row_multi.on('click', '.filter_on', function () {
        $products_list_body.find('.filter_on').each(function () {
            var $scope = $(this);
            if ($scope.closest('.product-row').find('.check_single').prop('checked')) {
                $scope.trigger('click');
            }
        });
    });

    $header_row_multi.on('click', '.sort-name.accessory_off', function () {
        $products_list_body.find('.sort-name.accessory_off').each(function () {
            var $scope = $(this);
            if ($scope.closest('.product-row').find('.check_single').prop('checked')) {
                $scope.trigger('click');
            }
        });
    });
    
    $header_row_multi.on('click', '.sort-name.accessory_on', function () {
        $products_list_body.find('.sort-name.accessory_on').each(function () {
            var $scope = $(this);
            if ($scope.closest('.product-row').find('.check_single').prop('checked')) {
                $scope.trigger('click');
            }
        });
    });

    $products_list_body.on('click', '.select_all_accessory_off', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.product-row');
        $handler.find('.accessories_off').prop('checked', $scope.prop('checked'));
    });

    $products_list_body.on('click', '.select_all_accessory_on', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.product-row');
        $handler.find('.accessories_on').prop('checked', $scope.prop('checked'));
    });

    $products_list_body.on('click', '.accessories_next', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.product-row');
        var $filter = $handler.find('.accessories_filter');
        $filter.data('page', parseInt($filter.data('page')) + 1);
        $filter.trigger('click');
    });

    $products_list_body.on('click', '.accessories_back', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.product-row');
        var $filter = $handler.find('.accessories_filter');
        $filter.data('page', parseInt($filter.data('page')) - 1);
        $filter.trigger('click');
    });
    
    $products_list_body.on('click', '.accessories_number', function() {
        var $scope = $(this);
        var $handler = $scope.closest('.product-row');
        var $filter = $handler.find('.accessories_filter');
        $filter.data('page', parseInt($scope.data('page')));
        $filter.trigger('click');
    });

    $products_list_body.on('click', '.select_all_accessory_mirror_off', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.product-row');
        $handler.find('.accessories_mirror_off').prop('checked', $scope.prop('checked'));
    });

    $products_list_body.on('click', '.select_all_accessory_mirror_on', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.product-row');
        $handler.find('.accessories_mirror_on').prop('checked', $scope.prop('checked'));
    });

    $products_list_body.on('click', '.filter_off', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.td-element-inside');
        $.ajax({
            data: {
                categories: $main.find('.filter_category_element').map(function () {
                    var $scope = $(this);
                    return $scope.prop('checked') ? $(this).val() : null;
                }).get(),
                manufacturer: $main.find('.filter_manufacturer_element').map(function () {
                    var $scope = $(this);
                    return $scope.prop('checked') ? $(this).val() : null;
                }).get(),
                product: $main.find('.filter_product_name').val(),
                price: $main.find('.filter_range_price').val(),
                quantity: $main.find('.filter_range_quantity').val(),
                weight: $main.find('.filter_range_weight').val(),
                date_add_from: $main.find('.filter_date_add_from').val(),
                date_add_to: $main.find('.filter_date_add_to').val(),
                date_update_from: $main.find('.filter_date_update_from').val(),
                date_update_to: $main.find('.filter_date_update_to').val(),
                active: $main.find('.filter_active').val(),
                categories_default: $main.find('.filter-category-default-input').map(function () {
                    var $scope = $(this);
                    return $scope.prop('checked') ? $(this).val() : null;
                }).get(),
                shop: $main.find('.filter_shop').val(),
                promotion: $main.find('.filter_promotion').val(),
                show_empty: $main.find('.filter_show_empty').val(),
                page: $scope.data('page'),
                elements: 20,
                filter: true,
                filter_name: 'filterOff',
                is_ajax: true,
                id_product: $scope.closest('.product-row').attr('p-id'),
                sort_name: $handler.find('.sort-name').prop('checked') ? 1 : 0
            },
            type: 'POST',
            dataType: 'json',
            success: function ($response)
            {
                $handler.html($response.content);
            },
            error: function ()
            {
                $.notify(serverError, 'error', {
                    autoHideDelay: 2000
                });
            }
        });
    });

    $products_list_body.on('click', '.filter_on', function () {
        var $scope = $(this);
        var $handler = $scope.closest('.td-element-inside');
        $.ajax({
            data: {
                categories: $main.find('.filter_category_element').map(function () {
                    var $scope = $(this);
                    return $scope.prop('checked') ? $(this).val() : null;
                }).get(),
                manufacturer: $main.find('.filter_manufacturer_element').map(function () {
                    var $scope = $(this);
                    return $scope.prop('checked') ? $(this).val() : null;
                }).get(),
                product: $main.find('.filter_product_name').val(),
                price: $main.find('.filter_range_price').val(),
                quantity: $main.find('.filter_range_quantity').val(),
                weight: $main.find('.filter_range_weight').val(),
                date_add_from: $main.find('.filter_date_add_from').val(),
                date_add_to: $main.find('.filter_date_add_to').val(),
                date_update_from: $main.find('.filter_date_update_from').val(),
                date_update_to: $main.find('.filter_date_update_to').val(),
                active: $main.find('.filter_active').val(),
                categories_default: $main.find('.filter-category-default-input').map(function () {
                    var $scope = $(this);
                    return $scope.prop('checked') ? $(this).val() : null;
                }).get(),
                shop: $main.find('.filter_shop').val(),
                promotion: $main.find('.filter_promotion').val(),
                show_empty: $main.find('.filter_show_empty').val(),
                page: $scope.data('page'),
                elements: 20,
                filter: true,
                filter_name: 'filterOn',
                is_ajax: true,
                id_product: $scope.closest('.product-row').attr('p-id'),
                sort_name: $handler.find('.sort-name').prop('checked') ? 1 : 0
            },
            type: 'POST',
            dataType: 'json',
            success: function ($response)
            {
                $handler.html($response.content);
            },
            error: function ()
            {
                $.notify(serverError, 'error', {
                    autoHideDelay: 2000
                });
            }
        });
    });
});