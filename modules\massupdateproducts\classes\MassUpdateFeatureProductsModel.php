<?php
/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */

class MassUpdateFeatureProductsModel extends MassUpdateProductsAbstract
{

	public function __construct(&$module, &$object, &$context)
	{
		$this->settings_name = Tools::strtoupper('MassUpdateFeatureProductsModel');
		parent::__construct($module, $object, $context);

		$features = Feature::getFeatures(Context::getContext()->language->id);

		if ($features)
			foreach ($features as $feature)
			{
				//feature
				$this->fields['feature_'.$feature['id_feature']] = array(
					'display_name' => $feature['name'],
					'name' => 'feature_'.$feature['id_feature'],
					'active' => true,
					'type' => self::TYPE_SEL_CUST
				);

				$feature_value_arr = FeatureValue::getFeatureValuesWithLang(Context::getContext()->language->id, $feature['id_feature'], true);
				$features_values = array(
					0 => $this->module->l('none', $this->languages_name)
				);
				if ($feature_value_arr)
					foreach ($feature_value_arr as $feature_value) $features_values[$feature_value['id_feature_value']] = $feature_value['value'];

				$this->fields['feature_'.$feature['id_feature']]['select'] = $features_values;
			}
	}

	public function save(array $data)
	{
		$result = array();
		$end = false;
		if ($data) {
            foreach ($data as $index => $params) {
                $result[$index] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );

                $tmp = explode('-', $index);
                $id_product = isset($tmp[0]) && Validate::isInt($tmp[0]) ? (int)$tmp[0] : 0;
                $id_shop = isset($tmp[1]) && Validate::isInt($tmp[1]) ? (int)$tmp[1] : null;

                $product_params = array_key_exists('data', $params) ? $params['data'] : null;
                $product = new Product($id_product, false, null, $id_shop);

                if (!Validate::isLoadedObject($product)) {
                    $result[$index]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                $product->id_shop_list = array($id_shop);
                $product->id_shop_object = $id_shop;

                if ($product_params) {
                    $feature_error = false;
                    if (($features = Feature::getFeatures(Context::getContext()->language->id))) {
                        foreach ($features as $feature) {
                            if (!isset($product_params['feature_' . $feature['id_feature']]))
                                continue;
                            Db::getInstance()->execute('DELETE FROM `' .
                                _DB_PREFIX_ . 'feature_product` WHERE `id_product` = ' . ((int)$product->id) . ' AND id_feature = ' . ((int)$feature['id_feature']));
                            if ($feature_error) {
                                continue;
                            }
                            $lang = false;
                            $lang_arr = array();
                            $id_feature_value = null;
                            foreach ($this->object->languages as $language) {
                                if (isset($product_params['feature_' . $feature['id_feature'] . '_' . $language['iso_code']])
                                    && $product_params['feature_' . $feature['id_feature'] . '_' . $language['iso_code']]) {
                                    $lang = true;
                                }
                                $lang_arr[$language['id_lang']] = $product_params['feature_' . $feature['id_feature'] . '_' . $language['iso_code']];
                            }

                            if ($lang) {
                                $feature_value = new FeatureValue();
                                $feature_value->custom = 1;
                                $feature_value->value = $lang_arr;
                                $feature_value->id_feature = $feature['id_feature'];

                                $errors = $feature_value->validateFields(false, true);
                                $errors2 = $feature_value->validateFieldsLang(false, true);

                                if ($errors !== true || $errors2 !== true) {
                                    $result[$index]['message'] = '';
                                    if ($errors !== true) {
                                        $result[$index]['message'] .= '<p style="color: #FFF;">' . (is_bool($errors) ?
                                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)) . '</p>';
                                    }
                                    if ($errors2 !== true) {
                                        $result[$index]['message'] .= '<p style="color: #FFF;">' . (is_bool($errors2) ?
                                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)) . '</p>';
                                    }
                                    $feature_error = true;
                                    continue;
                                } else {
                                    if ($feature_value->add())
                                        $id_feature_value = $feature_value->id;
                                    else {
                                        $result[$index]['message'] = $this->module->l('Problem with added feature', $this->languages_name);
                                        $feature_error = true;
                                        continue;
                                    }
                                }
                            } elseif (isset($product_params['feature_' . $feature['id_feature']]) && $product_params['feature_' . $feature['id_feature']])
                                $id_feature_value = isset($product_params['feature_' . $feature['id_feature']]) ? $product_params['feature_' . $feature['id_feature']] : 0;
                            if ($id_feature_value)
                                $product->addFeaturesToDB($feature['id_feature'], $id_feature_value);
                        }
                    }

                    $errors = $product->validateFields(false, true);
                    if ($feature_error)
                        continue;
                    elseif ($errors !== true) {
                        $result[$index]['message'] = '<p style="color: #FFF;">' . (is_bool($errors) ?
                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)) . '</p>';
                        continue;
                    } else {
                        if ($product->update()) {
                            $result[$index]['error'] = false;
                            $this->__construct($this->module, $this->object, $this->context);
                            $result[$index]['message'] = $this->displayProduct($product);
                        } else {
                            $result[$index]['message'] = $this->module->l('Problem with update', $this->languages_name);
                            continue;
                        }
                    }
                } else {
                    $result[$index]['error'] = false;
                    $result[$index]['message'] = $this->displayProduct($product);
                }
            }
        } else {
            $end = true;
        }

		return array(
			'raport' => $result,
			'end' => $end
		);
	}

	public function display($result)
	{
		$ids_product = $result['result'];
		$result['result'] = '';
		$result['table'] = true;

		if ($ids_product) {
            foreach ($ids_product as $product_arr) {
                $product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['dates']['products_count']--;
                    continue;
                }
                $product->id_shop_object = $product_arr['id_shop'];
                $result['result'] .= $this->displayProduct($product);
            }
        }

		return $result;
	}

	public function displayProduct(&$product)
	{
		parent::displayProduct($product);
		$product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
						$this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
		$product->quantity = $this->object->shop_group->share_stock ? $product->quantity : StockAvailable::getQuantityAvailableByProduct($product->id);
		$product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
		$product->full_name = $product->name[$this->context->language->id];
		$features_arr = Feature::getFeatures($this->context->language->id);

		if ($features_arr) {
            foreach ($features_arr as $feature) {
                $this->fields['feature_' . $feature['id_feature']]['value'] = 0;
            }
        }

		if (($features = $product->getFeatures())) {
            foreach ($features as $feature) {
                $this->fields['feature_' . $feature['id_feature']]['value'] = $feature['id_feature_value'];
            }
        }

		$this->context->smarty->assign(array(
			'product' => $product,
			'fields' => $this->getFields(),
			'languages' => $this->object->languages
		));
//dump($this->fields);
		return $this->object->createTemplate('tr-product.tpl')->fetch();
	}

	public function displayCombination(&$product, &$combination)
	{
		if (!parent::displayCombination($product, $combination)) {
            return '';
        }
	}

}
