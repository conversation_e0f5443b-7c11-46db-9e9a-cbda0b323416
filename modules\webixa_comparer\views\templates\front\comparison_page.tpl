{extends file='page.tpl'}

{block name='page_title'}
    {l s='Product Comparison' d='Modules.WebixaComparer.Shop'}
{/block}

{block name='page_content_container'}
<div class="page-content container">
    <div class="product-comparison">
        {if empty($products)}
            <div class="alert alert-info">
                {l s='No products to compare' d='Modules.WebixaComparer.Shop'}
                <a href="{$urls.pages.index}" class="btn btn-primary mt-3">
                    {l s='Continue shopping' d='Shop.Theme.Actions'}
                </a>
            </div>
        {else}
            <div class="mb-4 flex justify-end">
                <button id="clear-comparison" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                    {l s='Clear All' d='Modules.WebixaComparer.Shop'}
                </button>
            </div>
            
            <div class="product-container overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 shadow-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{l s='Feature' d='Modules.WebixaComparer.Shop'}</th>
                            {foreach from=$products item=product}
                                <th class="px-6 py-3 text-center relative">
                                    <button class="absolute top-1 right-1 text-red-500 remove-product" 
                                            data-product-id="{$product.id_product}">×</button>
                                    <a href="{$product.url}" class="block">
                                        <img src="{$product.image}" alt="{$product.name}" class="mx-auto w-24 h-24 object-contain mb-2" width="400" height="400" />
                                        <h3 class="text-sm font-semibold">{$product.name}</h3>
                                    </a>
                                </th>
                            {/foreach}
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
						<tr>
                            <td></td>
                            {foreach from=$products item=product}
                                <td class="text-center">
									<div class="add tw-p-3">
										<form action="{$urls.pages.cart}" method="post" class="add-to-cart-or-refresh">
											<input type="hidden" name="token" value="{$static_token}">
											<input type="hidden" name="id_product" value="{$product.id_product}">
											<input type="hidden" name="qty" value="1">
											<button class="btn btn-primary add-to-cart" data-button-action="add-to-cart" type="submit">
												{l s='Add to cart' d='Shop.Theme.Actions'}
											</button>
										</form>
									</div>
                                </td>
                            {/foreach}
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{l s='Price' d='Shop.Theme.Catalog'}</td>
                            {foreach from=$products item=product}
                                <td class="px-6 py-4 text-center">
                                    <div class="text-lg font-bold text-green-600">{$product.price}</div>
                                    {if isset($product.specific_prices) && $product.specific_prices}
                                        <div class="text-sm text-red-500">-{$product.specific_prices.discount}</div>
                                    {/if}
                                </td>
                            {/foreach}
                        </tr>
                        {if $features && count($features) > 0}
                            {foreach from=$features item=feature}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{$feature.name}</td>
                                    {foreach from=$products item=product}
                                        <td class="px-6 py-4 text-center">
                                            {if isset($product.features[$feature.id_feature])}
                                                {$product.features[$feature.id_feature]}
                                            {else}
                                                —
                                            {/if}
                                        </td>
                                    {/foreach}
                                </tr>
                            {/foreach}
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {     
        var ajaxUrl = '{$ajax_url|escape:'javascript'}';
                
        document.getElementById('clear-comparison').addEventListener('click', function() {
            fetch(ajaxUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'clear'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                }
            });
        });
                
        document.querySelectorAll('.remove-product').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                
                fetch(ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'remove',
                        productId: productId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    }
                });
            });
        });


    });
</script>
{/block}
