<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

class Webixa<PERSON>ontentSlide extends ObjectModel
{
    const _TYPE_IMG_DIR_ = 'img/slides';
    const _IMG_DIR_ = _PS_MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    const _IMG_PATH_ = _MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    const _IMG_DIR_MOBILE_ = _PS_MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/mobile_';
    const _IMG_PATH_MOBILE_ = _MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/mobile_';
    public $image_dir = self::_IMG_DIR_;
    public $image_dir_mobile = self::_IMG_DIR_MOBILE_;

    public $id_webixa_content_block;
    public $image = false;
    public $image_mobile = false;
    public $position;
    public $is_gradient = false;
    public $active = true;
    public $title;
    public $description;
    public $button_text;
    public $link;

    public $image_format;
    public $image_format_mobile;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'webixa_content_slide',
        'primary' => 'id_webixa_content_slide',
        'multilang' => true,
        'fields' => [
            'id_webixa_content_block' => ['type' => self::TYPE_INT, 'validate' => 'isInt', 'required' => true],
            'position' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => false],
            'is_gradient' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            /* Lang fields */
            'title' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml', 'size' => 128],
            'description' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml', 'size' => 3999999999999],
            'button_text' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 128],
            'link' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isUrl', 'size' => 512],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null, $translator = null)
    {
        if (version_compare(_PS_VERSION_, '1.7.1', '<')) {
            parent::__construct($id, $id_lang, $id_shop);
        } else {
            parent::__construct($id, $id_lang, $id_shop, $translator);
        }

        $this->image_format = $this->getImageExtensionInDir($this->id, self::_IMG_DIR_);

        if (file_exists(self::_IMG_DIR_ . $this->id . '.' . $this->image_format)) {
            $this->image = self::_IMG_PATH_ . $this->id . '.' . $this->image_format;
        }

        $this->image_format_mobile = $this->getImageExtensionInDir($this->id, self::_IMG_DIR_, 'mobile_');

        if (file_exists(self::_IMG_DIR_MOBILE_ . $this->id . '.' . $this->image_format_mobile)) {
            $this->image_mobile = self::_IMG_PATH_MOBILE_ . $this->id . '.' . $this->image_format_mobile;
        }
    }

    public function getImageExtensionInDir($id, $imageDir, $prefix = '')
    {
        $image = false;

        if (is_dir($imageDir)) {
            $filesInFolder = scandir($imageDir);

            foreach ($filesInFolder as $file) {
                $fileNameParts = explode('.', $file);
                $fileName = reset($fileNameParts);

                if ($fileName === ($prefix ?: '') .$id) {
                    $image = $imageDir . $file;
                    break;
                }
            }

            $imageParts = explode('.', $image);
            return end($imageParts);
        }

        return false;
    }

    public function add($auto_date = true, $null_values = false)
    {
        $this->position = static::getLastPosition($this->id_webixa_content_block);

        return parent::add($auto_date, $null_values);
    }

    public function delete()
    {
        if (parent::delete()) {
            return static::cleanPositions($this->id_webixa_content_block);
        }

        return false;
    }

    public function deleteImage($force_delete = false, $type = 'all')
    {
        if ($force_delete || !$this->hasMultishopEntries()) {
            if ('image_mobile' !== $type) {
                foreach (Shop::getShops(false, null, true) as $id_shop) {
                    if (
                        file_exists(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                        && !unlink(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                    ) {
                        return false;
                    }
                }
            }
            if (in_array($type, ['all', 'image_mobile'])) {
                if (
                    file_exists(self::_IMG_DIR_MOBILE_ . $this->id . '.' . $this->image_format_mobile)
                    && !unlink(self::_IMG_DIR_MOBILE_ . $this->id . '.' . $this->image_format_mobile)
                ) {
                    return false;
                }
                if (
                    file_exists(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mobile_' . (int) $this->id . '.' . $this->image_format_mobile)
                    && !unlink(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mobile_' . (int) $this->id . '.' . $this->image_format_mobile)
                ) {
                    return false;
                }
            }
        }
        if ('image_mobile' === $type) {
            return true;
        }
        return parent::deleteImage($force_delete);
    }

    public static function updatePositions(array $positions)
    {
        // todo: check if this work properly
        $query = 'UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '` SET `position` = CASE `' . static::$definition['primary'] . '` ';

        foreach ($positions as $pos => $args) {
            preg_match('/tr_\d+_(\d+)_\d+/', $args, $matches);
            if (!empty($matches[1])) {
                $query .= 'WHEN ' . $matches[1] . ' THEN ' . $pos . ' ';
            }
        }

        $query .= 'ELSE `position` END WHERE 1';

        return Db::getInstance()->execute($query);
    }

    public static function cleanPositions($blockId)
    {
        return Db::getInstance()->execute('
            UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '` psi1
            JOIN (
                SELECT `' . static::$definition['primary'] . '`, @i := @i+1 new_position
                FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`, (select @i:=-1) temp
                WHERE `' . WebixaContentBlock::$definition['primary'] . '`=' . (int)$blockId . '
                ORDER BY position asc
            ) psi2 ON psi1.`' . static::$definition['primary'] . '` = psi2.`' . static::$definition['primary'] . '`
            SET psi1.position = psi2.new_position
        ');
    }

    public static function getLastPosition($blockId)
    {
        $sql = '
		SELECT MAX(`position`)
		FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`
		WHERE `' . WebixaContentBlock::$definition['primary'] . '`=' . (int)$blockId;
        $value = Db::getInstance()->getValue($sql);

        return null === $value ? 0 : (int) $value + 1;
    }

    public static function getActiveByWebixaContentBlockId($idBlock, $idLang = null, $idShop = null)
    {
        $slides = [];
        $query = new DbQuery();
        $query->select('a.' . static::$definition['primary']);
        $query->from(static::$definition['table'], 'a');
        $query->where('a.active=' . 1);
        $query->where('a.' . WebixaContentBlock::$definition['primary'] . '=' . (int)$idBlock);

        if (null !== $idShop) {
            $query->innerJoin(
                static::$definition['table'] . '_shop',
                'sa',
                'a.' . static::$definition['primary'] . '=' . 'sa.' . static::$definition['primary'] . ' AND sa.id_shop=' . (int) $idShop
            );
        }
        if (null !== $idLang) {
            $query->innerJoin(
                static::$definition['table'] . '_lang',
                'al',
                'a.' . static::$definition['primary'] . '=' . 'al.' . static::$definition['primary'] . ' AND al.id_lang=' . (int) $idLang
            );

            $query->where('al.title IS NOT NULL AND al.title<>""');
        }

        $query->orderBy('a.position ASC');
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        foreach ($result as $row) {
            $webixaContentSlide = new WebixaContentSlide($row[static::$definition['primary']], $idLang, $idShop);
            if (Validate::isLoadedObject($webixaContentSlide)) {
                $slides[] = $webixaContentSlide;
            }
        }

        return $slides;
    }
}
