.slide {
    float: left;
    width: 25%;
    display: inline;
    position: relative;
}

.slide .slide-content {
    display: block;
    overflow: hidden;
    position: absolute;
    top: 0px;
    left: 50px;
}

.slide .slide-content .slide-content-left {
    display: inline-block;
    position: relative;
    overflow: hidden;
    width: 50px;
    height: 15px; 
}

.slide .slide-content .slide-content-right {
    font-size: 16px;
    color: #FF1E1E;
    display: inline-block;
    position: relative;
    overflow: hidden;
    width: 250px;
    height: 20px;
}

.slide .slide-button {
    cursor:pointer;
    overflow: hidden;
    margin-left: 5px;
    display: block;
    position: relative;
    width: 30px;
    height: 30px; 
}


.menu-list {
    box-sizing: border-box;
    overflow: hidden;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
    display: none;
    position: relative;
    z-index: 10000;
}

.menu-list .menu-list-element {
    display: block;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    padding: 10px;
    background-color: #FFF;
    border-bottom: 1px solid #D3D8DB;
    color: #000;
    text-decoration: none;
    min-width: 25%;
}

.menu-list .menu-list-element:hover {
    text-decoration: none;
    color: #00AFF0;
}

.menu-list .menu-list-element.active {
    background-color: #00AFF0;
    color: #FFF;
}

.massbtn {
    font-size: 12px !important;
    background-color: #FFF;
    border: 1px solid #D3D8DB;
    height: 30px;
    line-height: 30px !important;
    border-radius: 5px;
    cursor: pointer;
}

.checkbox {
    display: block;
    position: relative;
    overflow: hidden;
    margin-bottom: 5px;
    background-color: #FFF;
    cursor: move;
    padding: 10px;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
}

.backup {
    float: right;
}

.filter-panel-element-content input[type=text] {
    width: 40%;
    display: inline;
    margin: 0 5px;
}

#massupdateproducts-fields .fields-panel .checkbox {
    padding-left: 40px;
}
