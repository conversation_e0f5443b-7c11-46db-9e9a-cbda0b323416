<link rel="stylesheet" type="text/css" href="{$module_url}views/css/admin.css">

<div class="panel">
    <div class="panel-heading">
        <i class="icon-cogs"></i>
        {l s='Ustawienia modułu' mod='sortproductcategory'}
    </div>
    
    <div class="panel-body">
        <form method="post" action="{$admin_url}&tab=settings">
            <div class="row">
                <div class="col-lg-6">
                    <div class="form-group">
                        <label class="control-label">{l s='Liczba produktów na stronie:' mod='sortproductcategory'}</label>
                        <input type="number" name="items_per_page" class="form-control" value="{$items_per_page}" min="10" max="200">
                        <p class="help-block">{l s='Określa ile produktów będzie wyświetlanych na jednej stronie (10-200)' mod='sortproductcategory'}</p>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="form-group">
                        <label class="control-label">{l s='Opcje funkcjonalności:' mod='sortproductcategory'}</label>
                        
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="enable_search" value="1" {if $enable_search}checked{/if}>
                                {l s='Włącz wyszukiwanie produktów' mod='sortproductcategory'}
                            </label>
                        </div>
                        
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="enable_bulk" value="1" {if $enable_bulk}checked{/if}>
                                {l s='Włącz operacje grupowe' mod='sortproductcategory'}
                            </label>
                        </div>
                        
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="auto_save" value="1" {if $auto_save}checked{/if}>
                                {l s='Automatyczne zapisywanie zmian' mod='sortproductcategory'}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-12">
                    <div class="alert alert-info">
                        <h4>{l s='Informacje o module:' mod='sortproductcategory'}</h4>
                        <ul>
                            <li>{l s='Moduł pozwala na zaawansowane zarządzanie kolejnością produktów w kategoriach' mod='sortproductcategory'}</li>
                            <li>{l s='Obsługuje duże ilości produktów dzięki paginacji i wyszukiwaniu' mod='sortproductcategory'}</li>
                            <li>{l s='Umożliwia operacje grupowe dla szybkiego przenoszenia wielu produktów' mod='sortproductcategory'}</li>
                            <li>{l s='Pozwala na drag&drop oraz ręczne wprowadzanie pozycji' mod='sortproductcategory'}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{l s='Wskazówki dotyczące wydajności' mod='sortproductcategory'}</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h5>{l s='Dla kategorii z dużą liczbą produktów:' mod='sortproductcategory'}</h5>
                                    <ul>
                                        <li>{l s='Użyj wyszukiwania aby znaleźć konkretne produkty' mod='sortproductcategory'}</li>
                                        <li>{l s='Wykorzystaj operacje grupowe zamiast przeciągania' mod='sortproductcategory'}</li>
                                        <li>{l s='Zmniejsz liczbę produktów na stronie dla lepszej wydajności' mod='sortproductcategory'}</li>
                                    </ul>
                                </div>
                                <div class="col-lg-6">
                                    <h5>{l s='Optymalizacja pracy:' mod='sortproductcategory'}</h5>
                                    <ul>
                                        <li>{l s='Sortuj produkty według nazwy lub kodu przed zmianą pozycji' mod='sortproductcategory'}</li>
                                        <li>{l s='Użyj funkcji "Uporządkuj pozycje" aby usunąć luki' mod='sortproductcategory'}</li>
                                        <li>{l s='Zapisuj zmiany regularnie przy dużych operacjach' mod='sortproductcategory'}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-12">
                    <button type="submit" name="submitSettings" class="btn btn-primary">
                        <i class="icon-save"></i>
                        {l s='Zapisz ustawienia' mod='sortproductcategory'}
                    </button>
                    
                    <a href="{$admin_url}&tab=category_select" class="btn btn-default">
                        <i class="icon-arrow-left"></i>
                        {l s='Powrót do wyboru kategorii' mod='sortproductcategory'}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
