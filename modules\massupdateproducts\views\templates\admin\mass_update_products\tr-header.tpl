{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

{assign var='temp' value='head'}
<div class="header-row filter">
    {assign var=counter_tmp value=486}
    <div class="td-element"  style="width: 25px;"></div>
    <div class="td-element"  style="width: 27px;"></div>
    <div class="td-element" style="width: 27px;text-align: center;">
        {l s='ID' mod='massupdateproducts'}
        <p>
            <span sort-name="ps.id_product">
                <span class="sort-link down">
                    <i class="fa fa-caret-down"></i>
                </span>
                <span class="active sort-link up">
                    <i class="fa fa-caret-up"></i>
                </span>
            </span>
        </p>
    </div>
    <div class="td-element"  style="width: 114px;">
        {l s='Image' mod='massupdateproduc<PERSON>'}
    </div>
    <div class="td-element"  style="width: 208px;">
        {l s='Name' mod='massupdateproducts'}&nbsp;[{l s='Code' mod='massupdateproducts'}]
        <p>
            <span sort-name="pl.name">
                <span class="sort-link down">
                    <i class="fa fa-caret-down"></i>
                </span>
                <span class="sort-link up">
                    <i class="fa fa-caret-up"></i>
                </span>
            </span>
        </p>
    </div>
    {if $fields}
        {foreach $fields as $field}
            {if $field['active']}
                {if $field['type'] eq 7}
                    {$counter_tmp=$counter_tmp+200}
                {else}
                    {$counter_tmp=$counter_tmp+$widths[$field['type']]}
                {/if}
                {$counter_tmp=$counter_tmp+10}
                {assign var=file value=$field['type']}
                <div class="td-element" style="width: {if $field['type'] eq 7}200px{else if $field['type'] eq 12}auto{else}{$widths[$field['type']]}px{/if}">
                    {if $file eq '7'}
                        <span style="float: left;">
                            {$field['display_name']|strval}&nbsp;{l s='excl' mod='massupdateproducts'}.
                        </span>
                        <span style="float: right;">
                            {$field['display_name']|strval}&nbsp;{l s='incl' mod='massupdateproducts'}.
                        </span>
                    {else}
                        {$field['display_name']|strval}
                    {/if}
                    {if $field['sort']}
                        <p style="display: block;position: relative;overflow: hidden;">
                            <span sort-name="{$field['base_name']|strval}">
                                <span class="sort-link down">
                                    <i class="fa fa-caret-down"></i>
                                </span>
                                <span class="sort-link up">
                                    <i class="fa fa-caret-up"></i>
                                </span>
                            </span>
                        </p>
                    {/if}
                </div>
            {/if}
        {/foreach}
    {/if}
    <div class="td-element"  style="width: 25px;"></div>
</div>
<div class="header-row-multi filter">
    <div class="td-element"  style="width: 25px;">
	<div class="td-element-inside">
	    {if isset($has_combination) && $has_combination }<i id="multi-get-prod" style="cursor: pointer;" class="fa fa-plus"></i>{/if}
	</div>
    </div>
    <div class="td-element"  style="width: 27px;">
	<div class="td-element-inside">
	    <input type="checkbox" checked class="check_multi" />
	</div>
    </div>
    <div class="td-element" style="width: 27px;">
    </div>
    <div class="td-element"  style="font-size: 14px; width: 114px;">
	<div class="td-element-inside">
	    {l s='Mass update' mod='massupdateproducts'}
	</div>
    </div>
    <div class="td-element"  style="width: 208px;">
	<div class="td-element-inside">
	    <span class="buttons-ph-panel" id="page-header-desc-configuration-save">
		<i class="fa fa-floppy-o"></i>&nbsp;{l s='Save' mod='massupdateproducts'}
	    </span>
	    <br />
	    <span class="buttons-ph-panel" id="page-header-desc-configuration-delete">
		<i class="fa fa-times"></i>&nbsp;{l s='Remove' mod='massupdateproducts'}
	    </span>
	</div>
    </div>
    {assign var='counter' value='1'}
    {if $fields}
        {foreach $fields as $field}
            {if $field['active']}
                {assign var='counter' value=$counter + 1}
                {assign var=file value=$field['type']}
                <div  class="td-element" style="width: {if $field['type'] eq 7}200px{else if $field['type'] eq 12}auto{else}{$widths[$field['type']]}px{/if}">
		    <div class="td-element-inside" style="{if $field['type'] neq 0}text-align: center;{/if} width: {if $field['type'] eq 7}200px{else if $field['type'] eq 12}auto{else}{$widths[$field['type']]}px{/if}">
			{if $field['display']}
			    {if $field['lang']}
				{foreach $languages as $language}
				    <span class="field_handler{if $field['type'] eq 12} field_handler_tiny{/if}">
					{include file="./fields/field_$file.tpl" is_header=1 select=$field['select'] name=$field['name'] value='' lang=$language['iso_code'] class_mass='multi' extra=$field['extra']  validate=$field['validate'] attr=$field['attr']}
					<span  class="field_handler_flag"><img title="{$language['name']|strval}" style="width: 16px; height: 10px;" src="{$img_lang_src|strval}{$language['id_lang']|intval}.jpg" /></span>
				    </span>
				{/foreach}
			    {else}
				{include file="./fields/field_$file.tpl" select=$field['select'] name=$field['name'] value=$field['value'] extra=$field['extra'] class_mass='multi' validate=$field['validate'] attr=$field['attr']}
			    {/if}
			{/if}
		    </div>
                </div>
            {/if}
        {/foreach}
    {/if}
    <div class="td-element"  style="width: 25px;"></div>
</div>
<style>
    {literal}
        .ph-panel .header-row,
        .ph-panel .header-row-multi,
        .ph-panel .product-row,
        .ph-panel .product-row-mask,
        .ph-panel .product-row-raport,
        .ph-panel .combination-row,
        .ph-panel .combination-row-raport {
            width: {/literal}{$counter_tmp}{literal}px;
        }
    {/literal}
</style>