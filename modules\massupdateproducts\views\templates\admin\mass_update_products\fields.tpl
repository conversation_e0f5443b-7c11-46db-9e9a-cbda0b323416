{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<div class="ph-panel" id="massupdateproducts-fields">
    <div class="fields-mask"></div>
    <div class="fields-loading">{l s='Loading' mod='massupdateproducts'}...</div>
    <div class="ph-panel-head">
        <div class="ph-panel-head-main">
            <button class="massbtn show_checkbox">
                <i class="fa fa-plus-square"></i>&nbsp;{l s='Show' mod='massupdateproducts'}
            </button>
            <button class="massbtn hide_checkbox hidden">
                <i class="fa fa-minus-square"></i>&nbsp;{l s='Hide' mod='massupdateproducts'}
            </button>
            {l s='Fields' mod='massupdateproducts'} 
        </div>
    </div>
    <div class="fields-panel hidden ph-panel-content">
        {if $fields}
            {foreach $fields as $field}
                <div class="checkbox">
                    <input type="checkbox" name="{$field['name']|strval}" id="{$field['name']|strval}" class="field" {if $field['active']} checked="checked" {/if} />
                    <input class="field_position" type="hidden" name="{$field['name']|strval}_position" value="{if isset($field['position']) && $field['position']}{$field['position']|intval}{/if}" />
                    {$field['display_name']|strval}
                </div>
            {/foreach}
        {/if}
        <button class="massbtn save-fields">
            <i class="fa fa-floppy-o"></i>&nbsp;{l s='Save' mod='massupdateproducts'}
        </button>
    </div>
</div>