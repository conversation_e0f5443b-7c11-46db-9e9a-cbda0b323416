<?php

require_once __DIR__.'/../../classes/AuthDSMassUpdate.php';
require_once __DIR__.'/../../classes/Backup_Database.php';
require_once __DIR__.'/../../classes/Restore_Database.php';

class AdminMassUpdateProductsBackUpController extends ModuleAdminController
{
    private $module_name = 'massupdateproducts';

    public function __construct()
    {
        $this->bootstrap = true;
        $this->className = 'AdminMassUpdateProductsBackUp';
        parent::__construct();
    }

    private function isActiveModule()
    {
        $shop = new Shop((int)$this->context->shop->id);
        $auth = new AuthDSMassUpdate($this->module_name);
        $licence = $auth->isActive($shop->getBaseURL());
        if (empty($licence)) {
            $domain = AuthDSMassUpdate::clearDomain($shop->getBaseURL());
            $licence = $auth->getStaticLicence($domain, 'Masowa aktualizacja produktów', Configuration::get('MASSUPDATEPRODUCTS_LICENCE'));
        }
        return array(
            'active' => $licence,
            'actived' => $licence['licence']->licence
        );
    }

    public function initPageHeaderToolbar()
    {
        parent::initPageHeaderToolbar();
    }

    public function initHeader()
    {
        parent::initHeader();
    }

    public function initContent()
    {
        parent::initContent();

        $bqpList = Db::getInstance()->executeS('SELECT * FROM `'._DB_PREFIX_.'massupdateproduct_backup` ORDER BY `date_add` DESC');
        if (!empty($bqpList)) {
            $now = date('Y-m-d H:i:s');
            foreach ($bqpList as &$item) {
                $filesize = '--';
                if (file_exists($item['dir'].'/'.$item['file'].'.gz')) {
                    $filesize = filesize($item['dir'] . '/' . $item['file'].'.gz');
                }
                $item['size'] = number_format($filesize / 1000000, 2, '.', '');
                $target = new DateTime($now);
                $origin = new DateTime($item['date_add']);
                $interval = $origin->diff($target);
                if ($interval->d == 0 && $interval->m == 0 && $interval->y == 0) {
                    if ($interval->h == 0) {
                        $diff = '< 1h';
                    } else {
                        $diff = '> 1h';
                    }
                } else {
                    $diff = ' > '.$interval->days.' dni';
                }
                $item['diff'] = $diff;//$interval->format('%R%a days');
//                $diff2 = $interval->format('%i');
//                $item['diff2'] = $diff2;//$interval->format('%R%a days');
            }
        }
        $this->context->smarty->assign(array(
            'bqpList' => $bqpList,
        ));
    }

    public function postProcess()
    {
        parent::postProcess();

        if (Tools::getIsset('submitBackUp')) {
            error_reporting(E_ALL);
            set_time_limit(900); // 15 minutes

            $BACKUP_DIR = _PS_MODULE_DIR_.$this->module_name.'/backup';

            Db::getInstance()->insert('massupdateproduct_backup', array(
                'info' => '',
                'file' => '',
                'dir' => $BACKUP_DIR,
                'date_add' => date('Y-m-d H:i:s'),
                'date_upd' => date('Y-m-d H:i:s'),
            ));
            $id_backup = (int)Db::getInstance()->Insert_ID();

            if (php_sapi_name() != "cli") {
                echo '<div style="font-family: monospace;">';
            }

            $tables = array(
                _DB_PREFIX_.'accessory',
                _DB_PREFIX_.'category_product',
                _DB_PREFIX_.'feature_product',
                _DB_PREFIX_.'product',
                _DB_PREFIX_.'product_attachment',
                _DB_PREFIX_.'product_attribute',
                _DB_PREFIX_.'product_attribute_combination',
                _DB_PREFIX_.'product_attribute_image',
                _DB_PREFIX_.'product_attribute_shop',
                _DB_PREFIX_.'product_carrier',
                _DB_PREFIX_.'product_country_tax',
                _DB_PREFIX_.'product_download',
                _DB_PREFIX_.'product_group_reduction_cache',
                _DB_PREFIX_.'product_lang',
                _DB_PREFIX_.'product_sale',
                _DB_PREFIX_.'product_shop',
                _DB_PREFIX_.'product_supplier',
                _DB_PREFIX_.'product_tag',
                _DB_PREFIX_.'specific_price',
                _DB_PREFIX_.'stock',
                _DB_PREFIX_.'stock_available',
            );
            $tables = implode(',', $tables);
            $gzipBackupFile = true;
            $disableForeignKeyChecks = true;
            $batchSize = 1000;

            $backupDatabase = new Backup_Database(_DB_SERVER_, _DB_USER_, _DB_PASSWD_, _DB_NAME_, $id_backup, $BACKUP_DIR, $gzipBackupFile, $disableForeignKeyChecks, $batchSize, 'utf8');

            $result = $backupDatabase->backupTables($tables) ? 'OK' : 'KO';
            $backupDatabase->obfPrint('Backup result: ' . $result, 1);
            $output = $backupDatabase->getOutput();

            if (php_sapi_name() != "cli") {
                echo '</div>';
            }

            echo "Backup został wykonany - za 5 sek strona zostanie przeładowana.";
            echo '<meta http-equiv="refresh" content="5;url='.$_SERVER['HTTP_REFERER'].'" />';
            exit();
        } // end of backup
        if (Tools::getIsset('submitResetBase')) {
            $id_backup = (int)Tools::getValue('submitResetBase');
            if ($id_backup > 0) {
                $backup = Db::getInstance()->getRow('SELECT * FROM `'._DB_PREFIX_.'massupdateproduct_backup` WHERE `id_backup` = '.(int)$id_backup);
                if (!empty($backup)) {
                    $BACKUP_DIR = _PS_MODULE_DIR_.$this->module_name.'/backup';
                    $file = $backup['file'].'.gz';

                    error_reporting(E_ALL);
                    set_time_limit(900); // 15 minutes

                    if (php_sapi_name() != "cli") {
                        echo '<div style="font-family: monospace;">';
                    }

                    $restoreDatabase = new Restore_Database(_DB_SERVER_, _DB_USER_, _DB_PASSWD_, _DB_NAME_, $BACKUP_DIR, $file);
                    $result = $restoreDatabase->restoreDb($BACKUP_DIR, $file) ? 'OK' : 'NOK';
                    $restoreDatabase->obfPrint("Restoration result: ".$result, 1);

                    if ($result == 'OK') {
                        Db::getInstance()->update('massupdateproduct_backup', array(
                            'date_restore' => date('Y-m-d H:i:s'),
                        ), 'id_backup = '.(int)$id_backup);
                        echo "Tabele zostały przywrócone - za 5 sek strona zostanie przeładowana.";
                        echo '<meta http-equiv="refresh" content="5;url='.$_SERVER['HTTP_REFERER'].'" />';
                    } else {
                        echo "Wystąpił błąd. Spróbuj ponownie odświeżając stronę.";
                    }
                    if (php_sapi_name() != "cli") {
                        echo '</div>';
                    }
                    exit();
                }
            }
            Tools::redirect($_SERVER['HTTP_REFERER']);
        } // end of restore
        if (Tools::getIsset('SubmitDeleteBackUp')) {
            $id_backup = (int)Tools::getValue('SubmitDeleteBackUp');
            if ($id_backup > 0) {
                $backup = Db::getInstance()->getRow('SELECT * FROM `'._DB_PREFIX_.'massupdateproduct_backup` WHERE `id_backup` = '.(int)$id_backup);
                if (!empty($backup)) {
                    $BACKUP_DIR = _PS_MODULE_DIR_.$this->module_name.'/backup';
                    $file = $BACKUP_DIR.'/'.$backup['file'].'.gz';
                    if (unlink($file)) {
                        Db::getInstance()->delete('massupdateproduct_backup', 'id_backup = ' . (int)$id_backup);
                    }
                }
            }
            Tools::redirect($_SERVER['HTTP_REFERER']);
        } // end of delete
    }

    public function display()
    {
        $activeted = $this->isActiveModule();
        if ($activeted['actived'] == 1) {
            $tpl = 'backup.tpl';
        } else {
            $tpl = 'nolic.tpl';
        }
        $this->context->smarty->assign(array(
            'content' => $this->context->smarty->fetch($this->getTemplatePath().$tpl)
        ));
        parent::display();
    }

}
