<?php


/**
 * NOTICE OF LICENSE
 *
 * This file is licenced under the Software License Agreement.
 * With the purchase or the installation of the software in your application
 * you accept the licence agreement.
 *
 * You must not modify, adapt or create derivative works of this source code
 *
 *  <AUTHOR>
 *  @copyright 2019 PrestaHelp
 *  @license   LICENSE.txt
 */

class AuthDSMassUpdate
{

    private $url_hash;

    private $name;

    public function __construct($name = false)
    {
        $this->url_hash = 'aHR0cHM6Ly9tb2R1bGVzLnByZXN0YWhlbHAuY29tL3dzLw==';
        if ($name) {
            $this->name = $name;
        }
    }

    public function isActive($domain)
    {
        $url = base64_decode($this->url_hash).'getLicence';
        $data = array(
            'moduleName' => $this->name,
            'domain' => $domain
        );
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        return (array)json_decode(curl_exec($ch));
    }

    /**
     * @param $module_name
     * @param $module_version
     * @param $domain
     * @return bool
     */
    public function makeTools($module_name, $module_version, $domain)
    {
        $url = base64_decode($this->url_hash).'set';
        $data = array(
            'moduleName' => $module_name,
            'moduleVersion' => $module_version,
            'domain' => $domain,
            'date_add' => date('Y-m-d H:i:s')
        );
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        $response = curl_exec($ch);
        if (!$response) {
            return false;
        }
        return true;
    }

    /**
     * @return array
     */
    public function getBaners()
    {
        $url = base64_decode($this->url_hash).'getproducts';
        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
            $response = curl_exec($ch);
            curl_close($ch);
        } catch (Exception $e) {
            print_r($e);
        }
        $resp = (array)json_decode($response);
        return $resp;
    }

    /**
     * @return array
     */
    public function getAuthor()
    {
        $url = base64_decode($this->url_hash).'getAuthor';
        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
            $response = curl_exec($ch);
            curl_close($ch);
        } catch (Exception $e) {
            print_r($e);
        }
        $resp = (array)json_decode($response);
        $respons = array();
        if (!empty($resp)) {
            foreach ($resp as $r) {
                $respons[$r->name] = $r->value;
            }
        }
        return $respons;
    }

    /**
     * @return array|bool
     */
    public function getChangelog()
    {
        $url = base64_decode($this->url_hash).'getchangelog';
        $data = array(
            'module' => $this->name
        );
        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_VERBOSE, true);
            $response = curl_exec($ch);
        } catch (Exception $e) {
            print_r($e);
        }
        if (!$response) {
            return false;
        }
        return (array)json_decode($response);
    }

    /**
     * @return array|bool
     */
    public function getChangelogOther()
    {
        $url = base64_decode($this->url_hash).'getchangelogother';
        $data = array(
            'module' => $this->name
        );
        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_VERBOSE, true);
            $response = curl_exec($ch);
        } catch (Exception $e) {
            print_r($e);
        }
        if (!$response) {
            return false;
        }
        return (array)json_decode($response);
    }

    /**
     * @return array|bool
     */
    public function getCurrentModuleVersion()
    {
        $url = base64_decode($this->url_hash).'getCurrentModuleVersion';
        $data = array(
            'module' => $this->name
        );
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        $response = curl_exec($ch);
        if (!$response) {
            return false;
        }
        return (array)json_decode($response);
    }

    /**
     * @return bool|string|string[]
     */
    public static function getBanersHtml()
    {
        $url = "https://update.prestahelp.com/produkty.html";
        $ch = curl_init();
        $timeout = 5;
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        $html = curl_exec($ch);
        curl_close($ch);
        $html = str_replace('src="app/', 'src="https://update.prestahelp.com/app/', $html);
        return $html;
    }

    public function getStaticLicence($domain, $module_name, $licenceUser, $showLicence = null)
    {
        $time = 10;
        $key = 'pcmT8Fuwib';
        $module_number = 38;
        $licence = hash('sha512', $domain.(int)$time.$key.$module_name.(int)$module_number);
        Configuration::updateValue('MASSUPDATEPRODUCTS_LICENCE_GEN', $licence);
        Configuration::updateValue('MASSUPDATEPRODUCTS_LICENCE_GEN_DATA', $domain.'|'.$time.'|'.$key.'|'.$module_name.'|'.$module_number);
        $licences = array();
        if ($showLicence == 1) {
            echo $licenceUser.'<br />'.$licence.'<br />';
        }
        if ($licenceUser == $licence) {
            $licences['licence'] = new stdClass();
            $licences['licence']->licence = 1;
            $licences['licence']->date_expire_update = date('Y-m-d H:i:s', strtotime('+365 days'));
            $licences['licence']->date_expire_support = date('Y-m-d H:i:s', strtotime('+90 days'));
            $licences['licence']->date_expire = date('Y-m-d H:i:s', strtotime('+9999 days'));
            $licences['licence']->licence_update = 1;
            $licences['licence']->licence_support = 0;
            $licences['licence']->time = 10;
            $licences['licence']->time_upd = 7;
            $licences['licence']->date_expire_update_left = new stdClass();
            $licences['licence']->date_expire_update_left->left = '';
        }
        if ($showLicence == 1) {
            print_r($licences);
        }
        return $licences;
    }

    public static function clearDomain($domain) {
        $domain = str_replace('http://', '', $domain);
        $domain = str_replace('https://', '', $domain);
        $domain = str_replace('www.', '', $domain);
        $domain = str_replace('/', '', $domain);
        return $domain;
    }

}