<?php

class AdminSortProductCategoryController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->lang = false;

        parent::__construct();

        $this->meta_title = $this->l('Zarządzanie kolejnością produktów w kategoriach');
    }

    public function init()
    {
        parent::init();

        // Ensure module is loaded
        if (!$this->module) {
            $this->module = Module::getInstanceByName('sortproductcategory');
        }

        // Security checks
        $this->validateAccess();

        // CSRF protection for AJAX requests
        if (Tools::getValue('ajax')) {
            $this->validateAjaxToken();
        }
    }

    private function validateAccess()
    {
        // Check if user has permission to manage products
        if (!$this->access('edit')) {
            Tools::redirectAdmin($this->context->link->getAdminLink('AdminDashboard'));
        }

        // Additional check for catalog management
        if (!$this->tabAccess['edit']) {
            $this->errors[] = $this->l('Nie masz uprawnień do zarządzania produktami');
        }
    }

    private function validateAjaxToken()
    {
        $token = Tools::getValue('token');
        if (!$token || $token !== Tools::getAdminTokenLite('AdminSortProductCategory')) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowy token bezpieczeństwa']));
        }
    }

    private function validateCategoryAccess($id_category)
    {
        if (!$id_category || !is_numeric($id_category)) {
            return false;
        }

        $category = new Category($id_category);
        if (!Validate::isLoadedObject($category)) {
            return false;
        }

        // Check if category belongs to current shop context
        if (Shop::isFeatureActive() && !$category->checkAccess($this->context->customer->id)) {
            return false;
        }

        return true;
    }

    private function validateProductInCategory($id_product, $id_category)
    {
        if (!$id_product || !is_numeric($id_product) || !$id_category || !is_numeric($id_category)) {
            return false;
        }

        $sql = 'SELECT COUNT(*)
                FROM ' . _DB_PREFIX_ . 'category_product
                WHERE id_product = ' . (int)$id_product . '
                AND id_category = ' . (int)$id_category;

        return (bool)Db::getInstance()->getValue($sql);
    }

    private function sanitizeSearchInput($search)
    {
        if (!$search) {
            return '';
        }

        // Remove potentially dangerous characters
        $search = strip_tags($search);
        $search = htmlspecialchars($search, ENT_QUOTES, 'UTF-8');

        // Limit length
        if (strlen($search) > 100) {
            $search = substr($search, 0, 100);
        }

        return $search;
    }

    private function validatePositionLimits($id_category, $positions)
    {
        if (!is_array($positions)) {
            return false;
        }

        // Get total products in category
        $total_products = $this->module->getCategoryProductsCount($id_category);

        foreach ($positions as $id_product => $position) {
            // Validate product ID
            if (!is_numeric($id_product) || $id_product <= 0) {
                return false;
            }

            // Validate position
            if (!is_numeric($position) || $position < 0 || $position >= $total_products) {
                return false;
            }

            // Validate product belongs to category
            if (!$this->validateProductInCategory($id_product, $id_category)) {
                return false;
            }
        }

        return true;
    }

    public function initContent()
    {
        $this->content = $this->renderView();
        parent::initContent();
    }

    public function renderView()
    {
        $tab = Tools::getValue('tab', 'category_select');
        
        switch ($tab) {
            case 'category_select':
                return $this->renderCategorySelectTab();
            case 'product_list':
                return $this->renderProductListTab();
            case 'settings':
                return $this->renderSettingsTab();
            default:
                return $this->renderCategorySelectTab();
        }
    }

    private function renderCategorySelectTab()
    {
        $categories = $this->module->getCategoriesTree();
        
        $this->context->smarty->assign([
            'categories' => $categories,
            'current_tab' => 'category_select',
            'module_dir' => $this->module->getLocalPath(),
            'module_url' => $this->module->getPathUri(),
            'admin_url' => $this->context->link->getAdminLink('AdminSortProductCategory'),
            'selected_category' => Tools::getValue('id_category', 0)
        ]);
        
        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/category_select.tpl');
    }

    private function renderProductListTab()
    {
        $id_category = (int)Tools::getValue('id_category');
        
        if (!$id_category) {
            return $this->renderCategorySelectTab();
        }
        
        $page = (int)Tools::getValue('page', 1);
        $limit = (int)Tools::getValue('limit', Configuration::get('SORTPRODUCTCATEGORY_ITEMS_PER_PAGE', 50));

        // Validate and limit the maximum number of items per page
        $allowed_limits = [25, 50, 100, 200, 500, 1000, 1500];
        if (!in_array($limit, $allowed_limits)) {
            $limit = 50; // Default fallback
        }

        $search = Tools::getValue('search', '');
        $order_by = Tools::getValue('order_by', 'position');
        $order_way = Tools::getValue('order_way', 'ASC');
        
        $products = $this->module->getCategoryProducts($id_category, $page, $limit, $search, $order_by, $order_way);
        $total_products = $this->module->getCategoryProductsCount($id_category, $search);
        $total_pages = ceil($total_products / $limit);
        
        // Get category info
        $category = new Category($id_category, $this->context->language->id);
        
        $this->context->smarty->assign([
            'products' => $products,
            'category' => $category,
            'current_tab' => 'product_list',
            'module_dir' => $this->module->getLocalPath(),
            'module_url' => $this->module->getPathUri(),
            'admin_url' => $this->context->link->getAdminLink('AdminSortProductCategory'),
            'admin_token' => Tools::getAdminTokenLite('AdminSortProductCategory'),
            'id_category' => $id_category,
            'page' => $page,
            'limit' => $limit,
            'search' => $search,
            'order_by' => $order_by,
            'order_way' => $order_way,
            'total_products' => $total_products,
            'total_pages' => $total_pages,
            'pagination' => $this->generatePagination($page, $total_pages, $id_category, $search, $order_by, $order_way, $limit)
        ]);
        
        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/product_list.tpl');
    }

    private function renderSettingsTab()
    {
        if (Tools::isSubmit('submitSettings')) {
            $this->processSettingsForm();
        }
        
        $this->context->smarty->assign([
            'current_tab' => 'settings',
            'module_dir' => $this->module->getLocalPath(),
            'module_url' => $this->module->getPathUri(),
            'admin_url' => $this->context->link->getAdminLink('AdminSortProductCategory'),
            'items_per_page' => Configuration::get('SORTPRODUCTCATEGORY_ITEMS_PER_PAGE'),
            'enable_search' => Configuration::get('SORTPRODUCTCATEGORY_ENABLE_SEARCH'),
            'enable_bulk' => Configuration::get('SORTPRODUCTCATEGORY_ENABLE_BULK'),
            'auto_save' => Configuration::get('SORTPRODUCTCATEGORY_AUTO_SAVE')
        ]);
        
        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/settings.tpl');
    }

    private function generatePagination($current_page, $total_pages, $id_category, $search, $order_by, $order_way, $limit)
    {
        $pagination = [];
        $base_url = $this->context->link->getAdminLink('AdminSortProductCategory') .
                   '&tab=product_list&id_category=' . $id_category .
                   '&search=' . urlencode($search) .
                   '&order_by=' . $order_by .
                   '&order_way=' . $order_way .
                   '&limit=' . $limit;
        
        // Previous page
        if ($current_page > 1) {
            $pagination['prev'] = $base_url . '&page=' . ($current_page - 1);
        }
        
        // Page numbers
        $start = max(1, $current_page - 2);
        $end = min($total_pages, $current_page + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            $pagination['pages'][] = [
                'page' => $i,
                'url' => $base_url . '&page=' . $i,
                'current' => ($i == $current_page)
            ];
        }
        
        // Next page
        if ($current_page < $total_pages) {
            $pagination['next'] = $base_url . '&page=' . ($current_page + 1);
        }
        
        return $pagination;
    }

    private function processSettingsForm()
    {
        $items_per_page = (int)Tools::getValue('items_per_page', 50);
        $enable_search = (int)Tools::getValue('enable_search', 0);
        $enable_bulk = (int)Tools::getValue('enable_bulk', 0);
        $auto_save = (int)Tools::getValue('auto_save', 0);
        
        // Validate items per page
        if ($items_per_page < 10 || $items_per_page > 200) {
            $items_per_page = 50;
        }
        
        Configuration::updateValue('SORTPRODUCTCATEGORY_ITEMS_PER_PAGE', $items_per_page);
        Configuration::updateValue('SORTPRODUCTCATEGORY_ENABLE_SEARCH', $enable_search);
        Configuration::updateValue('SORTPRODUCTCATEGORY_ENABLE_BULK', $enable_bulk);
        Configuration::updateValue('SORTPRODUCTCATEGORY_AUTO_SAVE', $auto_save);
        
        $this->confirmations[] = $this->l('Ustawienia zostały zapisane.');
    }

    public function ajaxProcessUpdatePosition()
    {
        $id_product = (int)Tools::getValue('id_product');
        $id_category = (int)Tools::getValue('id_category');
        $new_position = (int)Tools::getValue('new_position');

        // Validate input parameters
        if (!$id_product || !$id_category || $new_position < 0) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        // Validate category access
        if (!$this->validateCategoryAccess($id_category)) {
            die(json_encode(['success' => false, 'message' => 'Brak dostępu do kategorii']));
        }

        // Validate product belongs to category
        if (!$this->validateProductInCategory($id_product, $id_category)) {
            die(json_encode(['success' => false, 'message' => 'Produkt nie należy do tej kategorii']));
        }

        // Validate position limits
        /*$total_products = $this->module->getCategoryProductsCount($id_category);
        if ($new_position >= $total_products) {
            die(json_encode(['success' => false, 'message' => 'Pozycja przekracza liczbę produktów w kategorii']));
        }*/

        $result = $this->module->updateProductPosition($id_product, $id_category, $new_position);

        die(json_encode([
            'success' => $result,
            'message' => $result ? 'Pozycja została zaktualizowana' : 'Błąd podczas aktualizacji pozycji'
        ]));
    }

    public function ajaxProcessBulkUpdatePositions()
    {
        $id_category = (int)Tools::getValue('id_category');
        $positions_json = Tools::getValue('positions');

        // Decode JSON positions
        $positions = json_decode($positions_json, true);

        if (!$id_category || !$positions || !is_array($positions)) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        // Validate category access
        if (!$this->validateCategoryAccess($id_category)) {
            die(json_encode(['success' => false, 'message' => 'Brak dostępu do kategorii']));
        }

        // Validate position limits and products
        if (!$this->validatePositionLimits($id_category, $positions)) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe pozycje lub produkty']));
        }

        // Limit bulk operations to prevent abuse
        if (count($positions) > 100) {
            die(json_encode(['success' => false, 'message' => 'Zbyt wiele produktów do aktualizacji jednocześnie (max 100)']));
        }

        $result = $this->module->bulkUpdatePositions($id_category, $positions);

        die(json_encode([
            'success' => $result,
            'message' => $result ? 'Pozycje zostały zaktualizowane' : 'Błąd podczas aktualizacji pozycji'
        ]));
    }

    public function ajaxProcessSearchProducts()
    {
        $id_category = (int)Tools::getValue('id_category');
        $search = $this->sanitizeSearchInput(Tools::getValue('search', ''));
        $page = max(1, (int)Tools::getValue('page', 1));
        $limit = min(200, max(10, (int)Configuration::get('SORTPRODUCTCATEGORY_ITEMS_PER_PAGE', 50)));

        if (!$id_category) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        // Validate category access
        if (!$this->validateCategoryAccess($id_category)) {
            die(json_encode(['success' => false, 'message' => 'Brak dostępu do kategorii']));
        }

        $products = $this->module->getCategoryProducts($id_category, $page, $limit, $search);
        $total_products = $this->module->getCategoryProductsCount($id_category, $search);

        die(json_encode([
            'success' => true,
            'products' => $products,
            'total' => $total_products,
            'page' => $page,
            'total_pages' => ceil($total_products / $limit)
        ]));
    }

    public function ajaxProcessCleanPositions()
    {
        $id_category = (int)Tools::getValue('id_category');

        if (!$id_category) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        $result = $this->module->cleanCategoryPositions($id_category);

        die(json_encode([
            'success' => $result,
            'message' => $result ? 'Pozycje zostały uporządkowane' : 'Błąd podczas porządkowania pozycji'
        ]));
    }

    public function ajaxProcessAutoSort()
    {
        $id_category = (int)Tools::getValue('id_category');
        $sort_by = Tools::getValue('sort_by', 'name');
        $direction = Tools::getValue('direction', 'ASC');

        if (!$id_category) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        $result = $this->module->autoSortProducts($id_category, $sort_by, $direction);

        die(json_encode([
            'success' => $result,
            'message' => $result ? 'Produkty zostały posortowane automatycznie' : 'Błąd podczas sortowania produktów'
        ]));
    }

    public function ajaxProcessMoveToPositions()
    {
        $id_category = (int)Tools::getValue('id_category');
        $product_positions = Tools::getValue('product_positions');
        $insert_mode = Tools::getValue('insert_mode', 'replace');

        if (!$id_category || !$product_positions || !is_array($product_positions)) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        $result = $this->module->moveProductsToPositions($id_category, $product_positions, $insert_mode);

        die(json_encode([
            'success' => $result,
            'message' => $result ? 'Produkty zostały przeniesione' : 'Błąd podczas przenoszenia produktów'
        ]));
    }

    public function ajaxProcessGetPositionGaps()
    {
        $id_category = (int)Tools::getValue('id_category');

        if (!$id_category) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        $gaps = $this->module->getPositionGaps($id_category);

        die(json_encode([
            'success' => true,
            'gaps' => $gaps,
            'count' => count($gaps)
        ]));
    }

    public function ajaxProcessDuplicatePositions()
    {
        $source_category = (int)Tools::getValue('source_category');
        $target_category = (int)Tools::getValue('target_category');

        if (!$source_category || !$target_category) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        $result = $this->module->duplicatePositions($source_category, $target_category);

        die(json_encode([
            'success' => $result,
            'message' => $result ? 'Pozycje zostały skopiowane' : 'Błąd podczas kopiowania pozycji'
        ]));
    }

    public function ajaxProcessAdvancedSearch()
    {
        $id_category = (int)Tools::getValue('id_category');
        $filters = [
            'page' => (int)Tools::getValue('page', 1),
            'limit' => (int)Tools::getValue('limit', 50),
            'search' => Tools::getValue('search', ''),
            'order_by' => Tools::getValue('order_by', 'position'),
            'order_way' => Tools::getValue('order_way', 'ASC'),
            'active_only' => (bool)Tools::getValue('active_only', false),
            'min_price' => Tools::getValue('min_price') ? (float)Tools::getValue('min_price') : null,
            'max_price' => Tools::getValue('max_price') ? (float)Tools::getValue('max_price') : null,
            'min_quantity' => Tools::getValue('min_quantity') ? (int)Tools::getValue('min_quantity') : null,
        ];

        if (!$id_category) {
            die(json_encode(['success' => false, 'message' => 'Nieprawidłowe parametry']));
        }

        $products = $this->module->getProductsAdvanced($id_category, $filters);
        $total_products = $this->module->getCategoryProductsCount($id_category, $filters['search']);

        die(json_encode([
            'success' => true,
            'products' => $products,
            'total' => $total_products,
            'page' => $filters['page'],
            'total_pages' => ceil($total_products / $filters['limit'])
        ]));
    }
}
