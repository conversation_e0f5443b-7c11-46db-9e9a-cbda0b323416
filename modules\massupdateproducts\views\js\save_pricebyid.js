var productInfo = [];

$(document).ready(function() {

    $('#priceChangeByIdSubmit').on('click', function() {
        var form = $('#priceByIdForm').serialize();
        $('.priceByIdResult').empty();
        if (validateField(form)) {
            step1();
        }
    });

});

function validateField(form)
{
    $('.form-group').each(function() {
        $(this).removeClass('errors');
    });
    $('.form-control').each(function() {
        $(this).removeClass('error');
    });
    $('.alertInfo').remove();

    var rq = true;
    var forms = form.split('&');
    var minId = forms[0];
    var minIdE = minId.split('=');
    if (typeof minIdE[1] == 'undefined' || minIdE[1] == '') {
        rq = false;
        $('#minId').addClass('error').closest('.form-group').addClass('errors');
    } else {
        var maxMinId = parseInt($('#minId').attr('max'));
        var valMinId = parseInt($('#minId').val());
        if (valMinId >= maxMinId) {
            rq = false;
            $('#minId').addClass('error').closest('.form-group').addClass('errors');
            $('#minId').after('<span class="alertInfo">ID produktu jest za wysokie. Max: '+maxMinId+'</span>');
        }
    }

    var maxId = forms[1];
    var maxIdE = maxId.split('=');
    if (typeof maxIdE[1] == 'undefined' || maxIdE[1] == '') {
        rq = false;
        $('#maxId').addClass('error').closest('.form-group').addClass('errors');
    } else {
        var maxMaxId = parseInt($('#maxId').attr('max'));
        var valMaxId = parseInt($('#maxId').val());
        if (valMaxId > maxMaxId) {
            rq = false;
            $('#maxId').addClass('error').closest('.form-group').addClass('errors');
            $('#maxId').after('<span class="alertInfo">ID produktu jest za wysokie. Max: '+maxMaxId+'</span>');
        }
    }

    var kind = forms[2];
    var kindE = kind.split('=');
    if (kindE[1] == 0) {
        rq = false;
        $('#kindDiscount').addClass('error').closest('.form-group').addClass('errors');
    }

    var type = forms[3];
    var typeE = type.split('=');
    if (typeE[1] == 0) {
        rq = false;
        $('#typeDiscount').addClass('error').closest('.form-group').addClass('errors');
    }

    var price = forms[4];
    var priceE = price.split('=');
    if (priceE[1] == 0) {
        rq = false;
        $('#typePrice').addClass('error').closest('.form-group').addClass('errors');
    }

    var amount = forms[5];
    var amountE = amount.split('=');
    if (typeof amountE[1] == 'undefined' || amountE[1] == '') {
        rq = false;
        $('#amount').addClass('error').closest('.form-group').addClass('errors');
    }
    console.log(rq);
    return rq;
}

function getProductInfo(minId, maxId)
{
    var result = $.ajax({
        url: ajaxGetProductInfo,
        data: 'action=getProductInfo&minId='+minId+'&maxId='+maxId,
        dataType: 'json',
        method: 'POST',
        success: function(json) {
            if (json) {
                productInfo = json;
            }
        },
        error: function (e) {

        }
    });
    return result;
}

async function step1()
{
    var obj = $('.priceByIdResult');
    $('html, body').animate({
        scrollTo: $('.priceByIdResult').offset().top
    }, 1000);

    obj.append('<div class="loadingPrice">'+langLoading+'....</div>');

    var min = parseInt($('#minId').val());
    var max = parseInt($('#maxId').val());
    await getProductInfo(min, max);

    obj.show();
    obj.append('<div class="alert alert-info">'+langToUpdate+' '+productInfo.length+' '+langProducts+'</div>');

    if (productInfo.length > 0) {
        var kindDiscount = parseInt($('#kindDiscount').val());
        var typeDiscount = parseInt($('#typeDiscount').val());
        var typePrice = parseInt($('#typePrice').val());
        var amount = parseInt($('#amount').val());
        for (i = 0; i < productInfo.length; i++) {
            await setNewPrice(productInfo[i].id_product, kindDiscount, typeDiscount, typePrice, amount, obj);
        }
    }
}

function setNewPrice(id, kind, type, price, amount, obj)
{
    var result = $.ajax({
        url: ajaxGetProductInfo,
        data: 'action=setProductPrice&id='+id+'&kind='+kind+'&type='+type+'&price='+price+'&amount='+amount,
        dataType: 'json',
        method: 'POST',
        success: function(json) {
            if (json) {
                if (json.status) {
                    obj.append('<div class="itemUpdate itemStatus' + json.status + '">'+langUpdateProduct+' <b>[' + json.id + '] ' + json.productName + '</b> '+langPriceFrom+' <b>' + json.price + '</b> '+langTaxExcl+' '+langTo+' <b>' + json.newPrice + '</b> '+langTaxExcl+'</div>');
                } else {
                    obj.append('<div class="itemUpdate itemStatus' + json.status + '">'+langNoUpdateProduct+' <b>[' + json.id + '] ' + json.productName + '</b> '+langPriceFrom+' <b>' + json.price + '</b> '+langTaxExcl+' '+langTo+' <b>' + json.newPrice + '</b> '+langTaxExcl+'</div>');
                }
            }
        },
        error: function (e) {

        }
    });
    return result;
}