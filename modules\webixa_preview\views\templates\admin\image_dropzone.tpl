{**
 * Image dropzone template for inspiration module
 * Based on PrestaShop product image dropzone
 *}

<div id="inspiration-images-container" class="panel">
    <div class="panel-heading">
        {l s='Images' d='Modules.Webixapreview.Admin'}
        <span class="panel-heading-action">
            <a class="list-toolbar-btn" id="add-inspiration-image-button">
                <span class="label-tooltip" data-toggle="tooltip" title="{l s='Add new image' d='Modules.Webixapreview.Admin'}">
                    <i class="process-icon-new"></i>
                </span>
            </a>
        </span>
    </div>

    <div class="form-wrapper">
        <div class="row">
            <div class="col-lg-12">
                <div id="inspiration-image-uploader" class="panel">
                    <div id="inspiration-image-dropzone" class="inspiration-image-dropzone panel">
                        <div class="dz-default dz-message">
                            <i class="icon-picture-o"></i><br/>
                            {l s='Drop images here or' d='Modules.Webixapreview.Admin'} <a>{l s='select files' d='Modules.Webixapreview.Admin'}</a>
                        </div>
                        <input id="inspiration-image-input" type="file" name="inspiration-image-input" multiple style="display:none;">
                    </div>
                    <div class="fallback">
                        <input name="images[]" type="file" multiple>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div id="inspiration-images-thumbnails" class="row">
                    {if isset($inspiration_images) && $inspiration_images}
                        {foreach from=$inspiration_images item=image}
                            <div class="col-xs-6 col-md-3 image-item" data-id="{$image.id_inspiration_image}">
                                <div class="panel {if $image.main}panel-success{else}panel-default{/if}">
                                    <div class="panel-heading">
                                        {if $image.main}
                                            <span class="label label-success">{l s='Main' d='Modules.Webixapreview.Admin'}</span>
                                        {/if}
                                        {l s='Image' d='Modules.Webixapreview.Admin'} #{$image.id_inspiration_image}
                                    </div>
                                    <div class="panel-body">
                                        <img src="{$image.url}" class="img-responsive" alt="">
                                    </div>
                                    <div class="panel-footer">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-default btn-sm set-main-image" data-id="{$image.id_inspiration_image}">
                                                <i class="icon-star"></i> {l s='Set as Main' d='Modules.Webixapreview.Admin'}
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm delete-image" data-id="{$image.id_inspiration_image}">
                                                <i class="icon-trash"></i> {l s='Delete' d='Modules.Webixapreview.Admin'}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/foreach}
                    {/if}
                </div>
                <input type="hidden" name="main_image_id" id="main_image_id" value="{if isset($main_image_id)}{$main_image_id}{/if}">
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize the image uploader
    var inspirationImageUploader = {
        init: function() {
            // Add click handler for the add image button
            $('#add-inspiration-image-button').click(function(e) {
                e.preventDefault();
                $('#inspiration-image-input').click();
            });

            // Handle file selection
            $('#inspiration-image-input').change(function() {
                var files = $(this)[0].files;
                if (files.length > 0) {
                    inspirationImageUploader.uploadFiles(files);
                }
            });

            // Initialize drag and drop
            $('#inspiration-image-dropzone').on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dz-drag-hover');
            }).on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dz-drag-hover');
            }).on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dz-drag-hover');
                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    inspirationImageUploader.uploadFiles(files);
                }
            });

            // Set main image
            $(document).on('click', '.set-main-image', function() {
                var imageId = $(this).data('id');
                $('#main_image_id').val(imageId);

                // Update UI
                $('.image-item .panel').removeClass('panel-success').addClass('panel-default');
                $('.image-item[data-id="' + imageId + '"] .panel').removeClass('panel-default').addClass('panel-success');

                // Update labels
                $('.image-item .label-success').remove();
                $('.image-item[data-id="' + imageId + '"] .panel-heading').prepend(
                    '<span class="label label-success">{l s='Main' d='Modules.Webixapreview.Admin'}</span> '
                );

                showSuccessMessage('{l s='Main image updated' d='Modules.Webixapreview.Admin'}');
            });

            // Delete image
            $(document).on('click', '.delete-image', function() {
                if (confirm('{l s='Are you sure you want to delete this image?' d='Modules.Webixapreview.Admin'}')) {
                    var imageId = $(this).data('id');
                    var imageItem = $(this).closest('.image-item');

                    $.ajax({
                        url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                        type: 'POST',
                        data: {
                            ajax: 1,
                            action: 'DeleteImage',
                            id_inspiration_image: imageId
                        },
                        success: function(response) {
                            var data = JSON.parse(response);
                            if (data.success) {
                                imageItem.remove();
                                showSuccessMessage('{l s='Image deleted successfully' d='Modules.Webixapreview.Admin'}');
                            } else {
                                showErrorMessage('{l s='Failed to delete image' d='Modules.Webixapreview.Admin'}');
                            }
                        },
                        error: function() {
                            showErrorMessage('{l s='Failed to delete image' d='Modules.Webixapreview.Admin'}');
                        }
                    });
                }
            });
        },

        uploadFiles: function(files) {
            var formData = new FormData();
            formData.append('ajax', 1);
            formData.append('action', 'UploadImage');
            formData.append('id_inspiration', {$id_inspiration|intval});
            
            // Add files to form data
            for (var i = 0; i < files.length; i++) {
                formData.append('images[]', files[i]);
            }
            
            // Show loading indicator
            $('#inspiration-image-dropzone').addClass('loading');
            
            // Upload files
            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    $('#inspiration-image-dropzone').removeClass('loading');
                    try {
                        var data = JSON.parse(response);
                        if (data.success) {
                            // Refresh the images list
                            inspirationImageUploader.refreshImagesList();
                            showSuccessMessage('{l s='Images uploaded successfully' d='Modules.Webixapreview.Admin'}');
                        } else {
                            showErrorMessage(data.message || '{l s='Failed to upload images' d='Modules.Webixapreview.Admin'}');
                        }
                    } catch (e) {
                        showErrorMessage('{l s='Invalid response from server' d='Modules.Webixapreview.Admin'}');
                    }
                },
                error: function() {
                    $('#inspiration-image-dropzone').removeClass('loading');
                    showErrorMessage('{l s='Failed to upload images' d='Modules.Webixapreview.Admin'}');
                }
            });
        },
        
        refreshImagesList: function() {
            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                type: 'GET',
                data: {
                    ajax: 1,
                    action: 'GetImages',
                    id_inspiration: {$id_inspiration|intval}
                },
                success: function(response) {
                    try {
                        var data = JSON.parse(response);
                        if (data.success) {
                            $('#inspiration-images-thumbnails').html(data.html);
                            
                            // Update product image selects
                            if (data.images && data.images.length > 0) {
                                var options = '<option value="0">{l s='Select image' d='Modules.Webixapreview.Admin'}</option>';
                                for (var i = 0; i < data.images.length; i++) {
                                    var image = data.images[i];
                                    var mainText = image.main ? ' ({l s='Main' d='Modules.Webixapreview.Admin'})' : '';
                                    options += '<option value="' + image.id_inspiration_image + '">{l s='Image' d='Modules.Webixapreview.Admin'} #' + image.id_inspiration_image + mainText + '</option>';
                                }
                                $('.product-image-select').html(options);
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing response', e);
                    }
                }
            });
        }
    };

    // Initialize the uploader
    inspirationImageUploader.init();
});
</script>

<style>
.inspiration-image-dropzone {
    padding: 20px;
    border: 2px dashed #ccc;
    text-align: center;
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}
.inspiration-image-dropzone.dz-drag-hover {
    border-color: #72C279;
    background-color: #f8f9fa;
}
.inspiration-image-dropzone.loading {
    opacity: 0.7;
    background: url('../img/loader.gif') no-repeat center center;
}
.inspiration-image-dropzone .dz-default {
    padding: 30px 0;
}
.inspiration-image-dropzone i {
    font-size: 3em;
    color: #ccc;
}
.inspiration-image-dropzone a {
    color: #00aff0;
    text-decoration: underline;
    cursor: pointer;
}
#inspiration-images-thumbnails .panel {
    margin-bottom: 20px;
}
#inspiration-images-thumbnails .panel-body {
    height: 150px;
    overflow: hidden;
    text-align: center;
    background-color: #f8f8f8;
}
#inspiration-images-thumbnails .panel-body img {
    max-height: 100%;
    max-width: 100%;
}
#inspiration-images-thumbnails .panel-footer {
    text-align: center;
}
</style>
