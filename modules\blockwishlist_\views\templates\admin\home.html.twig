{#**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License version 3.0
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 *#}
{% extends '@PrestaShop/Admin/layout.html.twig' %}

{% block content %}
  {{ form_start(configurationForm) }}
    <div class="row justify-content-center">
      <div class="col-xl-10">
        <div class="card">
          <h3 class="card-header">
            <i class="material-icons">edit</i> {{ 'Wording'|trans({}, 'Modules.Blockwishlist.Admin') }}
          </h3>

          <div class="card-body">
            <div class="form-wrapper">
              {{ form_widget(configurationForm) }}
            </div>
          </div>

          <div class="card-footer">
            <div class="d-flex justify-content-end">
              <button class="btn btn-primary">{{ 'Save'|trans({}, 'Modules.Blockwishlist.Admin') }}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  {{ form_end(configurationForm) }}

  {% if resultHandleForm != null %}
    {# resutHandleForm represent the save of every config (can be true/false/null) null = no submission #}
  {% endif %}
{% endblock %}

{% block javascripts %}
  {{ parent() }}
  <script src="{{ asset('../modules/blockwishlist/public/form.bundle.js') }}"></script>
{% endblock %}
