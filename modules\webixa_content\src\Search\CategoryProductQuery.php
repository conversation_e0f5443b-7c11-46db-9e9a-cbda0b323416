<?php

namespace Webixa\Content\Search;

use Combination;
use Configuration;
use Context;
use Db;
use FrontController;
use Group;
use Product;
use Shop;
use Tools;
use Validate;

class CategoryProductQuery
{
    /**
     * Get required informations on category products.
     *
     * @param int $idLang Language id
     * @param int|null $idCategory Category id (optional)
     * @param bool|false $subcategories (optional)
     * @param int $pageNumber Start from (optional)
     * @param int $nbProducts Number of products to return (optional)
     * @param string|null $orderBy sorting field (optional)
     * @param string|null $orderWay sorting way ASC|DESC (optional)
     *
     * @return array|bool from Product::getProductProperties
     *                    `false` if failure
     */
    public static function getProducts($idLang, $idCategory = null, $subcategories = false, $pageNumber = 0, $nbProducts = 10, $orderBy = null, $orderWay = null)
    {
        $context = Context::getContext();
        if (!Validate::isUnsignedId($idCategory)) {
            $idCategory = $context->shop->getCategory();
        }
        if ($pageNumber < 1) {
            $pageNumber = 1;
        }
        if ($nbProducts < 1) {
            $nbProducts = 10;
        }
        $finalOrderBy = $orderBy;
        $orderTable = '';

        $invalidOrderBy = !Validate::isOrderBy($orderBy);
        if ($invalidOrderBy || null === $orderBy) {
            $orderBy = 'position';
            $orderTable = 'cp';
        }

        if ($orderBy == 'date_add' || $orderBy == 'date_upd') {
            $orderTable = 'product_shop';
        }

        $invalidOrderWay = !Validate::isOrderWay($orderWay);
        if ($invalidOrderWay || null === $orderWay || $orderBy == 'priority') {
            $orderWay = 'ASC';
        }

        $interval = Validate::isUnsignedInt(Configuration::get('PS_NB_DAYS_NEW_PRODUCT')) ? Configuration::get('PS_NB_DAYS_NEW_PRODUCT') : 20;

        $sql = 'SELECT p.*, product_shop.*, stock.out_of_stock, IFNULL(stock.quantity, 0) as quantity,
                    ' . (Combination::isFeatureActive() ? 'product_attribute_shop.minimal_quantity AS product_attribute_minimal_quantity,IFNULL(product_attribute_shop.id_product_attribute,0) id_product_attribute,' : '') . '
                    pl.`description`, pl.`description_short`, pl.`link_rewrite`, pl.`meta_description`,
                    pl.`meta_keywords`, pl.`meta_title`, pl.`name`, pl.`available_now`, pl.`available_later`,
                    m.`name` AS manufacturer_name, p.`id_manufacturer` as id_manufacturer,
                    image_shop.`id_image` id_image, il.`legend`,
                    t.`rate`, pl.`meta_keywords`, pl.`meta_title`, pl.`meta_description`,
                    DATEDIFF(p.`date_add`, DATE_SUB("' . date('Y-m-d') . ' 00:00:00",
                    INTERVAL ' . (int) $interval . ' DAY)) > 0 AS new'
            . ' FROM `' . _DB_PREFIX_ . 'category_product` cp
                LEFT JOIN `' . _DB_PREFIX_ . 'product` p ON cp.`id_product` = p.`id_product`
                ' . Shop::addSqlAssociation('product', 'p', false);
        if (Combination::isFeatureActive()) {
            $sql .= ' LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_shop` product_attribute_shop
                            ON (p.`id_product` = product_attribute_shop.`id_product` AND product_attribute_shop.`default_on` = 1 AND product_attribute_shop.id_shop=' . (int) $context->shop->id . ')';
        }

        $sql .= ' LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl
                    ON p.`id_product` = pl.`id_product`
                    AND pl.`id_lang` = ' . (int) $idLang . Shop::addSqlRestrictionOnLang('pl') . '
                LEFT JOIN `' . _DB_PREFIX_ . 'image_shop` image_shop
                    ON (image_shop.`id_product` = p.`id_product` AND image_shop.cover=1 AND image_shop.id_shop=' . (int) $context->shop->id . ')
                LEFT JOIN `' . _DB_PREFIX_ . 'image_lang` il ON (image_shop.`id_image` = il.`id_image` AND il.`id_lang` = ' . (int) $idLang . ')
                LEFT JOIN `' . _DB_PREFIX_ . 'manufacturer` m ON (m.`id_manufacturer` = p.`id_manufacturer`)
                LEFT JOIN `' . _DB_PREFIX_ . 'tax_rule` tr ON (product_shop.`id_tax_rules_group` = tr.`id_tax_rules_group`)
                    AND tr.`id_country` = ' . (int) $context->country->id . '
                    AND tr.`id_state` = 0
                LEFT JOIN `' . _DB_PREFIX_ . 'tax` t ON (t.`id_tax` = tr.`id_tax`)
                ' . Product::sqlStock('p', 0);

        $sql .= '
                WHERE
                    ' .
            (
                !$subcategories ? 'cp.`id_category`=' . (int)$idCategory
                : '
                    cp.`id_category` IN (
                        SELECT c2.id_category
                        FROM `' . _DB_PREFIX_ . 'category` c
                        INNER JOIN  `' . _DB_PREFIX_ . 'category` c2 ON (c.nleft <= c2.nleft AND c.nright>=c2.nright)
                        WHERE c.id_category=' . (int) $idCategory . '
                    )
                '
            )
            . '
                    AND product_shop.`active` = 1
                    AND product_shop.`visibility` != \'none\'
        ';

        if (Group::isFeatureActive()) {
            $groups = FrontController::getCurrentCustomerGroups();
            $sql .= ' AND EXISTS(SELECT 1 FROM `' . _DB_PREFIX_ . 'category_product` cp
            JOIN `' . _DB_PREFIX_ . 'category_group` cg ON (cp.id_category = cg.id_category AND cg.`id_group` ' . (count($groups) ? 'IN (' . implode(',', $groups) . ')' : '=' . (int) Group::getCurrent()->id) . ')
            WHERE cp.`id_product` = p.`id_product`)';
        }
        $sql .= ' GROUP BY p.id_product';

        if ($finalOrderBy != 'price') {
            $sql .= '
                    ORDER BY stock.`quantity` > 0 DESC, stock.`out_of_stock` = 1 DESC, product_shop.`available_for_order` > 0 DESC, ' . (!empty($orderTable) ? '`' . pSQL($orderTable) . '`.' : '') . '`' . pSQL($orderBy) . '` ' . pSQL($orderWay) . '
                    LIMIT ' . (int) (($pageNumber - 1) * $nbProducts) . ', ' . (int) $nbProducts;
        }

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if ($finalOrderBy == 'price') {
            Tools::orderbyPrice($result, $orderWay);
            $result = array_slice($result, (int) (($pageNumber - 1) * $nbProducts), (int) $nbProducts);
        }
        if (!$result) {
            return false;
        }

        return Product::getProductsProperties($idLang, $result);
    }

    /**
     * Get number of actives products sold.
     *
     * @param int $idCategory Category id
     *
     * @return int number of actives products listed in product_sales
     */
    public static function getNbProducts($idCategory, $subcategories = false)
    {
        $sql = 'SELECT COUNT(*) AS nb
                FROM `' . _DB_PREFIX_ . 'category_product` cp
                LEFT JOIN `' . _DB_PREFIX_ . 'product` p ON p.`id_product` = cp.`id_product`
                ' . Shop::addSqlAssociation('product', 'p', false) . '
                WHERE
                ' .
            (
                !$subcategories ? 'cp.`id_category`=' . (int)$idCategory
                : '
                    cp.`id_category` IN (
                        SELECT c2.id_category
                        FROM `' . _DB_PREFIX_ . 'category` c
                        INNER JOIN  `' . _DB_PREFIX_ . 'category` c2 ON (c.nleft <= c2.nleft AND c.nright>=c2.nright)
                        WHERE c.id_category=' . (int) $idCategory . '
                    )
                '
            )
            . '
                    AND product_shop.`active` = 1';

        return (int) Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }
}
