<link rel="stylesheet" type="text/css" href="{$module_url}views/css/admin.css">

<div class="panel">
    <div class="panel-heading">
        <i class="icon-folder-open"></i>
        {l s='Wybór kategorii' mod='sortproductcategory'}
    </div>
    
    <div class="panel-body">
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-info">
                    <p>{l s='Wybierz kategorię, aby zarządzać kolejnością produktów.' mod='sortproductcategory'}</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group">
                    <label class="control-label">{l s='Kategoria:' mod='sortproductcategory'}</label>
                    <select id="category-select" class="form-control" onchange="selectCategory()">
                        <option value="">{l s='-- <PERSON><PERSON><PERSON><PERSON> kategorię --' mod='sortproductcategory'}</option>
                        {function name=displayCategories categories=$categories level=0}
                            {foreach $categories as $category}
                                <option value="{$category.id_category}" {if $category.id_category == $selected_category}selected{/if}>
                                    {str_repeat('&nbsp;&nbsp;&nbsp;', $level)}{$category.name}
                                </option>
                                {if isset($category.children) && $category.children}
                                    {call name=displayCategories categories=$category.children level=$level+1}
                                {/if}
                            {/foreach}
                        {/function}
                        {call name=displayCategories categories=$categories level=0}
                    </select>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-12">
                <button type="button" class="btn btn-primary unified-btn" onclick="selectCategory()">
                    <i class="icon-arrow-right"></i>
                    {l s='Przejdź do zarządzania produktami' mod='sortproductcategory'}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function selectCategory() {
    var categoryId = document.getElementById('category-select').value;
    if (categoryId) {
        window.location.href = '{$admin_url}&tab=product_list&id_category=' + categoryId;
    } else {
        alert('{l s='Proszę wybrać kategorię' mod='sortproductcategory'}');
    }
}

// Auto-redirect if category is already selected
{if $selected_category}
    window.location.href = '{$admin_url}&tab=product_list&id_category={$selected_category}';
{/if}
</script>
