<?php


if (!defined('_PS_VERSION_')) {
    exit;
}

class Webixa_ComparerCompareModuleFrontController extends ModuleFrontController
{    
    public $auth = false;
    public $ssl = true;

    /**
     * Inicjalizacja kontrolera
     */
    public function init(): void
    {
        parent::init();

        // Sprawdzenie czy moduł jest aktywny
        if (!$this->module->active) {
            Tools::redirect('index.php');
        }
    }

    /**
     * Przetwarzanie żądań przed wyświetleniem strony
     */
    public function postProcess(): void
    {
        // Sprawdź czy to żądanie AJAX
        if (Tools::isSubmit('ajax')) {
            $this->processAjaxRequest();
            return;
        }

        parent::postProcess();
    }

    /**
     * Przetwarzanie żądań AJAX
     */
    private function processAjaxRequest(): void
    {
        // Sprawdzanie czy żądanie jest poprawne
        if (!$this->module->active) {
            $this->ajaxResponse(false, 'Invalid request or module inactive');
            return;
        }
        
        $inputJSON = file_get_contents('php://input');
        $input = json_decode($inputJSON, true);

        // Jeśli nie ma danych JSON, spróbuj standardowych parametrów
        if (empty($input)) {
            $action = Tools::getValue('action');
            $productId = (int)Tools::getValue('productId');
        } else {
            $action = isset($input['action']) ? $input['action'] : null;
            $productId = isset($input['productId']) ? (int)$input['productId'] : 0;
        }
        
        // Walidacja danych wejściowych
        if (!$action || ($action !== 'clear' && !Validate::isUnsignedInt($productId))) {
            $this->ajaxResponse(false, 'Invalid parameters');
            return;
        }
        
        try {
            switch ($action) {
                case 'add':
                    $result = $this->module->addProduct($productId);
                    $message = $result['success'] 
                        ? $this->module->l('Product added to comparison') 
                        : $result['message'];
                    $this->ajaxResponse($result['success'], $message, [
                        'count' => $result['count'] ?? 0,
                        'productName' => Product::getProductName($productId)
                    ]);
                    break;
                
                case 'remove':
                    $result = $this->module->removeProduct($productId);
                    $message = $result['success'] 
                        ? $this->module->l('Product removed from comparison') 
                        : $result['message'];
                    $this->ajaxResponse($result['success'], $message, [
                        'count' => $result['count'] ?? 0,
                        'productName' => Product::getProductName($productId)
                    ]);
                    break;
                
                case 'clear':
                    $result = $this->module->clearComparison();
                    $this->ajaxResponse(true, $this->module->l('All products removed from comparison'));
                    break;
                
                default:
                    $this->ajaxResponse(false, $this->module->l('Invalid action'));
            }
        } catch (Exception $e) {
            $this->ajaxResponse(false, $e->getMessage());
        }
    }
    
    /**
     * Generuje odpowiedź JSON dla żądań AJAX
     *
     * @param bool $success Status operacji
     * @param string $message Komunikat
     * @param array $data Dodatkowe dane
     */
    private function ajaxResponse(bool $success, string $message, array $data = []): void
    {
        header('Content-Type: application/json');
        die(json_encode([
            'success' => (bool)$success,
            'message' => $message,
            'data' => $data
        ]));
    }

    /**
     * Inicjalizacja zawartości strony
     */
    public function initContent(): void
    {
        // Jeśli to żądanie AJAX, nie wyświetlaj strony
        if (Tools::isSubmit('ajax')) {
            return;
        }

        parent::initContent();

        // Pobranie produktów do porównania
        $products = $this->module->getProductsForComparison(
            (int)$this->context->customer->id,
            (int)$this->context->cookie->id_guest
        );

        // Jeśli nie ma produktów, wyświetl komunikat
        if (empty($products)) {
            $this->context->smarty->assign([
                'empty_comparison' => true,
                'home_url' => $this->context->link->getPageLink('index')
            ]);
        } else {
            // Przygotowanie danych produktów i cech do porównania
            $this->context->smarty->assign([
                'products' => $this->getProductDetails($products),
                'features' => $this->getComparableFeatures($products),
                'ajax_url' => $this->context->link->getModuleLink('webixa_comparer', 'compare', ['ajax' => 1])
            ]);
        }

        // Ustawienie szablonu
        $this->setTemplate('module:webixa_comparer/views/templates/front/comparison_page.tpl');
    }

    /**
     * Pobiera szczegółowe dane produktów
     *
     * @param array $productIds Lista ID produktów
     * @return array Szczegółowe dane produktów
     */
    private function getProductDetails(array $productIds): array
    {
        $products = [];
        $languageId = $this->context->language->id;
        $currencyId = $this->context->currency->id;
        
        // Pobierz dane dla każdego produktu
        foreach ($productIds as $productId) {
            $product = new Product(
                $productId,
                true,
                $languageId,
                $this->context->shop->id
            );
            
            // Pomiń nieprawidłowe produkty
            if (!Validate::isLoadedObject($product) || !$product->active || !$product->isAssociatedToShop()) {
                continue;
            }
            
            // Pobierz zdjęcie produktu
            $coverImageId = Product::getCover($productId);
            $imageUrl = $this->context->link->getImageLink(
                $product->link_rewrite,
                $coverImageId ? $coverImageId['id_image'] : '',
                ImageType::getFormattedName('home')
            );
            
            // Pobierz cenę produktu
            $price = Product::getPriceStatic(
                $productId,
                true,
                null,
                6,
                null,
                false,
                true
            );
            
            // Pobierz cechy produktu
            $features = [];
            $productFeatures = $product->getFrontFeatures($languageId);
            foreach ($productFeatures as $feature) {
                $features[$feature['id_feature']] = $feature['value'];
            }
            
            // Pobierz atrybuty produktu
            $attributes = $this->getProductAttributes($productId);
            
            // Dodaj produkt do listy
            $products[] = [
                'id_product' => $productId,
                'name' => $product->name,
                'description_short' => $product->description_short,
                'price' => Tools::displayPrice($price, $currencyId),
                'price_without_tax' => Tools::displayPrice(
                    Product::getPriceStatic($productId, false),
                    $currencyId
                ),
                'image' => $imageUrl,
                'url' => $this->context->link->getProductLink($product),
                'available' => $product->checkQty(1),
                'stock_quantity' => StockAvailable::getQuantityAvailableByProduct($productId),
                'features' => $features,
                'manufacturer' => ($product->id_manufacturer) ? 
                    Manufacturer::getNameById($product->id_manufacturer) : '',
                'attributes' => $attributes,
                'specific_prices' => $this->getSpecificPrices($productId),
                'reference' => $product->reference,
                'ean13' => $product->ean13,
                'upc' => $product->upc,
                'isbn' => $product->isbn,
                'mpn' => $product->mpn
            ];
        }
        
        return $products;
    }

    /**
     * Pobiera atrybuty produktu
     *
     * @param int $productId ID produktu
     * @return array Atrybuty produktu
     */
    private function getProductAttributes(int $productId): array
    {
        $attributes = [];
        $result = Db::getInstance()->executeS('
            SELECT agl.name AS group_name, al.name AS attribute_name
            FROM '._DB_PREFIX_.'product_attribute pa
            LEFT JOIN '._DB_PREFIX_.'product_attribute_combination pac 
                ON pa.id_product_attribute = pac.id_product_attribute
            LEFT JOIN '._DB_PREFIX_.'attribute a 
                ON a.id_attribute = pac.id_attribute
            LEFT JOIN '._DB_PREFIX_.'attribute_lang al 
                ON (a.id_attribute = al.id_attribute AND al.id_lang = '.(int)$this->context->language->id.')
            LEFT JOIN '._DB_PREFIX_.'attribute_group ag 
                ON ag.id_attribute_group = a.id_attribute_group
            LEFT JOIN '._DB_PREFIX_.'attribute_group_lang agl 
                ON (ag.id_attribute_group = agl.id_attribute_group AND agl.id_lang = '.(int)$this->context->language->id.')
            WHERE pa.id_product = '.(int)$productId
        );
        
        foreach ($result as $row) {
            if (!isset($attributes[$row['group_name']])) {
                $attributes[$row['group_name']] = [];
            }
            $attributes[$row['group_name']][] = $row['attribute_name'];
        }
        
        return $attributes;
    }

    /**
     * Pobiera informacje o promocjach produktu
     *
     * @param int $productId ID produktu
     * @return array Informacje o promocjach
     */
    private function getSpecificPrices(int $productId): array
    {
        $specificPrice = SpecificPrice::getSpecificPrice(
			$productId,
			$this->context->shop->id,
			$this->context->currency->id,
			$this->context->country->id,
			$this->context->customer->id_default_group,
			1,  // Quantity
			null,  // Product attribute ID (null for all attributes)
			$this->context->customer->id,  // Customer ID
			0,  // Cart ID
			0   // Real quantity
		);
        
        if (!$specificPrice) {
            return [];
        }
        
        $reduction = 0;
        if ($specificPrice['reduction_type'] == 'percentage') {
            $reduction = $specificPrice['reduction'] * 100;
        } else {
            $price = Product::getPriceStatic($productId, false);
            $reduction = ($price > 0) ? ($specificPrice['reduction'] / $price) * 100 : 0;
        }
        
        return [
            'discount' => round($reduction).'%',
            'from' => $specificPrice['from'],
            'to' => $specificPrice['to']
        ];
    }

    /**
     * Pobiera cechy do porównania
     *
     * @param array $productIds Lista ID produktów
     * @return array Cechy do porównania
     */
    private function getComparableFeatures(array $productIds): array
    {
        $features = [];
        
        // Pobierz wszystkie cechy dla wybranych produktów
        $result = Db::getInstance()->executeS('
            SELECT DISTINCT f.id_feature, fl.name
            FROM '._DB_PREFIX_.'feature_product fp
            LEFT JOIN '._DB_PREFIX_.'feature f ON f.id_feature = fp.id_feature
            LEFT JOIN '._DB_PREFIX_.'feature_lang fl ON (f.id_feature = fl.id_feature AND fl.id_lang = '.(int)$this->context->language->id.')
            WHERE fp.id_product IN ('.implode(',', array_map('intval', $productIds)).')
            ORDER BY f.position ASC
        ');
        
        foreach ($result as $row) {
            $features[] = [
                'id_feature' => $row['id_feature'],
                'name' => $row['name']
            ];
        }
        
        return $features;
    }

    /**
     * Generuje linki do breadcrumb
     *
     * @return array Linki do breadcrumb
     */
    public function getBreadcrumbLinks(): array
    {
        $breadcrumb = parent::getBreadcrumbLinks();
        $breadcrumb['links'][] = [
            'title' => $this->module->l('Product Comparison'),
            'url' => $this->context->link->getModuleLink('webixa_comparer', 'compare')
        ];

        return $breadcrumb;
    }

    /**
     * Dodaje skrypty JavaScript
     */
    public function setMedia(): void
    {
        parent::setMedia();

        // Dodaj skrypt JavaScript z URL do kontrolera AJAX
        Media::addJsDef([
            'ajaxUrl' => $this->context->link->getModuleLink('webixa_comparer', 'compare', ['ajax' => 1])
        ]);
    }
}