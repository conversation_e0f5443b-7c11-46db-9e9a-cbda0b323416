<?php

class AdminWebixaContentHookController extends ModuleAdminController
{
    public $bootstrap = true;
    protected $position_identifier;

    public function __construct()
    {
        $this->bootstrap = true;
        parent::__construct();

        if (version_compare(_PS_VERSION_, '1.7', '>=')) {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
            $this->tabAccess['view'] = Module::getPermissionStatic($this->module->id, 'view');

            $configAccess = Module::getPermissionStatic($this->module->id, 'configure');
            $this->tabAccess['add'] = $configAccess;
            $this->tabAccess['edit'] = $configAccess;
            $this->tabAccess['delete'] = $configAccess;
        } else {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
        }
    }

    public function initProcess()
    {
        if (
            Tools::isSubmit('view' . WebixaContentHook::$definition['table']) ||
            Tools::isSubmit('delete' . WebixaContentHook::$definition['table'] . '_block') ||
            Tools::isSubmit('submitAdd' . WebixaContentHook::$definition['table'] . '_block') ||
            Tools::isSubmit('add' . WebixaContentHook::$definition['table'] . '_block')
        ) {
            $this->initHookPositionList();
            $this->display = 'view';
            if (Tools::isSubmit('delete' . $this->table)) {
                if ($this->access('delete')) {
                    $this->action = 'deleteHookBlock';
                } else {
                    $this->errors[] = $this->l('You do not have permission to delete this.');
                }
            } elseif (
                Tools::isSubmit('submitAdd' . $this->table)
            ) {
                if ($this->access('add')) {
                    $this->action = 'saveHookBlock';
                } else {
                    $this->errors[] = $this->l('You do not have permission to add this.');
                }
            } elseif (Tools::isSubmit('add' . $this->table)) {
                if ($this->access('add')) {
                    $this->action = 'addHookBlock';
                    $this->display = 'addHookBlock';
                } else {
                    $this->errors[] = $this->l('You do not have permission to add this.');
                }
            }
        } else {
            $this->initList();
            parent::initProcess();
        }
    }

    public function initContent()
    {
        if (!$this->viewAccess()) {
            $this->errors[] = $this->l('You do not have permission to view this.');

            return;
        }

        if ('addHookBlock' == $this->display) {
            if (
                !Tools::getValue(WebixaContentHook::$definition['primary']) &&
                !Tools::getValue(WebixaContentBlock::$definition['primary'])
            ) {
                return;
            }

            $this->content .= $this->renderHookBlockForm();
        } else {
            return parent::initContent();
        }

        $this->context->smarty->assign([
            'content' => $this->content,
        ]);
    }

    public function initList()
    {
        $this->table = WebixaContentHook::$definition['table'];
        $this->className = 'WebixaContentHook';
        $this->identifier = WebixaContentHook::$definition['primary'];
        $this->_defaultOrderBy = 'hook';
        $this->_defaultOrderWay = 'ASC';

        $this->lang = false;

        $this->_select = '
            b.blocks
        ';

        $this->_join = '
            LEFT JOIN (
                SELECT
                    GROUP_CONCAT(CONCAT(b.name, "(", b.' . WebixaContentBlock::$definition['primary'] . ', ")", " - ", hb.template) ORDER BY hb.position ASC SEPARATOR "\n") as blocks,
                    hb.' . $this->identifier . '
                FROM `' . _DB_PREFIX_ . WebixaContentHook::$definition['table'] . '_block` hb
                JOIN `' . _DB_PREFIX_ . WebixaContentBlock::$definition['table'] . '` b
                    ON (b.' . WebixaContentBlock::$definition['primary'] . '=hb.' . WebixaContentBlock::$definition['primary'] . ')
                ' .
            (
                !Shop::isFeatureActive() ? '' :
                '
                INNER JOIN `' . _DB_PREFIX_ . WebixaContentBlock::$definition['table'] . '_shop` bs
                    ON (b.' . WebixaContentBlock::$definition['primary'] . '=bs.' . WebixaContentBlock::$definition['primary'] . ' AND bs.`id_shop`=' . (int)$this->context->shop->id . ')
                '
            )
            . '
                LEFT JOIN `' . _DB_PREFIX_ . WebixaContentBlock::$definition['table'] . '_lang` bl
                    ON (b.' . WebixaContentBlock::$definition['primary'] . '=bl.' . WebixaContentBlock::$definition['primary'] . ' AND bl.`id_lang`=' . (int)$this->context->language->id . ')
                GROUP BY hb.' . $this->identifier . '
                ORDER BY hb.position
            ) b ON (a.' . $this->identifier . '=b.' . $this->identifier . ')
        ';

        $this->_group = 'GROUP BY a.' . $this->identifier;

        $this->fields_list = [
            $this->identifier => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
            ],
            'hook' => [
                'title' => $this->l('Hook'),
                'maxlength' => 50,
            ],
            'blocks' => [
                'title' => $this->l('Blocks'),
                'orderby' => false,
                'search' => false,
                'remove_onclick' => true,
                'callback_object' => 'Tools',
                'callback' => 'nl2br',
            ],
        ];

        $this->addRowAction('view');
        $this->addRowAction('edit');
        $this->addRowAction('delete');

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected hooks?'),
            ],
        ];
    }

    public function initHookPositionList()
    {
        $webixaContentHook = new WebixaContentHook(Tools::getValue(WebixaContentHook::$definition['primary']));

        $this->table = WebixaContentHook::$definition['table'] . '_block';
        $this->className = 'WebixaContentBlock';
        $this->identifier = WebixaContentBlock::$definition['primary'];
        $this->position_identifier = $this->identifier;
        $this->position_group_identifier = WebixaContentHook::$definition['primary'];
        $this->_defaultOrderBy = 'position';
        $this->_defaultOrderWay = 'ASC';

        self::$currentIndex .= '&' . $this->position_group_identifier . '=' . (int)$webixaContentHook->id;

        $this->lang = false;

        $this->_select = '
            h.hook,
            b.type,
            b.name,
            bl.title as block_title
        ';

        $this->_join = '
                JOIN `' . _DB_PREFIX_ . WebixaContentHook::$definition['table'] . '` h
                    ON (h.' . WebixaContentHook::$definition['primary'] . '=a.' . WebixaContentHook::$definition['primary'] . ')
                JOIN `' . _DB_PREFIX_ . WebixaContentBlock::$definition['table'] . '` b
                    ON (b.' . WebixaContentBlock::$definition['primary'] . '=a.' . WebixaContentBlock::$definition['primary'] . ')
                ' .
            (
                !Shop::isFeatureActive() ? '' :
                '
                INNER JOIN `' . _DB_PREFIX_ . WebixaContentBlock::$definition['table'] . '_shop` bs
                    ON (b.' . WebixaContentBlock::$definition['primary'] . '=bs.' . WebixaContentBlock::$definition['primary'] . ' AND bs.`id_shop`=' . (int)$this->context->shop->id . ')
                '
            )
            . '
                LEFT JOIN `' . _DB_PREFIX_ . WebixaContentBlock::$definition['table'] . '_lang` bl
                    ON (b.' . WebixaContentBlock::$definition['primary'] . '=bl.' . WebixaContentBlock::$definition['primary'] . ' AND bl.`id_lang`=' . (int)$this->context->language->id . ')
        ';

        $this->_group = false;
        $this->_where = ' AND a.`' . $this->position_group_identifier . '`=' . (int)$webixaContentHook->id;

        $this->fields_list = [
            $this->identifier => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
            ],
            'hook' => [
                'title' => $this->l('Hook'),
                'maxlength' => 50,
                'orderby' => false,
                'search' => false,
            ],
            'name' => [
                'title' => $this->l('Block name'),
                'filter_key' => 'b!name',
            ],
            'type' => [
                'title' => $this->l('Type'),
                'filter_key' => 'b!type',
            ],
            'template' => [
                'title' => $this->l('Template'),
            ],
            'position' => [
                'title' => $this->l('Position'),
                'filter_key' => 'a!position',
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'position' => 'position',
            ],
        ];

        $this->addRowAction('delete');
    }

    public function renderView()
    {
        return $this->renderList();
    }

    public function renderForm()
    {
        /** @var WebixaContentHook $obj */
        if (!($obj = $this->loadObject(true))) {
            return;
        }

        $this->show_form_cancel_button = false;
        $this->fields_form = [
            'tinymce' => true,
            'legend' => [
                'title' => Validate::isLoadedObject($obj) ? $this->l('Update Hook') : $this->l('Add Hook'),
                'icon' => 'icon-cogs',
            ],
            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Hook'),
                    'name' => 'hook',
                    'readonly' => Validate::isLoadedObject($obj),
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
            ],
            'buttons' => [
                [
                    'title' => $this->l('Cancel'),
                    'id' => $this->table . '_form_cancel_btn',
                    'href' => self::$currentIndex . '&token=' . $this->token,
                    'icon' => 'process-icon-cancel',
                ],
            ],
        ];

        return parent::renderForm();
    }

    public function renderHookBlockForm()
    {
        $webixaContentHook = new WebixaContentHook((int)Tools::getValue(WebixaContentHook::$definition['primary']));
        $webixaContentBlock = new WebixaContentBlock((int)Tools::getValue(WebixaContentBlock::$definition['primary']));

        if (!Validate::isLoadedObject($webixaContentBlock)) {
            return;
        }
        $this->show_form_cancel_button = false;
        $this->fields_form = [
            'tinymce' => true,
            'legend' => [
                'title' => $this->l('Assign content Block to Hook'),
                'icon' => 'icon-cogs',
            ],
            'input' => [
                [
                    'type' => 'select',
                    'label' => $this->l('Hook'),
                    'name' => WebixaContentHook::$definition['primary'],
                    'class' => 'input fixed-width-xxl',
                    'options' => [
                        'query' => WebixaContentHook::getAll(),
                        'id' => WebixaContentHook::$definition['primary'],
                        'name' => 'hook',
                    ],
                    'readonly' => Validate::isLoadedObject($webixaContentHook),
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Block'),
                    'name' => WebixaContentBlock::$definition['primary'],
                    'class' => 'input fixed-width-xxl',
                    'options' => [
                        'query' => WebixaContentBlock::getAll($this->context->language->id, $this->context->shop->id),
                        'id' => WebixaContentBlock::$definition['primary'],
                        'name' => 'name',
                    ],
                    'readonly' => Validate::isLoadedObject($webixaContentBlock),
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Template'),
                    'name' => 'template',
                    'class' => 'input fixed-width-xxl',
                    'options' => [
                        'query' => $this->module->getAvailableTemplatesByBlockType($webixaContentBlock->type),
                        'id' => 'id',
                        'name' => 'name',
                    ],
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
            ],
            'buttons' => [
                [
                    'title' => $this->l('Cancel'),
                    'id' => $this->table . '_form_cancel_btn',
                    'href' => self::$currentIndex . '&token=' . $this->token,
                    'icon' => 'process-icon-cancel',
                ],
            ],
        ];

        return parent::renderForm();
    }


    public function renderList()
    {
        return $this->module->prepareAdminInfoBanner() . parent::renderList();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Webixa content hooks');
    }

    public function initToolbar()
    {
        parent::initToolbar();

        if ('edit' == $this->display || 'add' == $this->display) {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => self::$currentIndex . '&token=' . $this->token,
                'desc' => $this->l('Back'),
                'icon' => 'process-icon-back',
            ];
        } elseif ('view' == $this->display) {
            $this->toolbar_btn['new'] = [
                'href' => self::$currentIndex . '&add' . $this->table . '&token=' . $this->token,
                'desc' => $this->l('Add new'),
            ];
        }
    }

    public function setMedia($isNewTheme = false)
    {
        if (!$this->module->active && version_compare(_PS_VERSION_, '8.0.2', '>=')) {
            $this->module->hookDisplayBackOfficeHeader([]);
        }
        parent::setMedia($isNewTheme);
        $this->addJqueryUI([
            'ui.core',
        ]);
        $this->addJqueryPlugin('tablednd');
        if ($isNewTheme) {
            $this->registerJavascript('dnd', _PS_JS_DIR_ . 'admin/dnd.js', ['position' => 'bottom', 'priority' => 150]);
        } else {
            $this->addJS(_PS_JS_DIR_ . 'admin/dnd.js');
        }
    }

    public function postProcess()
    {
        $obj = new WebixaContentHook(WebixaContentHook::$definition['primary']);
        $ret = parent::postProcess();
        if (!$this->ajax) {
            if (!empty($this->action) && method_exists($this, 'process' . ucfirst(Tools::toCamelCase($this->action)))) {
                $this->module->clearModuleCache('*', $this->module->name . '|' . $obj->hook);
            }
        }

        return $ret;
    }

    public function processSaveHookBlock()
    {
        $webixaContentHook = new WebixaContentHook(Tools::getValue(WebixaContentHook::$definition['primary']));
        $webixaContentBlock = new WebixaContentBlock(Tools::getValue(WebixaContentBlock::$definition['primary']));
        $template = Tools::getValue('template', false);
        if (!Validate::isLoadedObject($webixaContentBlock)) {
            $this->errors[] = $this->l('Block is required');
        } elseif (!Validate::isLoadedObject($webixaContentHook)) {
            $this->errors[] = $this->l('Hook is required');
        } elseif (!in_array($template, array_keys($this->module->getAvailableTemplatesByBlockType($webixaContentBlock->type)))) {
            $this->errors[] = $this->l('Template is required');
        }
        if (empty($this->errors)) {
            $insertData = [
                WebixaContentHook::$definition['primary'] => (int)$webixaContentHook->id,
                WebixaContentBlock::$definition['primary'] => (int)$webixaContentBlock->id,
                'position' => (int)WebixaContentHook::getLastPosition($webixaContentHook->id),
                'template' => pSQL($template)
            ];
            if (Db::getInstance()->execute('
                INSERT INTO `' . _DB_PREFIX_ . WebixaContentHook::$definition['table'] . '_block` (' . implode(',', array_keys($insertData)) . ')
                VALUES
                ("' . implode('","', $insertData) . '")
                ON DUPLICATE KEY UPDATE `template` = "' . $insertData['template'] . '"
            ')) {
                Tools::redirectAdmin(
                    $this->context->link->getAdminLink('AdminWebixaContentHook') .
                        '&' . WebixaContentHook::$definition['primary'] . '=' . $webixaContentHook->id .
                        '&view' . WebixaContentHook::$definition['table'] .
                        '&conf=3'
                );
            }
        }

        $this->display = 'addHookBlock';

        return false;
    }

    public function processDeleteHookBlock()
    {
        $webixaContentHook = new WebixaContentHook(Tools::getValue(WebixaContentHook::$definition['primary']));
        $webixaContentBlock = new WebixaContentBlock(Tools::getValue(WebixaContentBlock::$definition['primary']));
        if (!Validate::isLoadedObject($webixaContentBlock)) {
            $this->errors[] = $this->l('Block is missing');
        } elseif (!Validate::isLoadedObject($webixaContentHook)) {
            $this->errors[] = $this->l('Hook is missing');
        }
        if (empty($this->errors)) {
            if (Db::getInstance()->delete(
                WebixaContentHook::$definition['table'] . '_block',
                WebixaContentHook::$definition['primary'] . '=' . (int) $webixaContentHook->id .
                    ' AND ' . WebixaContentBlock::$definition['primary'] . '=' . (int) $webixaContentBlock->id
            )) {
                WebixaContentHook::cleanPositions($webixaContentHook->hook);
                Tools::redirectAdmin(
                    $this->context->link->getAdminLink('AdminWebixaContentHook') .
                        '&' . WebixaContentHook::$definition['primary'] . '=' . $webixaContentHook->id .
                        '&view' . WebixaContentHook::$definition['table'] .
                        '&conf=1'
                );
            }
        }

        return false;
    }

    public function ajaxProcessUpdatePositions()
    {
        $obj = new WebixaContentHook(WebixaContentHook::$definition['primary']);
        if (
            !($positions = Tools::getValue(WebixaContentBlock::$definition['table'], false)) ||
            !WebixaContentHook::updatePositions($positions)
        ) {
            $this->ajaxDie(
                json_encode(
                    [
                        'hasError' => true,
                        'errors' => $this->l('Update position failed'),
                    ]
                )
            );
        }

        $this->module->clearModuleCache('*', $this->module->name . '|' . $obj->hook);
        $this->ajaxDie(
            json_encode(
                [
                    'success' => true,
                ]
            )
        );
    }

    protected function ajaxDie($value = null, $controller = null, $method = null)
    {
        if (ob_get_length() > 0) {
            ob_end_clean();
        }
        header('Content-Type: application/json');

        if (version_compare(_PS_VERSION_, '1.6.1', '>=')) {
            return parent::ajaxDie($value, $controller, $method);
        }

        header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        echo $value;
        exit;
    }
}
