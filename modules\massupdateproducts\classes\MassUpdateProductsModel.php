<?php

/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateProductsModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateProductsModel');
        parent::__construct($module, $object, $context);
        //name
        $this->fields['name'] = array(
            'display_name' => $this->module->l('Name', $this->languages_name),
            'name' => 'name',
            'active' => true,
            'type' => self::TYPE_STR,
            'lang' => true
        );

        //manufacturer
        $this->fields['manufacturer'] = array(
            'display_name' => $this->module->l('Manufacturer', $this->languages_name),
            'name' => 'manufacturer',
            'active' => true,
            'type' => self::TYPE_SEL,
            'sort' => true,
            'base_name' => 'p.id_manufacturer'
        );

        $manufacturers = Manufacturer::getManufacturers();
        $manufacturers_select = array(
            0 => $this->module->l('none', $this->languages_name)
        );
        if ($manufacturers)
            foreach ($manufacturers as $manufacturer) $manufacturers_select[$manufacturer['id_manufacturer']] = $manufacturer['name'];
        $this->fields['manufacturer']['select'] = $manufacturers_select;

        //supplier
        $this->fields['supplier'] = array(
            'display_name' => $this->module->l('Supplier', $this->languages_name),
            'name' => 'supplier',
            'active' => true,
            'type' => self::TYPE_SEL,
            'sort' => true,
            'base_name' => 'p.id_supplier'
        );

        $suppliers = Supplier::getSuppliers();
        $suppliers_select = array(
            0 => $this->module->l('none', $this->languages_name)
        );
        if ($suppliers)
            foreach ($suppliers as $supplier) $suppliers_select[$supplier['id_supplier']] = $supplier['name'];
        $this->fields['supplier']['select'] = $suppliers_select;

        //price
        $this->fields['price'] = array(
            'display_name' => $this->module->l('Price', $this->languages_name),
            'name' => 'price',
            'active' => true,
            'type' => self::TYPE_PRI,
            'combination' => true,
            'sort' => true,
            'base_name' => 'ps.price'
        );

        //wholesale_price
        $this->fields['wholesale_price'] = array(
            'display_name' => $this->module->l('Wholesale price', $this->languages_name),
            'name' => 'wholesale_price',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'combination' => true,
            'sort' => true,
            'base_name' => 'ps.wholesale_price'
        );

        //wholesale_price
        $this->fields['unit_price'] = array(
            'display_name' => $this->module->l('Unit price', $this->languages_name),
            'name' => 'unit_price',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'combination' => true,
            'sort' => true,
            'base_name' => 'ps.unit_price'
        );

        //quantity
        $this->fields['quantity'] = array(
            'display_name' => $this->module->l('Quantity', $this->languages_name),
            'name' => 'quantity',
            'active' => true,
            'type' => self::TYPE_INT,
            'combination' => true,
            'sort' => true,
            'base_name' => !$this->object->shop_group->share_stock ? 'sa.quantity ' : 'p.quantity '
        );

        //minimal_quantity
        $this->fields['minimal_quantity'] = array(
            'display_name' => $this->module->l('Minimal quantity', $this->languages_name),
            'name' => 'minimal_quantity',
            'active' => true,
            'combination' => true,
            'type' => self::TYPE_INT,
            'sort' => true,
            'base_name' => 'p.minimal_quantity'
        );

        //tax
        $this->fields['tax'] = array(
            'display_name' => $this->module->l('Tax', $this->languages_name),
            'name' => 'tax',
            'active' => true,
            'type' => self::TYPE_SEL,
            'sort' => true,
            'base_name' => 'ps.id_tax_rules_group'
        );

        $tax_selected = array(
            0 => $this->module->l('none', $this->languages_name)
        );
        if ($this->object->tax_rules_group) {
            foreach ($this->object->tax_rules_group as $tax) {
                $tax_selected[$tax['id_tax_rules_group']] = $tax['name'];
            }
        }
        $this->fields['tax']['select'] = $tax_selected;

        //width
        $this->fields['width'] = array(
            'display_name' => $this->module->l('Width', $this->languages_name),
            'name' => 'width',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'combination' => false,
            'sort' => true,
            'base_name' => 'p.width'
        );
        //height
        $this->fields['height'] = array(
            'display_name' => $this->module->l('Height', $this->languages_name),
            'name' => 'height',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'combination' => false,
            'sort' => true,
            'base_name' => 'p.height'
        );
        //depth
        $this->fields['depth'] = array(
            'display_name' => $this->module->l('Depth', $this->languages_name),
            'name' => 'depth',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'combination' => false,
            'sort' => true,
            'base_name' => 'p.depth'
        );

        //weight
        $this->fields['weight'] = array(
            'display_name' => $this->module->l('Weight', $this->languages_name),
            'name' => 'weight',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'combination' => true,
            'sort' => true,
            'base_name' => 'p.weight'
        );

        //code
        $this->fields['code'] = array(
            'display_name' => $this->module->l('Code', $this->languages_name),
            'name' => 'code',
            'active' => true,
            'type' => self::TYPE_STR,
            'validate' => 'isReference',
            'combination' => true,
            'sort' => true,
            'base_name' => 'p.reference'
        );

        //ean
        $this->fields['ean'] = array(
            'display_name' => $this->module->l('Ean', $this->languages_name),
            'name' => 'ean',
            'active' => true,
            'type' => self::TYPE_STR,
            'validate' => 'isEan',
            'combination' => true,
            'sort' => true,
            'base_name' => 'p.ean13'
        );

        $this->fields['cn'] = array(
            'display_name' => $this->module->l('Kod CN', $this->languages_name),
            'name' => 'cn',
            'active' => true,
            'type' => self::TYPE_STR,
            'validate' => 'isReference',
            'sort' => true,
            'base_name' => 'ps.cn'
        );

        //condition
        $this->fields['condition'] = array(
            'display_name' => $this->module->l('Condition', $this->languages_name),
            'name' => 'condition',
            'active' => true,
            'type' => self::TYPE_SEL,
            'sort' => true,
            'base_name' => 'ps.condition'
        );

        $conditions = array(
            'new' => $this->module->l('New', $this->languages_name),
            'used' => $this->module->l('Used', $this->languages_name),
            'refurbished' => $this->module->l('Refurbished', $this->languages_name)
        );

        $this->fields['condition']['select'] = $conditions;

        //available_for_order
        $this->fields['available_for_order'] = array(
            'display_name' => $this->module->l('Available for order', $this->languages_name),
            'name' => 'available_for_order',
            'active' => true,
            'type' => self::TYPE_BOOL,
            'sort' => true,
            'base_name' => 'ps.available_for_order'
        );

        //show_price
        $this->fields['show_price'] = array(
            'display_name' => $this->module->l('Show price', $this->languages_name),
            'name' => 'show_price',
            'active' => true,
            'type' => self::TYPE_BOOL,
            'sort' => true,
            'base_name' => 'ps.show_price'
        );

        //online_only
        $this->fields['online_only'] = array(
            'display_name' => $this->module->l('Online only', $this->languages_name),
            'name' => 'online_only',
            'active' => true,
            'type' => self::TYPE_BOOL,
            'sort' => true,
            'base_name' => 'ps.online_only'
        );

        //additional_shipping_cost
        $this->fields['additional_shipping_cost'] = array(
            'display_name' => $this->module->l('Additional Shipping Cost', $this->languages_name),
            'name' => 'additional_shipping_cost',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'validate' => 'isPrice',
            'sort' => true,
            'base_name' => 'p.additional_shipping_cost'
        );

        //on_sale
        $this->fields['on_sale'] = array(
            'display_name' => $this->module->l('On sale', $this->languages_name),
            'name' => 'on_sale',
            'active' => true,
            'type' => self::TYPE_BOOL,
            'sort' => true,
            'base_name' => 'p.on_sale'
        );

        //active
        $this->fields['active'] = array(
            'display_name' => $this->module->l('Active', $this->languages_name),
            'name' => 'active',
            'active' => true,
            'type' => self::TYPE_BOOL,
            'sort' => true,
            'base_name' => 'p.active'
        );

        $this->fields['additional_delivery_times'] = array(
            'display_name' => $this->module->l('Czas dostawy', $this->languages_name),
            'name' => 'additional_delivery_times',
            'active' => true,
            'type' => self::TYPE_SEL,
            'sort' => true,
            'base_name' => 'p.additional_delivery_times'
        );
        $delivery[0] = 'Żaden';
        $delivery[1] = 'Domyślny czas dostawy';
        $delivery[2] = 'Określony czas dostawy do tego produktu';
        $this->fields['additional_delivery_times']['select'] = $delivery;
        $this->fields['additional_delivery_times']['value'] = 1;

        $this->fields['delivery_in_stock'] = array(
            'display_name' => $this->module->l('Czas dostawy produktów dostępnych w magazynie:', $this->languages_name),
            'name' => 'delivery_in_stock',
            'active' => true,
            'type' => self::TYPE_STR,
            'lang' => true
        );

        $this->fields['delivery_out_stock'] = array(
            'display_name' => $this->module->l('Czas dostawy wyprzedanych produktów z możliwością rezerwacji:', $this->languages_name),
            'name' => 'delivery_out_stock',
            'active' => true,
            'type' => self::TYPE_STR,
            'lang' => true
        );
    }

    public function display($result)
    {
        $ids_product = $result['result'];
        $result['result'] = '';
        $result['table'] = true;

        if ($ids_product)
            foreach ($ids_product as $product_arr) {
                $product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['dates']['products_count'] --;
                    continue;
                }
                $product->id_shop_object = $product_arr['id_shop'];
                $result['result'] .= $this->displayProduct($product);

                /* $combination_tmp = array();

                  if (($combinations = $product->getAttributeCombinations($this->context->language->id)))
                  {
                  foreach ($combinations as $combination_arr)
                  {
                  if (array_key_exists($combination_arr['id_product_attribute'], $combination_tmp))
                  continue;
                  $combination = new Combination($combination_arr['id_product_attribute'], null, $product->id_shop_object);
                  if (!ValidateCore::isLoadedObject($combination))
                  continue;
                  $result['result'] .= $this->displayCombination($product, $combination);
                  $combination_tmp[$combination->id] = true;
                  }
                  }
                 */
            }

        return $result;
    }

    public function displayProduct(&$product)
    {
        parent::displayProduct($product);

        if (empty($product->id) || $product->id == 0) {
            return '';
        }

//        $product->image_link = $this->context->link->getImageLink($this->object->img_type, $product->getCoverWs(), $this->object->img_type);
        $cover = Product::getCover($product->id);

        $link_rewrite = $product->link_rewrite[$this->context->cookie->id_lang];
        $id_image = isset($cover['id_image']) ? (int)$cover['id_image'] : 0;


        $product->image_link = $this->context->link->getImageLink($link_rewrite, $id_image, 'large_default');
        $product->quantity = StockAvailable::getQuantityAvailableByProduct($product->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $product->full_name = $product->name[$this->context->language->id];
        $product->has_combination = !!$product->getAttributeCombinations($this->context->language->id);

        $this->fields['name']['value'] = $product->name;
        $this->fields['delivery_in_stock']['value'] = $product->delivery_in_stock;
        $this->fields['delivery_out_stock']['value'] = $product->delivery_out_stock;

        $this->fields['manufacturer']['value'] = $product->id_manufacturer;

        $this->fields['supplier']['value'] = $product->id_supplier;

        $this->fields['price']['value'] = array(
            0 => round($product->price, 2),
            1 => round($product->price * (1 + ($product->rate / 100)), 2)
        );
        $this->fields['quantity']['value'] = $product->quantity;
        $this->fields['minimal_quantity']['value'] = $product->minimal_quantity;
        $this->fields['tax']['value'] = $product->id_tax_rules_group;

        $this->fields['tax']['extra'] = $this->object->tax_rates;

        $this->fields['width']['value'] = $product->width;
        $this->fields['height']['value'] = $product->height;
        $this->fields['depth']['value'] = $product->depth;
        $this->fields['weight']['value'] = $product->weight;
        $this->fields['code']['value'] = $product->reference;
        $this->fields['ean']['value'] = $product->ean13;
        $this->fields['additional_delivery_times']['value'] = $product->additional_delivery_times;
        $cn = '';
        if (isset($product->cn)) {
            $cn = $product->cn;
        }
        $this->fields['cn']['value'] = $cn;
        $this->fields['additional_shipping_cost']['value'] = $product->additional_shipping_cost;
        $this->fields['on_sale']['value'] = $product->on_sale;
        $this->fields['active']['value'] = $product->active;
        $this->fields['condition']['value'] = $product->condition;
        $this->fields['available_for_order']['value'] = $product->available_for_order;
        $this->fields['show_price']['value'] = $product->show_price;
        $this->fields['online_only']['value'] = $product->online_only;
        $this->fields['wholesale_price']['value'] = $product->wholesale_price;
        $this->fields['unit_price']['value'] = $product->unit_price;

        if (($product->getAttributeCombinations($this->context->language->id)))
            $this->fields['minimal_quantity']['display'] = false;
        else
            $this->fields['minimal_quantity']['display'] = true;

        $this->context->smarty->assign(array(
            'product' => $product,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
        ));

        return $this->object->createTemplate('tr-product.tpl')->fetch();
    }

    public function displayCombination(&$product, &$combination)
    {
        if (!parent::displayCombination($product, $combination)) {
            return '';
        }
        $cover = $combination->getWsImages();
        $combination->image_link = isset($cover[0]['id']) && $cover[0]['id'] ? $this->context->link->getImageLink(
                        $this->object->img_type, isset($cover[0]['id']) ? $cover[0]['id'] : 0, $this->object->img_type) : '';
        $combination->quantity = StockAvailable::getQuantityAvailableByProduct($product->id, $combination->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $names = $combination->getAttributesName($this->context->cookie->id_lang);
        $tmp_n = array();
        if ($names)
            foreach ($names as $name) $tmp_n[] = $name['name'];
        $combination->full_name = implode(' - ', $tmp_n);

        $this->fields['price']['value'] = array(
            0 => round($combination->price, 2),
            1 => round($combination->price * (1 + ($product->rate / 100)), 2)
        );
        $this->fields['code']['value'] = $combination->reference;
        $this->fields['quantity']['value'] = $combination->quantity;
        $this->fields['minimal_quantity']['value'] = $combination->minimal_quantity;
        $this->fields['minimal_quantity']['display'] = true;
        $this->fields['weight']['value'] = $combination->weight;
        $this->fields['wholesale_price']['value'] = $combination->wholesale_price;
        $this->fields['unit_price']['value'] = $combination->unit_price;
        $this->fields['ean']['value'] = $combination->ean13;

        $this->context->smarty->assign(array(
            'product' => $product,
            'combination' => $combination,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
            'widths' => MassUpdateProductsAbstract::getWidths()
        ));

        return $this->object->createTemplate('tr-combination.tpl')->fetch();
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product => $params) {
                $result[$id_product] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );

                $id_shop = $this->context->shop->id;

                $product_params = array_key_exists('data', $params) ? $params['data'] : null;
                $combinations = array_key_exists('combinations', $params) ? $params['combinations'] : null;

                $product = new Product($id_product, true, null, $id_shop);

                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                $product->id_shop_list = array($id_shop);

                if ($product_params) {
                    $languages = Language::getLanguages(false);

                    if ($languages) {
                        foreach ($languages as $language) {
                            if (isset($product_params['name_' . $language['iso_code']])) {
                                $product->name[$language['id_lang']] = $product_params['name_' . $language['iso_code']];
                            }
                            if (isset($product_params['delivery_in_stock_' . $language['iso_code']])) {
                                $product->delivery_in_stock[$language['id_lang']] = $product_params['delivery_in_stock_' . $language['iso_code']];
                            }
                            if (isset($product_params['delivery_out_stock_' . $language['iso_code']])) {
                                $product->delivery_out_stock[$language['id_lang']] = $product_params['delivery_out_stock_' . $language['iso_code']];
                            }
                        }
                    }

                    $product->active = isset($product_params['active']) ? $product_params['active'] : $product->active;
                    $product->additional_shipping_cost = isset($product_params['additional_shipping_cost']) ?
                            $product_params['additional_shipping_cost'] : $product->additional_shipping_cost;
                    $product->reference = isset($product_params['code']) ? $product_params['code'] : $product->reference;
                    $product->ean13 = isset($product_params['ean']) ? $product_params['ean'] : $product->ean13;
//                    $product->cn = isset($product_params['cn']) ? $product_params['cn'] : $product->cn;
                    $product->id_manufacturer = isset($product_params['manufacturer']) ? $product_params['manufacturer'] : $product->id_manufacturer;
                    $product->id_supplier = isset($product_params['supplier']) ? $product_params['supplier'] : $product->id_supplier;
                    $product->on_sale = isset($product_params['on_sale']) ? $product_params['on_sale'] : $product->on_sale;
                    $product->price = isset($product_params['price']) ? $product_params['price'] : $product->price;
                    $product->id_tax_rules_group = isset($product_params['tax']) ? $product_params['tax'] : $product->id_tax_rules_group;
                    $product->width = isset($product_params['width']) ? $product_params['width'] : $product->width;
                    $product->height = isset($product_params['height']) ? $product_params['height'] : $product->height;
                    $product->depth = isset($product_params['depth']) ? $product_params['depth'] : $product->depth;
                    $product->weight = isset($product_params['weight']) ? $product_params['weight'] : $product->weight;
                    if ($this->object->shop_group->share_stock)
                        $product->quantity = isset($product_params['quantity']) ? $product_params['quantity'] : $this->quantity;
                    elseif (isset($product_params['quantity']))
                        StockAvailable::setQuantity($id_product, 0, $product_params['quantity']);

                    $product->minimal_quantity = isset($product_params['minimal_quantity']) ? $product_params['minimal_quantity'] : $product->minimal_quantity;
                    $product->condition = isset($product_params['condition']) ? $product_params['condition'] : $product->condition;
                    $product->available_for_order = isset($product_params['available_for_order']) ?
                            $product_params['available_for_order'] : $product->available_for_order;
                    $product->show_price = isset($product_params['show_price']) ? $product_params['show_price'] : $product->show_price;
                    $product->online_only = isset($product_params['online_only']) ? $product_params['online_only'] : $product->online_only;
                    $product->wholesale_price = isset($product_params['wholesale_price']) ? $product_params['wholesale_price'] : $product->wholesale_price;
                    $product->unit_price = isset($product_params['unit_price']) ? $product_params['unit_price'] : $product->unit_price;
                    $product->additional_delivery_times = isset($product_params['additional_delivery_times']) ? $product_params['additional_delivery_times'] : $product->additional_delivery_times;

                    $errors = $product->validateFields(false, true);
                    $errors2 = $product->validateFieldsLang(false, true);
                    if ($errors !== true || $errors2 !== true) {
                        $result[$id_product]['message'] = '';
                        if ($errors !== true)
                            $result[$id_product]['message'] .= '<p style="color: #FFF;">'.(is_bool($errors) ?
                                            $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)).'</p>';
                        if ($errors2 !== true)
                            $result[$id_product]['message'] .= '<p style="color: #FFF;">'.(is_bool($errors2) ?
                                            $this->module->l('Validate error', $this->languages_name) : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)).'</p>';
                        continue;
                    }
                    else {
                        if ($product->update()) {
                            Db::getInstance()->update('product', array(
                                'id_supplier' => (int)$product_params['supplier']
                            ), 'id_product = '.$id_product);

                            $supplier = Db::getInstance()->getRow('SELECT * FROM `'._DB_PREFIX_.'product_supplier` WHERE `id_product` = '.(int)$id_product.' AND `id_supplier` = '.(int)$product_params['supplier']);
                            if (empty($supplier)) {
                                Db::getInstance()->insert('product_supplier', array(
                                    'id_supplier' => (int)$product_params['supplier'],
                                    'id_product' => $id_product,
                                    'id_product_attribute' => 0,
                                    'id_currency' => 1
                                ));
                            }
                            $result[$id_product]['error'] = false;
                            if (!(int)$this->object->shop_group->share_stock)
                                $result[$id_product]['message'] = $this->displayProduct($product);
                        }
                        else {
                            $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                            continue;
                        }
                    }
                } else {
                    $result[$id_product]['error'] = false;
                    $result[$id_product]['message'] = $this->displayProduct($product);
                }

                if ($combinations) {
                    foreach ($combinations as $id_combination => $combination_params) {
                        $result[$id_product]['combinations'][$id_combination] = array(
                            'error' => true,
                            'message' => ''
                        );
                        $combination = new Combination($id_combination, null, $id_shop);

                        if (!Validate::isLoadedObject($combination)) {
                            $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Combination not found', $this->languages_name);
                            continue;
                        }

                        $combination->id_shop_list = array($id_shop);

                        $combination->reference = isset($combination_params['code']) ? $combination_params['code'] : $combination->reference;
                        $combination->ean13 = isset($combination_params['ean']) ? $combination_params['ean'] : $combination->ean13;
                        $combination->price = isset($combination_params['price']) ? $combination_params['price'] : $combination->price;
                        $combination->weight = isset($combination_params['weight']) ? $combination_params['weight'] : $combination->weight;
                        $combination->wholesale_price = isset($combination_params['wholesale_price']) ?
                                $combination_params['wholesale_price'] : $combination->wholesale_price;

                        if (!!(int)$this->object->shop_group->share_stock)
                            $combination->quantity = isset($combination_params['quantity']) ? $combination_params['quantity'] : $combination->quantity;
                        else {
                            StockAvailable::setQuantity($id_product, $id_combination, $combination_params['quantity']);
                        }
                        $combination->minimal_quantity = isset($combination_params['minimal_quantity']) ?
                                $combination_params['minimal_quantity'] : $combination->minimal_quantity;

                        $errors = $combination->validateFields(false, true);
                        if ($errors !== true) {
                            $result[$id_product]['combinations'][$id_combination]['message'] = is_bool($errors) ?
                                    $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode('<br>', $errors) : $errors);
                            continue;
                        } else {
                            if ($combination->update()) {
                                $result[$id_product]['combinations'][$id_combination]['error'] = false;
                                $result[$id_product]['combinations'][$id_combination]['message'] = $this->displayCombination($product, $combination);
                            } else {
                                $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Problem with update', $this->languages_name);
                                continue;
                            }
                        }

                        if (!!(int)$this->object->shop_group->share_stock) {
                            if (Db::getInstance()->getValue('SELECT id_stock_available FROM `'._DB_PREFIX_.'stock_available` WHERE `id_product` = '
                                            .pSQL($id_product).' AND `id_product_attribute` = '
                                            .pSQL($id_combination).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id)))
                                Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'stock_available` SET `quantity` = '
                                        .pSQL(isset($combination_params['quantity']) ? $combination_params['quantity'] : $combination->quantity)
                                        .' WHERE `id_product` = '.pSQL($id_product).' AND `id_product_attribute` = '
                                        .pSQL($id_combination).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id));
                            else
                                Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_
                                        .'stock_available` (`id_product`, `id_product_attribute`, `id_shop`, `id_shop_group`, `quantity`, `depends_on_stock`, `out_of_stock`) VALUES ('
                                        .pSQL($id_product).','.pSQL($id_combination).',0,'.pSQL($this->object->shop_group->id).','.pSQL(isset($combination_params['quantity']) ?
                                                        $combination_params['quantity'] : $combination->quantity).',0,2)');
                            Cache::clean('StockAvailable::getQuantityAvailableByProduct_'.(int)$id_product.'*');
                            $new_combination = new Combination($id_combination, null, $id_shop);
                            $new_combination->quantity = isset($combination_params['quantity']) ? $combination_params['quantity'] : $combination->quantity;
                            $result[$id_product]['combinations'][$id_combination]['message'] = $this->displayCombination($product, $new_combination);
                        }
                    }
                }

                if (!!(int)$this->object->shop_group->share_stock) {
                    if (Db::getInstance()->getValue('SELECT id_stock_available FROM `'._DB_PREFIX_.'stock_available` WHERE `id_product` = '.pSQL($id_product)
                                    .' AND `id_product_attribute` = '
                                    .pSQL(0).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id)))
                        Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'stock_available` SET `quantity` = '.pSQL(isset($product_params['quantity']) ?
                                                $product_params['quantity'] : $this->quantity).' WHERE `id_product` = '.pSQL($id_product).' AND `id_product_attribute` = '
                                .pSQL(0).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id));
                    else
                        Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_
                                .'stock_available` (`id_product`, `id_product_attribute`, `id_shop`, `id_shop_group`, `quantity`, `depends_on_stock`, `out_of_stock`) VALUES ('
                                .pSQL($id_product).',0,0,'.pSQL($this->object->shop_group->id).','
                                .pSQL(isset($product_params['quantity']) ? $product_params['quantity'] : $this->quantity).',0,2)');
                    Cache::clean('StockAvailable::getQuantityAvailableByProduct_'.(int)$id_product.'*');
                    $new_product = new Product($id_product, false, null, $id_shop);
                    $new_product->quantity = isset($product_params['quantity']) ? $product_params['quantity'] : $this->quantity;
                    $result[$id_product]['message'] = $this->displayProduct($new_product);
                }
            } else
            $end = true;

        return array(
            'raport' => $result,
            'end' => $end
        );
    }

    public function hasCombination()
    {
        return true;
    }

}
