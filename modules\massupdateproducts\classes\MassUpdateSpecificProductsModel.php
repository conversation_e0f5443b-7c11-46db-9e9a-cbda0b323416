<?php

/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateSpecificProductsModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateSpecificProductsModel');
        parent::__construct($module, $object, $context);

        //promotion_price
        $this->fields['promotion_price'] = array(
            'display_name' => $this->module->l('Promotion price', $this->languages_name),
            'name' => 'promotion_price',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'attr' => 'readonly style="border: none !important;width: 74px;box-shadow:none;background-color: transparent;text-align: center;"',
            'combination' => true,
            'none-copy' => true
        );

        //reduction
        $this->fields['reduction'] = array(
            'display_name' => $this->module->l('Reduction', $this->languages_name),
            'name' => 'reduction',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'combination' => true
        );

        //reduction_type
        $this->fields['reduction_type'] = array(
            'display_name' => $this->module->l('Reduction type', $this->languages_name),
            'name' => 'reduction_type',
            'active' => true,
            'type' => self::TYPE_SEL,
            'combination' => true
        );

        $this->fields['reduction_type']['select'] = array(
            0 => $this->module->l('none', $this->languages_name),
            1 => $this->module->l('amount', $this->languages_name),
            2 => $this->module->l('percent', $this->languages_name),
        );

        //old_price
        $this->fields['old_price'] = array(
            'display_name' => $this->module->l('Old price', $this->languages_name),
            'name' => 'old_price',
            'active' => true,
            'type' => self::TYPE_FLOAT,
            'attr' => 'readonly style="border: none !important;box-shadow:none;width: 74px;background-color: transparent;text-align: center;"',
            'combination' => true,
            'none-copy' => true
        );

        //date_from
        $this->fields['date_from'] = array(
            'display_name' => $this->module->l('Date from', $this->languages_name),
            'name' => 'date_from',
            'active' => true,
            'type' => self::TYPE_STR,
            'combination' => true
        );

        //date_to
        $this->fields['date_to'] = array(
            'display_name' => $this->module->l('Date to', $this->languages_name),
            'name' => 'date_to',
            'active' => true,
            'type' => self::TYPE_STR,
            'combination' => true
        );

        //remove
        $this->fields['remove'] = array(
            'display_name' => $this->module->l('Remove', $this->languages_name),
            'name' => 'remove',
            'active' => true,
            'type' => self::TYPE_REM,
            'combination' => true
        );

        $this->fields['promotion_price']['display'] = false;
        $this->fields['old_price']['display'] = false;
        $this->fields['remove']['display'] = false;
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product => $params) {
                $result[$id_product] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );

                $product_params = array_key_exists('data', $params) ? $params['data'] : null;
                $combinations = array_key_exists('combinations', $params) ? $params['combinations'] : null;

                $product = new Product($id_product);

                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                if ($product_params) {
                    $specific_price = new SpecificPrice(
                            Db::getInstance()->getValue('SELECT id_specific_price FROM `'._DB_PREFIX_.
                                    'specific_price` WHERE id_product = '.
                                    ((int)$product->id).' AND id_product_attribute = 0 AND id_shop IN (0, '.
                                    ((int)$product->id_shop_default).') ORDER BY id_shop DESC'
                    ));

                    $reduction = isset($product_params['reduction']) ? $product_params['reduction'] : 0;
                    if ((float)$reduction) {
                        $reduction_type = isset($product_params['reduction_type']) ? $product_params['reduction_type'] : 1;
                        $date_from = $product_params['date_from'];
                        $date_to = $product_params['date_to'];

                        if (!Validate::isLoadedObject($specific_price)) {
                            $specific_price->id_product = $product->id;
                            $specific_price->id_product_attribute = 0;
                            $specific_price->id_currency = 0;
                            $specific_price->id_country = 0;
                            $specific_price->id_group = 0;
                            $specific_price->id_customer = 0;
                        }

                        $specific_price->reduction = $reduction_type == 1 ? (float)$reduction : round(($reduction / 100), 2);
                        $specific_price->reduction_type = $reduction_type == 1 ? 'amount' : 'percentage';
                        $specific_price->from = $date_from ? $date_from : '0000-00-00 00:00:00';
                        $specific_price->to = $date_to ? $date_to : '0000-00-00 00:00:00';
                        $specific_price->price = -1;
                        $specific_price->from_quantity = 1;
                        $specific_price->id_shop = $product->id_shop_default;

                        $errors = $specific_price->validateFields(false, true);
                        if ($errors !== true) {
                            $result[$id_product]['message'] = '<p style="color: #FFF;">'.(is_bool($errors) ?
                                            $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)).'</p>';
                            continue;
                        } else {

                            if ($specific_price->save()) {
                                $result[$id_product]['error'] = false;
                                $result[$id_product]['message'] = $this->displayProduct($product);
                            } else {
                                if (ValidateCore::isLoadedObject($specific_price))
                                    $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                                else
                                    $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);

                                continue;
                            }
                        }
                    }
                    elseif (isset($product_params['reduction_type']) && $product_params['reduction_type'])
                        $result[$id_product]['message'] = $this->module->l('Enter reduction', $this->languages_name);
                }
                else {
                    $result[$id_product]['error'] = false;
                    $result[$id_product]['message'] = $this->displayProduct($product);
                }
                if ($combinations)
                    foreach ($combinations as $id_combination => $combination_params) {
                        $result[$id_product]['combinations'][$id_combination] = array(
                            'error' => true,
                            'message' => ''
                        );
                        $combination = new Combination($id_combination);

                        if (!Validate::isLoadedObject($combination)) {
                            $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Combination not found', $this->languages_name);
                            continue;
                        }

                        $specific_price = new SpecificPrice(
                                Db::getInstance()->getValue('SELECT id_specific_price FROM `'._DB_PREFIX_.
                                        'specific_price` WHERE id_product = '.
                                        ((int)$product->id).' AND id_product_attribute = '.
                                        ((int)$combination->id).' AND id_shop IN (0, '.
                                        ((int)$product->id_shop_default).') ORDER BY id_shop DESC'
                        ));

                        $reduction = isset($combination_params['reduction']) ? $combination_params['reduction'] : 0;
                        if ((float)$reduction) {
                            $reduction_type = isset($combination_params['reduction_type']) ? (int)$combination_params['reduction_type'] : 1;
                            $date_from = $combination_params['date_from'];
                            $date_to = $combination_params['date_to'];

                            if (!Validate::isLoadedObject($specific_price)) {
                                $specific_price->id_product = $product->id;
                                $specific_price->id_product_attribute = 0;
                                $specific_price->id_currency = 0;
                                $specific_price->id_country = 0;
                                $specific_price->id_group = 0;
                                $specific_price->id_customer = 0;
                            }

                            $specific_price->id_product = $product->id;
                            $specific_price->id_product_attribute = $combination->id;
                            $specific_price->reduction = $reduction_type == 1 ? (float)$reduction : round(($reduction / 100), 2);
                            $specific_price->reduction_type = $reduction_type == 1 ? 'amount' : 'percentage';
                            $specific_price->from = $date_from ? $date_from : '0000-00-00 00:00:00';
                            $specific_price->to = $date_to ? $date_to : '0000-00-00 00:00:00';
                            $specific_price->price = -1;
                            $specific_price->from_quantity = 1;
                            $specific_price->id_shop = $product->id_shop_default;

                            $errors = $specific_price->validateFields(false, true);
                            if ($errors !== true) {
                                $result[$id_product]['combinations'][$id_combination]['message'] = is_bool($errors) ?
                                        $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode('<br>', $errors) : $errors);
                                continue;
                            } else {
                                if ($specific_price->save()) {
                                    $result[$id_product]['combinations'][$id_combination]['error'] = false;
                                    $result[$id_product]['combinations'][$id_combination]['message'] = $this->displayCombination($product, $combination);
                                } else {
                                    $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Problem with update', $this->languages_name);
                                    continue;
                                }
                            }
                        } elseif (isset($combination_params['reduction_type']) && $combination_params['reduction_type'])
                            $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Enter reduction', $this->languages_name);
                    }
            } else
            $end = true;

        return array(
            'raport' => $result,
            'end' => $end
        );
    }

    public function display($result)
    {
        $ids_product = $result['result'];
        $result['result'] = '';
        $result['table'] = true;

        if ($ids_product)
            foreach ($ids_product as $product_arr) {
                $product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['dates']['products_count'] --;
                    continue;
                }

                $result['result'] .= $this->displayProduct($product);

                /* $combination_tmp = array();

                  if (($combinations = $product->getAttributeCombinations($this->context->language->id)))
                  foreach ($combinations as $combination_arr)
                  {
                  if (array_key_exists($combination_arr['id_product_attribute'], $combination_tmp))
                  continue;
                  $combination = new Combination($combination_arr['id_product_attribute']);
                  if (!ValidateCore::isLoadedObject($combination))
                  continue;

                  $promotion = (int)Tools::getValue('promotion', 0);

                  if ($promotion == 1)
                  if (!Db::getInstance()->getValue('SELECT id_specific_price FROM `'
                  ._DB_PREFIX_.'specific_price` WHERE id_product = '.
                  ((int)$product->id).' AND id_product_attribute = '.(int)$combination->id))
                  continue;

                  $result['result'] .= $this->displayCombination($product, $combination);
                  $combination_tmp[$combination->id] = true;
                  } */
            }

        return $result;
    }

    public function displayProduct(&$product)
    {
        $this->fields['promotion_price']['display'] = true;
        $this->fields['old_price']['display'] = true;
        $this->fields['remove']['display'] = true;
        parent::displayProduct($product);
        $product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
                        $this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
        $product->quantity = $this->object->shop_group->share_stock ? $product->quantity : StockAvailable::getQuantityAvailableByProduct($product->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $product->full_name = $product->name[$this->context->language->id];
        $product->has_combination = !!$product->getAttributeCombinations($this->context->language->id);

        $specific_price = new SpecificPrice(Db::getInstance()->getValue('SELECT id_specific_price FROM `'
                        ._DB_PREFIX_.'specific_price` WHERE id_product = '.
                        $product->id.' AND id_product_attribute = 0'));

        if (Validate::isLoadedObject($specific_price)) {
            $type = $specific_price->reduction_type == 'amount' ? 1 : 2;
            $old_price = $specific_price->price == -1 ? ($product->price) : ($specific_price->price);
            $this->fields['promotion_price']['value'] = round(($type == 1 ?
                            ($old_price - $specific_price->reduction) : ($old_price - ($old_price * $specific_price->reduction ))), 2);
            $this->fields['reduction']['value'] = round(($type == 1 ? $specific_price->reduction : ($specific_price->reduction * 100)), 2);
            $this->fields['reduction_type']['value'] = $type;
            $this->fields['old_price']['value'] = round($old_price, 2);
            $this->fields['date_from']['value'] = $specific_price->from;
            $this->fields['date_to']['value'] = $specific_price->to;
            $this->fields['remove']['value'] = $specific_price->id;
        } else {
            $this->fields['promotion_price']['value'] = '';
            $this->fields['reduction']['value'] = '';
            $this->fields['reduction_type']['value'] = 0;
            $this->fields['old_price']['value'] = $product->price;
            $this->fields['date_from']['value'] = '';
            $this->fields['date_to']['value'] = '';
            $this->fields['remove']['value'] = '';
            $this->fields['remove']['display'] = false;
        }

        $this->context->smarty->assign(array(
            'product' => $product,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages
        ));

        return $this->object->createTemplate('tr-product.tpl')->fetch();
    }

    public function displayCombination(&$product, &$combination)
    {
        $this->fields['promotion_price']['display'] = true;
        $this->fields['old_price']['display'] = true;
        $this->fields['remove']['display'] = true;
        if (!parent::displayCombination($product, $combination))
            return '';
        $cover = $combination->getWsImages();
        $combination->image_link = isset($cover[0]['id']) && $cover[0]['id'] ? $this->context->link->getImageLink(
                        $this->object->img_type, isset($cover[0]['id']) ? $cover[0]['id'] : 0, $this->object->img_type) : '';
        $combination->quantity = $this->object->shop_group->share_stock ?
                $combination->quantity : StockAvailable::getQuantityAvailableByProduct($product->id, $combination->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $names = $combination->getAttributesName($this->context->cookie->id_lang);
        $tmp_n = array();
        if ($names)
            foreach ($names as $name) $tmp_n[] = $name['name'];
        $combination->full_name = implode(' - ', $tmp_n);

        $specific_price = new SpecificPrice(Db::getInstance()->getValue('SELECT id_specific_price FROM `'
                        ._DB_PREFIX_.'specific_price` WHERE id_product = '.
                        $product->id.' AND id_product_attribute = '.(int)$combination->id));

        if (Validate::isLoadedObject($specific_price)) {
            $type = $specific_price->reduction_type == 'amount' ? 1 : 2;
            $old_price = $specific_price->price == -1 ? ($product->price + $combination->price) : ($specific_price->price);
            $this->fields['promotion_price']['value'] = round(($type == 1 ?
                            ($old_price - $specific_price->reduction) : ($old_price - ($old_price * $specific_price->reduction ))), 2);
            $this->fields['reduction']['value'] = round(($type == 1 ? $specific_price->reduction : ($specific_price->reduction * 100)), 2);
            $this->fields['reduction_type']['value'] = $type;
            $this->fields['old_price']['value'] = round($old_price, 2);
            $this->fields['date_from']['value'] = $specific_price->from;
            $this->fields['date_to']['value'] = $specific_price->to;
            $this->fields['remove']['value'] = $specific_price->id;
        } else {
            $this->fields['promotion_price']['value'] = '';
            $this->fields['reduction']['value'] = '';
            $this->fields['reduction_type']['value'] = 0;
            $this->fields['old_price']['value'] = $product->price + $combination->price;
            $this->fields['date_from']['value'] = '';
            $this->fields['date_to']['value'] = '';
            $this->fields['remove']['value'] = '';
            $this->fields['remove']['display'] = false;
        }

        $this->context->smarty->assign(array(
            'product' => $product,
            'combination' => $combination,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
            'widths' => MassUpdateProductsAbstract::getWidths()
        ));

        return $this->object->createTemplate('tr-combination.tpl')->fetch();
    }

    public function remove()
    {
        $specific = new SpecificPrice((int)Tools::getValue('id_specific', 0));
        $message = '';
        $error = true;
        if (!Validate::isLoadedObject($specific))
            $message = $this->module->l('Specific price not found', $this->languages_name);
        else {
            if ($specific->delete()) {
                $error = false;
                $message = $this->module->l('Specific price removed', $this->languages_name);
            } else
                $message = $this->module->l('Problem woth remove', $this->languages_name);
        }
        return array(
            'error' => $error,
            'message' => $message
        );
    }

    public function hasCombination()
    {
        return true;
    }

}
