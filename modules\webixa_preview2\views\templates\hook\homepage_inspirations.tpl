{if isset($homepage_inspirations) && $homepage_inspirations}
<div id="homepage-inspirations" class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-8 text-center">{l s='Our Inspirations' d='Modules.Webixapreview.Shop'}</h2>

    {foreach from=$homepage_inspirations item=inspiration name=inspirations}
        {assign var="isEven" value=($smarty.foreach.inspirations.iteration|intval) % 2 == 0}

        <div class="inspiration-block mb-12 rounded-lg overflow-hidden shadow-lg"
             style="background-color: {$inspiration.rgb_color|escape:'htmlall':'UTF-8'};">

            <div class="flex flex-col md:flex-row {if $isEven}flex-row-reverse{/if}">
                <div class="w-full md:w-1/2 p-6 justify-center inspiration-text-content" style="flex: 0 0 50%;" data-bg-color="{$inspiration.rgb_color|escape:'htmlall':'UTF-8'}">
                    <h3 class="text-xl font-bold mb-3 inspiration-title">{$inspiration.title|escape:'htmlall':'UTF-8'}</h3>
                    <div class="prose mb-4 inspiration-description">
                        {if isset($inspiration.short_description) && $inspiration.short_description}
                            {$inspiration.short_description|strip_tags}
                        {else}
                            {$inspiration.description|truncate:200:'...'|strip_tags}
                        {/if}
                    </div>
                    <a href="{$inspiration.url}" class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition duration-200 self-start inspiration-button">
                        {l s='Discover more' d='Modules.Webixapreview.Shop'}
                    </a>
                </div>                
                <div class="w-full md:w-1/2 relative" style="flex: 0 0 50%;">
                    {if isset($inspiration.images) && $inspiration.images|count > 0}
                        <div class="inspiration-slider relative" data-inspiration-id="{$inspiration.id_inspiration}">
                            <div class="slider-container overflow-hidden relative">
                                <div class="slider-track flex transition-transform duration-300">
                                    {foreach from=$inspiration.images item=image name=images}
                                        <div class="slider-slide w-full flex-shrink-0 {if $smarty.foreach.images.first}active{/if}" data-slide-index="{$smarty.foreach.images.index|intval}">
                                            <img src="{if $smarty.foreach.images.first}{if isset($image.url)}{$image.url}{else}{$inspiration_img_dir_uri}{$image.image}{/if}{else}data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=={/if}"
                                                 data-src="{if isset($image.url)}{$image.url}{else}{$inspiration_img_dir_uri}{$image.image}{/if}"
                                                 alt="{$inspiration.title|escape:'htmlall':'UTF-8'}"
                                                 class="w-full h-auto {if !$smarty.foreach.images.first}lazy-load{/if}">

                                            {* Hotspots for Products - only show for active slide *}
                                            {if (isset($image.products) && $image.products) || (isset($inspiration.products) && $inspiration.products)}
                                                {* Use image.products if available, otherwise use inspiration.products *}
                                                {if isset($image.products) && $image.products}
                                                    {assign var="productList" value=$image.products}
                                                {else}
                                                    {assign var="productList" value=$inspiration.products}
                                                {/if}

                                                {foreach from=$productList item=product}                                                    
                                                    {if isset($product.inspiration_image) && $product.inspiration_image != $image.image}
                                                        {continue}
                                                    {/if}
                                                    
                                                    {if isset($product.position_x) && isset($product.position_y)}
                                                        {assign var="posX" value=$product.position_x}
                                                        {assign var="posY" value=$product.position_y}
                                                    {else}
                                                        {continue}
                                                    {/if}

                                                    <div class="inspiration-hotspot"
                                                         style="left: {$posX}%; top: {$posY}%;"
                                                         data-id-product="{$product.id_product}"
                                                         data-toggle="tooltip"
                                                         data-placement="auto"
                                                         data-html="true"
                                                         data-animation="true"
                                                         data-trigger="manual"
                                                         data-container="body"
                                                         data-content='<div class="tooltip-product-content">
                                                           <div class="d-flex align-items-start">
                                                             <img src="{$product.image_url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="tooltip-product-image">
                                                             <div class="tooltip-product-info">
                                                               <h5 class="tooltip-product-name">{$product.name|escape:'htmlall':'UTF-8'}</h5>
                                                               <div class="tooltip-product-price">{$product.price}</div>
                                                               <div class="tooltip-product-actions">
                                                                 <form method="post" action="{url entity='cart' params=['add' => 1, 'id_product' => $product.id_product, 'token' => $static_token]}" style="margin: 0;">
                                                                   <input type="hidden" name="id_product" value="{$product.id_product}">
                                                                   <input type="hidden" name="qty" value="1">
                                                                   <button class="tooltip-btn tooltip-btn-primary" data-button-action="add-to-cart" type="submit">
                                                                     {l s='Add to cart' d='Shop.Theme.Actions'}
                                                                   </button>
                                                                 </form>
                                                               </div>
                                                             </div>
                                                           </div>
                                                         </div>'>
                                                        <span class="hotspot-cross">
                                                            +
                                                        </span>

                                                    </div>
                                                {/foreach}
                                            {/if}
                                        </div>
                                    {/foreach}
                                </div>

                                {* Slider Navigation Buttons *}
                                {if $inspiration.images|count > 1}
                                    <button class="slider-prev absolute left-0 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-r-lg p-2 shadow-md z-30 focus:outline-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="15 18 9 12 15 6"></polyline>
                                        </svg>
                                    </button>
                                    <button class="slider-next absolute right-0 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-l-lg p-2 shadow-md z-30 focus:outline-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="9 18 15 12 9 6"></polyline>
                                        </svg>
                                    </button>

                                    {* Slider Pagination Dots *}
                                    <div class="slider-pagination absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                                        {foreach from=$inspiration.images item=image name=dots}
                                            <button class="pagination-dot w-3 h-3 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100 focus:outline-none {if $smarty.foreach.dots.first}active{/if}"
                                                    data-slide-index="{$smarty.foreach.dots.index|intval}"></button>
                                        {/foreach}
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {elseif $inspiration.image_url}                        
                        <div class="relative inspiration-image-container">
                            <img src="{$inspiration.image_url}" alt="{$inspiration.title|escape:'htmlall':'UTF-8'}" class="w-full h-auto">
                            
                            {if isset($inspiration.products) && $inspiration.products}
                                {foreach from=$inspiration.products item=product}                                    
                                    {if isset($product.position_x) && isset($product.position_y)}
                                        {assign var="posX" value=$product.position_x}
                                        {assign var="posY" value=$product.position_y}
                                    {else}
                                        {continue}
                                    {/if}

                                    <div class="inspiration-hotspot"
                                         style="left: {$posX}%; top: {$posY}%;"
                                         data-id-product="{$product.id_product}">                                        
                                        <div class="hotspot-cross">
                                            <span>+</span>
                                        </div>

                                    </div>
                                {/foreach}
                            {/if}
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    {/foreach}
</div>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {    
    function adjustTextColors() {
        var containers = document.querySelectorAll('.inspiration-text-content');

        for (var i = 0; i < containers.length; i++) {
            var container = containers[i];
            var bgColor = container.getAttribute('data-bg-color') || '';

            // Simple check if background is dark (this is a simplified approach)
            var isDark = false;

            // Check if it starts with # and convert to lowercase
            if (bgColor.charAt(0) === '#') {
                bgColor = bgColor.toLowerCase();

                // Very simple check - if first char after # is 0-7, it's likely dark
                if ('01234567'.indexOf(bgColor.charAt(1)) > -1) {
                    isDark = true;
                }
            }
            // Check if it's rgb format with low values
            else if (bgColor.indexOf('rgb') === 0) {
                // If it contains "rgb(0," or "rgb(1," etc, it's likely dark
                if (bgColor.indexOf('rgb(0,') > -1 ||
                    bgColor.indexOf('rgb(1,') > -1 ||
                    bgColor.indexOf('rgb(2,') > -1 ||
                    bgColor.indexOf('rgb(3,') > -1) {
                    isDark = true;
                }
            }

            // Apply colors based on darkness
            if (isDark) {
                container.style.color = '#ffffff';
                var title = container.querySelector('.inspiration-title');
                var desc = container.querySelector('.inspiration-description');
                if (title) title.style.color = '#ffffff';
                if (desc) desc.style.color = '#f0f0f0';
            } else {
                container.style.color = '#333333';
                var title = container.querySelector('.inspiration-title');
                var desc = container.querySelector('.inspiration-description');
                if (title) title.style.color = '#333333';
                if (desc) desc.style.color = '#555555';
            }
        }
    }

    // Call the function
    adjustTextColors();

    // Initialize sliders
    const sliders = document.querySelectorAll('.inspiration-slider');

    sliders.forEach(function(slider) {
        const track = slider.querySelector('.slider-track');
        const slides = slider.querySelectorAll('.slider-slide');
        const prevBtn = slider.querySelector('.slider-prev');
        const nextBtn = slider.querySelector('.slider-next');
        const dots = slider.querySelectorAll('.pagination-dot');
        const slideCount = slides.length;
        let currentIndex = 0;

        // Skip initialization if only one slide
        if (slideCount <= 1) return;

        // Function to go to a specific slide
        function goToSlide(index) {
            if (index < 0) index = slideCount - 1;
            if (index >= slideCount) index = 0;

            currentIndex = index;

            // Update transform for slider track
            track.style.transform = 'translateX(-' + (currentIndex * 100) + '%)';

            // Update active states
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === currentIndex);

                // Load lazy images when slide becomes active or adjacent
                if (i === currentIndex || i === currentIndex - 1 || i === currentIndex + 1) {
                    const lazyImg = slide.querySelector('img.lazy-load');
                    if (lazyImg && lazyImg.dataset.src) {
                        lazyImg.src = lazyImg.dataset.src;
                        lazyImg.classList.remove('lazy-load');
                    }
                }
            });

            // Update pagination dots
            if (dots.length) {
                dots.forEach((dot, i) => {
                    dot.classList.toggle('active', i === currentIndex);
                    // Add visual indication for active dot
                    if (i === currentIndex) {
                        dot.classList.add('bg-blue-600');
                        dot.classList.remove('bg-white', 'bg-opacity-70');
                    } else {
                        dot.classList.remove('bg-blue-600');
                        dot.classList.add('bg-white', 'bg-opacity-70');
                    }
                });
            }
        }

        // Event listeners for navigation
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                goToSlide(currentIndex - 1);
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                goToSlide(currentIndex + 1);
            });
        }

        // Event listeners for pagination dots
        dots.forEach((dot, i) => {
            dot.addEventListener('click', function() {
                goToSlide(i);
            });
        });

        // Initialize first slide
        goToSlide(0);

        // Optional: Auto-advance slides every 5 seconds
        let autoplayInterval = setInterval(function() {
            goToSlide(currentIndex + 1);
        }, 5000);

        // Pause autoplay on hover
        slider.addEventListener('mouseenter', function() {
            clearInterval(autoplayInterval);
        });

        slider.addEventListener('mouseleave', function() {
            autoplayInterval = setInterval(function() {
                goToSlide(currentIndex + 1);
            }, 5000);
        });
    });
    
    if (typeof inspirationModule !== 'undefined') {
        inspirationModule.init();
    }

    const lazyImages = document.querySelectorAll('img.lazy-load');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const image = entry.target;
                    image.src = image.dataset.src;
                    image.classList.remove('lazy-load');
                    imageObserver.unobserve(image);
                }
            });
        });

        lazyImages.forEach(function(image) {
            imageObserver.observe(image);
        });
    } else {        
        lazyImages.forEach(function(image) {
            image.src = image.dataset.src;
            image.classList.remove('lazy-load');
        });
    }
});
</script>

<style>
.slider-container {
    width: 100%;
    overflow: hidden;
}
.slider-track {
    display: flex;
    width: 100%;
    transition: transform 0.5s ease;
}
.slider-slide {
    flex: 0 0 100%;
    width: 100%;
    transition: opacity 0.3s ease;
    position: relative;
}
.slider-slide:not(.active) {
    opacity: 0.7;
}
.pagination-dot.active {
    transform: scale(1.2);
}
.slider-prev, .slider-next {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 30 !important;
    transition: all 0.2s ease !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid rgba(0,0,0,0.2) !important;
}
.slider-prev {
    left: 10px !important;
}
.slider-next {
    right: 10px !important;
}
.slider-prev:hover, .slider-next:hover {
    transform: translateY(-50%) scale(1.1) !important;
    background-color: white !important;
}

.inspiration-hotspot {
    position: absolute;
    transform: translate(-50%, -50%);
    z-index: 100;
    cursor: pointer;
    width: 30px;
    height: 30px;
    pointer-events: auto;
}
.hotspot-cross {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    border: 3px solid #2fb5d2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    color: #2fb5d2;
    transition: all 0.3s ease;
    z-index: 500;
    animation: pulse 2s infinite;
}
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(47, 181, 210, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(47, 181, 210, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(47, 181, 210, 0);
    }
}
.inspiration-hotspot:hover .hotspot-cross {
    transform: scale(1.2) !important;
    background-color: #2fb5d2 !important;
    color: white !important;
    animation: none;
}




/* Bootstrap Tooltip Styles */
.tooltip {
    z-index: 99999 !important;
}

.tooltip .tooltip-inner {
    background-color: white !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    padding: 0 !important;
    max-width: 300px !important;
}

.tooltip .tooltip-arrow {
    border-top-color: #ddd !important;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow {
    border-bottom-color: #ddd !important;
}

.tooltip.bs-tooltip-left .tooltip-arrow {
    border-left-color: #ddd !important;
}

.tooltip.bs-tooltip-right .tooltip-arrow {
    border-right-color: #ddd !important;
}

.tooltip-product-content {
    padding: 15px;
    max-width: 300px;
    text-align: left;
}

.tooltip-product-image {
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 12px;
    flex-shrink: 0;
}

.tooltip-product-info {
    flex: 1;
}

.tooltip-product-name {
    font-size: 14px;
    font-weight: 600;
    color: #232323;
    line-height: 1.2;
    margin-bottom: 8px;
}

.tooltip-product-price {
    color: #2fb5d2;
    font-weight: 700;
    font-size: 12px;
    margin-bottom: 12px;
}

.tooltip-product-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.tooltip-btn {
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    transition: all 0.2s;
    display: block;
    border: none;
    cursor: pointer;
}

.tooltip-btn-primary {
    background-color: #2fb5d2;
    color: white;
}

.tooltip-btn-primary:hover {
    background-color: #2592a9;
    text-decoration: none;
    color: white;
}

/* Custom tooltip styles */
.custom-tooltip {
    background: white !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    padding: 0 !important;
}

.custom-tooltip .tooltip-product-content {
    padding: 15px;
}

.custom-tooltip .d-flex {
    display: flex !important;
}

.custom-tooltip .align-items-start {
    align-items: flex-start !important;
}




.inspiration-block {
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
    .flex-col {
        flex-direction: column !important;
    }
}
@media (min-width: 768px) {
    .flex-col {
        flex-direction: row !important;
    }
    .flex-row-reverse {
        flex-direction: row-reverse !important;
    }
}
.inspiration-text-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;    
}

</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Use custom tooltips instead of Bootstrap
    initCustomTooltips();
});

// Custom tooltip implementation
function initCustomTooltips() {
    const hotspots = document.querySelectorAll('[data-toggle="tooltip"]');

    hotspots.forEach(function(hotspot) {
        let tooltip = null;
        let hideTimeout = null;

        function showTooltip() {
            if (tooltip) return; // Already showing

            // Clear any pending hide
            if (hideTimeout) {
                clearTimeout(hideTimeout);
                hideTimeout = null;
            }

            // Create tooltip
            tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip fade show';

            // Get and parse the HTML content
            const htmlContent = hotspot.getAttribute('data-content') || hotspot.getAttribute('title');
            tooltip.innerHTML = htmlContent;
            tooltip.style.cssText = `
                position: fixed;
                z-index: 99999;
                background: white;
                color: #333;
                border: 1px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                font-size: 12px;
                max-width: 300px;
                pointer-events: auto;
                opacity: 0;
                transition: opacity 0.3s ease;
                padding: 0;
            `;

            document.body.appendChild(tooltip);

            // Position tooltip intelligently
            const rect = hotspot.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            const margin = 15;

            let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
            let top = rect.top - tooltipRect.height - margin;

            // Adjust if tooltip goes off screen
            if (left < margin) left = margin;
            if (left + tooltipRect.width > window.innerWidth - margin) {
                left = window.innerWidth - tooltipRect.width - margin;
            }
            if (top < margin) {
                top = rect.bottom + margin;
            }

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';

            // Show tooltip
            setTimeout(() => {
                if (tooltip) tooltip.style.opacity = '1';
            }, 10);

            // Add hover listeners to tooltip
            tooltip.addEventListener('mouseenter', function() {
                if (hideTimeout) {
                    clearTimeout(hideTimeout);
                    hideTimeout = null;
                }
            });

            tooltip.addEventListener('mouseleave', function() {
                hideTooltip();
            });
        }

        function hideTooltip() {
            if (hideTimeout) return; // Already hiding

            hideTimeout = setTimeout(() => {
                if (tooltip) {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        if (tooltip && tooltip.parentNode) {
                            tooltip.parentNode.removeChild(tooltip);
                            tooltip = null;
                        }
                    }, 300);
                }
                hideTimeout = null;
            }, 100);
        }

        hotspot.addEventListener('mouseenter', showTooltip);
        hotspot.addEventListener('mouseleave', hideTooltip);
    });
}


</script>

{/if}
