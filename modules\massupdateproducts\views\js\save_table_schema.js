/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function () {
	var $save = $('#page-header-desc-configuration-save');
	var $products_list = $('#products_list').find('.main_body');
	var $header = $('#products_list').find('.main_head');
	var $delete = $('#page-header-desc-configuration-delete');
	var $mask = $('.saving-table-mask');
	var $massupdateproducts_products = $('#massupdateproducts-products');

	$products_list.on('click', '.expand', function () {
		var $scope = $(this);
		var $product = $scope.closest('.product-row');
		var $field_handler = $scope.closest('.field_handler');
		if (!$scope.data('tiny')) {
			$field_handler.find('.expand').data('tiny', true);
			$tiny_get({selector: '.tiny-get-' + $product.attr('p-id') + '-' + $scope.attr('lang')});
		}

		$field_handler.find('.tiny-mass').show();
		$field_handler.find('.collapse').show();
		$scope.hide();
	});

	$header.on('click', '.expand_all', function () {
		var $scope = $(this);
		var $closest = $scope.closest('.field_handler');

		$products_list.find('.product-row').each(function () {
			var $el = $(this);
			if ($el.find('.check_single').prop('checked')) {
				$el.find('.expand[exp="' + $scope.attr('exp') + '"]').trigger('click');
			}
		});

		$scope.hide();
		$closest.find('.collapse_all').show();
	});

	$header.on('click', '.collapse_all', function () {
		var $scope = $(this);
		var $closest = $scope.closest('.field_handler');
		$products_list.find('.collapse[lang="' + $scope.attr('lang') + '"]').trigger('click');
		$scope.hide();
		$closest.find('.expand_all').show();
	});

	$products_list.on('click', '.collapse', function () {
		var $scope = $(this);
		var $field_handler = $scope.closest('.field_handler');
		$field_handler.find('.tiny-mass').hide();
		$field_handler.find('.expand').show();
		$scope.hide();
	});
	var $tiny_get = function (config) {
		if (!config)
			config = {};

		//var editor_selector = 'rte';
		//if (typeof config['editor_selector'] !== 'undefined')
		//var editor_selector = config['editor_selector'];
		if (typeof config['editor_selector'] != 'undefined')
			config['selector'] = '.' + config['editor_selector'];

//    safari,pagebreak,style,table,advimage,advlink,inlinepopups,media,contextmenu,paste,fullscreen,xhtmlxtras,preview
		default_config = {
			selector: "textarea",
			plugins: "colorpicker link image paste pagebreak table contextmenu filemanager table code media autoresize textcolor",
			toolbar1: "code,|,bold,italic,underline,strikethrough,|,alignleft,aligncenter,alignright,alignfull,formatselect,|,blockquote,colorpicker,pasteword,|,bullist,numlist,|,outdent,indent,|,link,unlink,|,cleanup,|,media,image",
			toolbar2: "",
			external_filemanager_path: ad + "/filemanager/",
			filemanager_title: "File manager",
			external_plugins: {"filemanager": ad + "/filemanager/plugin.min.js"},
			language: iso,
			skin: "prestashop",
			statusbar: false,
			relative_urls: false,
			convert_urls: false,
			extended_valid_elements: "em[class|name|id]",
			menu: {
				edit: {title: 'Edit', items: 'undo redo | cut copy paste | selectall'},
				insert: {title: 'Insert', items: 'media image link | pagebreak'},
				view: {title: 'View', items: 'visualaid'},
				format: {title: 'Format', items: 'bold italic underline strikethrough superscript subscript | formats | removeformat'},
				table: {title: 'Table', items: 'inserttable tableprops deletetable | cell row column'},
				tools: {title: 'Tools', items: 'code'}
			},
			setup: function (editor) {
				editor.on('change', function (e) {
					$('#' + e.target.id).val(e.target.getContent());
					console.log('change event', e.target.id, e.target.getContent());
				});
			}

		}

		$.each(default_config, function (index, el)
		{
			if (config[index] === undefined)
				config[index] = el;
		});

		tinyMCE.init(config);
	};
	var load = function ($only_ids) {
		var $dataSend = {};
		$products_list.find('.product-row').each(function () {
			var $tr = $(this);
			var $id = $tr.attr('p-id');
			if ($tr.find('.check_single').prop('checked')) {
				$dataSend[$id] = {};
				$dataSend[$id]['data'] = {};
				$dataSend[$id]['combinations'] = {};
				var $j = 0;
				var $count_send_p = 0;
				if (!$only_ids)
				{
					$tr.find('.to-send').each(function () {
						var $scope = $(this);
						var $send_name = $scope.attr('send-name');
						if ($send_name in $dataSend[$id]['data']) {
							if (!$.isPlainObject($dataSend[$id]['data'][$send_name])) {
								var $tmp = $dataSend[$id]['data'][$send_name];
								$dataSend[$id]['data'][$send_name] = {};
								$dataSend[$id]['data'][$send_name][$j++] = $tmp;
							}
							if ($scope.hasClass('val-element')) {
								$dataSend[$id]['data'][$send_name][$scope.attr('id-e')] = $scope.prop('checked') ? 1 : 0;
							} else {
								$dataSend[$id]['data'][$send_name][$j++] = $scope.is(':checkbox') ? ($scope.hasClass('no-checkbox') ? ($scope.prop('checked') ? $scope.val() : 0) : ($scope.prop('checked') ? 1 : 0)) : $scope.val();
							}
						} else {
							if ($scope.hasClass('val-element')) {
								$dataSend[$id]['data'][$send_name] = {};
								$dataSend[$id]['data'][$send_name][$scope.attr('id-e')] = $scope.prop('checked') ? 1 : 0;
							} else {
								$dataSend[$id]['data'][$send_name] = $scope.is(':checkbox') ? ($scope.hasClass('no-checkbox') ? ($scope.prop('checked') ? $scope.val() : 0) : ($scope.prop('checked') ? 1 : 0)) : $scope.val();
							}
						}
						$count_send_p++;
					});
				} else
					$dataSend[$id]['data'] = true;


				$products_list.find('.combination-row[p-id="' + $id + '"]').each(function () {
					var $tr_com = $(this);
					var $id_com = $tr_com.attr('c-id');
					if ($tr_com.find('.check_single').prop('checked')) {
						$dataSend[$id]['combinations'][$id_com] = {};
						var $j2 = 0;
						var $count_send_c = 0;
						if (!$only_ids)
						{
							$tr_com.find('.to-send').each(function () {
								var $scope = $(this);
								var $send_name = $scope.attr('send-name');
								if ($send_name in $dataSend[$id]['combinations'][$id_com]) {
									if (!$.isPlainObject($dataSend[$id]['combinations'][$id_com][$send_name])) {
										var $tmp = $dataSend[$id]['combinations'][$id_com][$send_name];
										$dataSend[$id]['combinations'][$id_com][$send_name] = {};
										$dataSend[$id]['combinations'][$id_com][$send_name][$j2++] = $tmp;
									}
									$dataSend[$id]['combinations'][$id_com][$send_name][$j2++] = $scope.is(':checkbox') ? ($scope.hasClass('no-checkbox') ? ($scope.prop('checked') ? $scope.val() : 0) : ($scope.prop('checked') ? 1 : 0)) : $scope.val();
								} else {
									$dataSend[$id]['combinations'][$id_com][$send_name] = $scope.is(':checkbox') ? ($scope.hasClass('no-checkbox') ? ($scope.prop('checked') ? $scope.val() : 0) : ($scope.prop('checked') ? 1 : 0)) : $scope.val();
								}

								$count_send_c++;
							});
						} else
							$dataSend[$id]['combinations'][$id_com] = true;
					}

					$tr_com.hide();
				});

				$tr.hide();

				$products_list.find('.product-row-mask[p-id="' + $id + '"]').show();
			}
		});

		return $dataSend;
	};

	var prepare = function ($shift, $elements, $dataSend) {
		var $data = {};
		var $counter = $shift * $elements;
		var $i = 0;
		var $j = 0;
		if ($dataSend)
			$.each($dataSend, function (key, value) {
				if ($counter == $i)
				{
					$data[key] = value;
					$data[key]['use'] = true;
					$j++;

					$('html, body').animate({
						scrollTop: ($products_list.find('.product-row-mask[p-id="' + key + '"]').offset().top - 150)
					}, 1000);

					$products_list.find('.product-row-raport[p-id="' + key + '"]').hide().find('.td-element').html('');
					$products_list.find('.combination-row-raport[p-id="' + key + '"]').hide().find('.td-element').html('');
				} else
					$i++;
				if ($j == $elements)
					return false;
			});

		return $data;
	};

	var save = function ($shift, $elements, $dataSend) {
		if (!$dataSend)
			return;

		var $prepare = prepare($shift, $elements, $dataSend);

		$mask.show();
		$.ajax({
			data: {
				dataSend: $prepare,
				promotion: $('.filter_promotion').val(),
				is_ajax: true,
				save_mass: true
			},
			type: 'POST',
			dataType: 'json',
			success: function ($response) {
				if (!$response.end) {
					$.each($response.raport, function (key, value) {
						$products_list.find('.product-row[p-id="' + key + '"]').show();
						$products_list.find('.combination-row[p-id="' + key + '"]').show();
						$products_list.find('.product-row-mask[p-id="' + key + '"]').hide();
						if (value.error) {
							$products_list.find('.product-row[p-id="' + key + '"] .td-element:first').addClass('save-e').remove('save-s');
							$products_list.find('.product-row-raport[p-id="' + key + '"]').show().find('.td-element').html(value.message);
						} else {
							$products_list.find('.product-row-mask[p-id="' + key + '"]').remove();
							$products_list.find('.product-row-raport[p-id="' + key + '"]').remove();
							$products_list.find('.product-row[p-id="' + key + '"]').replaceWith(value.message);
							$products_list.find('.product-row[p-id="' + key + '"] .td-element:first').addClass('save-s').remove('save-e');
							if (value.combinations) {
                                $.each(value.combinations, function (key2, value2) {
                                    if (value2.error) {
                                        $products_list.find('.combination-row-raport[c-id="' + key2 + '"]').show().find('.td-element').html(value2.message);
                                        $products_list.find('.combination-row[c-id="' + key2 + '"] .td-element:first').addClass('save-e').remove('save-s');
                                    } else {
                                        $products_list.find('.combination-row-raport[c-id="' + key2 + '"]').remove();
                                        $products_list.find('.combination-row[c-id="' + key2 + '"]').replaceWith(value2.message);
                                        $products_list.find('.combination-row[c-id="' + key2 + '"] .td-element:first').addClass('save-s').remove('save-e');
                                    }
                                });
                            }
						}
					});

					save($shift + 1, 1, $dataSend);
				} else
				{
					$.notify(allProductsSave, 'success', {
						autoHideDelay: 2000
					});
					$massupdateproductsProcess = false;
					$mask.hide();
					$products_list.trigger('saved', $response);
				}
			},
			error: function () {
				$massupdateproductsProcess = false;
				$mask.hide();
			}
		});
	};

	var remove = function ($shift, $elements, $dataSend) {
		if (!$dataSend)
			return;

		$mask.show();
		$.ajax({
			data: {
				dataSend: prepare($shift, $elements, $dataSend),
				promotion: $('.filter_promotion').val(),
				is_ajax: true,
				remove_mass: true
			},
			type: 'POST',
			dataType: 'json',
			success: function ($response) {
				if (!$response.end) {
					$.each($response.raport, function (key, value) {
						$products_list.find('.product-row[p-id="' + key + '"]').show();
						$products_list.find('.combination-row[p-id="' + key + '"]').show();
						$products_list.find('.product-row-mask[p-id="' + key + '"]').hide();
						if (value.error)
						{
							$products_list.find('.product-row[p-id="' + key + '"] .td-element:first').addClass('save-s').remove('save-e');
							$products_list.find('.product-row-raport[p-id="' + key + '"]').show().find('.td-element').html(value.message);
						} else
						{
							$products_list.find('.product-row-mask[p-id="' + key + '"]').remove();
							$products_list.find('.product-row-raport[p-id="' + key + '"]').remove();
							//$products_list.find('.product-row[p-id="' + key + '"]').replaceWith(value.message);
							$products_list.find('.product-row[p-id="' + key + '"] .td-element:first').addClass('save-s').remove('save-e');
							$.each(value.combinations, function (key2, value2) {
								if (value2.error)
								{
									$products_list.find('.combination-row[c-id="' + key2 + '"] .td-element:first').addClass('save-e').remove('save-s');
									$products_list.find('.combination-row-raport[c-id="' + key2 + '"]').show().find('.td-element').html(value2.message);
								} else
								{
									$products_list.find('.combination-row[c-id="' + key2 + '"] .td-element:first').addClass('save-s').remove('save-e');
									//$products_list.find('.combination-row[c-id="' + key2 + '"]').replaceWith(value2.message);
								}
							});
						}
					});

					save($shift + 1, 1, $dataSend);
				} else
				{
					$.notify(allProductsSave, 'success', {
						autoHideDelay: 2000
					});
					$massupdateproductsProcess = false;
					$mask.hide();
				}
			},
			error: function () {
				$massupdateproductsProcess = false;
				$mask.hide();
			}
		});
	};

	$save.on('click', function () {
		if (massProcess())
			return false;

		$massupdateproductsProcess = true;
		$products_list.find('.product-row-raport').hide();
		$products_list.find('.combination-row-raport').hide();
		save(0, 1, load(false));
	});

	$delete.on('click', function () {
		if (massProcess())
			return false;

		$massupdateproductsProcess = true;
		$products_list.find('.product-row-raport').hide();
		$products_list.find('.combination-row-raport').hide();
		remove(0, 1, load(true));
	});

	$('.check_multi').on('change', function () {
		$('.check_single').prop('checked', $(this).prop('checked'));
	});

	$products_list.on('click', '.fa-refresh', function () {
		if (massProcess())
			return false;

		$massupdateproductsProcess = true;
		var $scope = $(this);
		var $tr = $scope.closest('.element-row');
		var $index = $tr.attr('p-id');
		var $tmp = $tr.attr('p-id').split('-');
		var $id_product = $tmp[0];
		var $shop = $tmp[1];
		var $id_combination = 0;
		if ($scope.hasClass('ir-com'))
			$id_combination = $tr.attr('c-id');

		$.ajax({
			data: {
				is_ajax: true,
				refresh: true,
				id_product: $id_product,
				id_combination: $id_combination,
				filter: true,
				promotion: $('.filter_promotion').val(),
				shop: $shop
			},
			dataType: 'json',
			type: 'POST',
			success: function ($response) {
				if ($scope.hasClass('ir-com'))
				{
					$products_list.find('.combination-row-raport[c-id="' + $id_combination + '"]').remove();
					$tr.replaceWith($response.result);
				} else
				{
					$products_list.find('.product-row-mask[p-id="' + $index + '"]').remove();
					$products_list.find('.product-row-raport[p-id="' + $index + '"]').remove();
					$products_list.find('.combination-row[p-id="' + $index + '"]').remove();
					$products_list.find('.combination-row-raport[p-id="' + $index + '"]').remove();
					$tr.replaceWith($response.result);
				}

				$products_list.trigger('refresh');
			},
			complete: function () {
				$massupdateproductsProcess = false;
			}
		});
	});

	$products_list.on('click', '.fa-plus', function () {
		var $scope = $(this);
		var $tr = $scope.closest('.element-row');
		var $tmp = $tr.attr('p-id').split('-');
		var $id_product = $tmp[0];
		var $shop = $tmp[1];

		$.ajax({
			data: {
				is_ajax: true,
				get_combinations: true,
				id_product: $id_product,
				shop: $shop
			},
			dataType: 'json',
			type: 'POST',
			success: function ($response) {
				$tr.after($response.result);
				$scope.removeClass('fa-plus').addClass('fa-minus');
				bindfaminus();
				$products_list.trigger('refresh');
			}
		});
	});

	bindfaminus();

	function bindfaminus() {
		$products_list.on('click', '.fa-minus', function () {

			var $scope = $(this);
			var $tr = $scope.closest('.element-row');

			$products_list.find('.combination-row[p-id="' + $tr.attr('p-id') + '"]').remove();
			$scope.addClass('fa-plus').removeClass('fa-minus');
		});
	}

	$header.on('on_scroll', function () {
		if (!$header.data('on_scroll')) {
			$header.data('default_left', $header.offset().left);
			$(window).data('current_left', $(window).scrollLeft());

			$(window).on('scroll', function () {
				var $scope = $(this);
				var $left = $scope.scrollLeft();
				if ($scope.scrollTop() > ($header.offset().top + $header.height()) && !$header.hasClass('fixed')) {
					$header.addClass('fixed').css({left: $header.data('default_left')});
				}

				if ($scope.scrollTop() <= $massupdateproducts_products.offset().top && $header.hasClass('fixed')) {
					$header.removeClass('fixed').css({left: $header.data('default_left')});
				}

				if ($scope.data('current_left') != $left) {
					$header.css({left: ($header.data('default_left') - $left)});
					$scope.data('current_left', $left);
				}
			});
			$header.data('on_scroll', true);
		}
	});
});