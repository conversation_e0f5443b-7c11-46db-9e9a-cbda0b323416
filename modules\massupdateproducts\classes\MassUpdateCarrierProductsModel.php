<?php
/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2020 prestahelp.com
 *  @license   Shareware
 *  @extension Ra<PERSON><PERSON> Przybylski <<EMAIL>>
 */

class MassUpdateCarrierProductsModel extends MassUpdateProductsAbstract
{
    
	public function __construct(&$module, &$object, &$context)
	{
		$this->settings_name = Tools::strtoupper('MassUpdateCarrierProductsModel');
		parent::__construct($module, $object, $context);
		//carriers
		$this->fields['carriers'] = array(
			'display_name' => $this->module->l('Carriers', $this->languages_name),
			'name' => 'carriers',
			'active' => true,
			'type' => self::TYPE_MUL_SEL
		);
        $carriers = Carrier::getCarriers((int)Context::getContext()->language->id, false, false, false, null, Carrier::ALL_CARRIERS);
		$carriers_selected = array();
		if ($carriers) {
            foreach ($carriers as $carrier) {
                $carriers_selected[$carrier['id_reference']] = array(
                    'level_depth' => 0,
                    'display_name' => $carrier['name']
                );
            }
        }
		$this->fields['carriers']['select'] = $carriers_selected;
	}

	public function save(array $data)
	{
		$result = array();
		$end = false;
		if ($data) {
            foreach ($data as $id_product => $params) {
                $result[$id_product] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );
                $product_params = array_key_exists('data', $params) ? $params['data'] : null;
                if (!$product_params) {
                    $result[$id_product]['message'] = $this->module->l('Incorrect schema', $this->languages_name);
                    continue;
                }
                $product = new Product($id_product);
                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }
                if ($product_params) {
                    $carriers = $product_params['carriers'];
                    $carriers_add = array();
                    if ($carriers) {
                        foreach ($carriers as $carrier) {
                            $carriers_add[] = $carrier;
                        }
                    }
                    $errors = $product->validateFields(false, true);
                    if ($errors !== true) {
                        $result[$id_product]['message'] = '<p style="color: #FFF;">' . (is_bool($errors) ?
                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)) . '</p>';
                        continue;
                    } else {
                        $product->setCarriers($carriers_add);
                        if ($product->update()) {
                            $result[$id_product]['error'] = false;
                            $result[$id_product]['message'] = $this->displayProduct($product);
                        } else {
                            $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                            continue;
                        }
                    }
                } else {
                    $result[$id_product]['error'] = false;
                    $result[$id_product]['message'] = $this->displayProduct($product);
                }
            }
        } else {
            $end = true;
        }
		return array(
			'raport' => $result,
			'end' => $end,
            'info' => '<p class="alert alert-warning">Niewybrani przewoźnicy zostaną skasowani!</p>'
		);
	}

	public function display($result)
	{
		$ids_product = $result['result'];
		$result['result'] = '';
		$result['table'] = true;
		if ($ids_product) {
            foreach ($ids_product as $product_arr) {
                $product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['dates']['products_count']--;
                    continue;
                }
                $result['result'] .= $this->displayProduct($product);
            }
        }
		return $result;
	}

	public function displayProduct(&$product)
	{
		parent::displayProduct($product);
		$product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
			$this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
		$product->quantity = $this->object->shop_group->share_stock ? $product->quantity : StockAvailable::getQuantityAvailableByProduct($product->id);
		$product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
		$product->full_name = $product->name[$this->context->language->id];
		$carriers_arr = $product->getCarriers();
		$carriers = array();
		if ($carriers_arr) {
            foreach ($carriers_arr as $carrier) {
                $carriers[] = $carrier['id_reference'];
            }
        }
		$this->fields['carriers']['value'] = $carriers;
		$this->context->smarty->assign(array(
			'product' => $product,
			'fields' => $this->getFields(),
			'languages' => $this->object->languages,
            'iso' => file_exists(_PS_ROOT_DIR_.'/js/tinymce/langs/'.$this->context->language->iso_code.'.js') ? $this->context->language->iso_code : 'en',
            'path_css' => _THEME_CSS_DIR_,
            'ad' => dirname($_SERVER['PHP_SELF'])
		));
		return $this->object->createTemplate('tr-product.tpl')->fetch();
	}

	public function displayCombination(&$product, &$combination)
	{
		if (!parent::displayCombination($product, $combination)) {
            return '';
        }
	}
}
