<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

class WebixaContentBanner extends ObjectModel
{
    const _TYPE_IMG_DIR_ = 'img/banners';
    const _IMG_DIR_ = _PS_MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    const _IMG_PATH_ = _MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    public $image_dir = self::_IMG_DIR_;

    public $id_webixa_content_block;
    public $image = false;
    public $show_on_home = true;
    public $show_to_guest = true;
    public $show_to_logged = true;
    public $has_group_restrictions = false;
    public $has_category_restrictions = false;
    public $position;
    public $active = true;
    public $alt;
    public $link;

    public $category_restrictions = [];
    public $group_restrictions = [];

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'webixa_content_banner',
        'primary' => 'id_webixa_content_banner',
        'multilang' => true,
        'fields' => [
            'id_webixa_content_block' => ['type' => self::TYPE_INT, 'validate' => 'isInt', 'required' => true],
            'show_on_home' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'show_to_guest' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'show_to_logged' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'has_group_restrictions' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'has_category_restrictions' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'position' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => false],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            /* Lang fields */
            'alt' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 128, 'required' => true],
            'link' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isUrl', 'size' => 512],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null, $translator = null)
    {
        if (version_compare(_PS_VERSION_, '1.7.1', '<')) {
            parent::__construct($id, $id_lang, $id_shop);
        } else {
            parent::__construct($id, $id_lang, $id_shop, $translator);
        }
        if (file_exists(self::_IMG_DIR_ . $this->id . '.' . $this->image_format)) {
            $this->image = self::_IMG_PATH_ . $this->id . '.' . $this->image_format;
        }
        if ($this->has_category_restrictions) {
            $this->category_restrictions = $this->getCategoryRestrictions();
        }
        if ($this->has_group_restrictions) {
            $this->group_restrictions = $this->getGroupRestrictions();
        }
    }

    public function add($auto_date = true, $null_values = false)
    {
        $this->position = static::getLastPosition($this->id_webixa_content_block);
        $this->has_category_restrictions = !empty($this->category_restrictions);
        $this->has_group_restrictions = !empty($this->group_restrictions);

        $return = parent::add($auto_date, $null_values);

        $this->saveCategoryRestrictions();
        $this->saveGroupRestrictions();

        return $return;
    }

    public function update($null_values = false)
    {
        $this->has_category_restrictions = !empty($this->category_restrictions);
        $this->has_group_restrictions = !empty($this->group_restrictions);

        $return = parent::update($null_values);

        $this->saveCategoryRestrictions();
        $this->saveGroupRestrictions();

        return $return;
    }

    public function delete()
    {
        if (parent::delete()) {
            return static::cleanPositions($this->id_webixa_content_block);
        }

        return false;
    }

    public function deleteImage($force_delete = false)
    {
        if ($force_delete || !$this->hasMultishopEntries()) {
            foreach (Shop::getShops(false, null, true) as $id_shop) {
                if (
                    file_exists(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                    && !unlink(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                ) {
                    return false;
                }
            }
        }
        return parent::deleteImage($force_delete);
    }

    public function getCategoryRestrictions()
    {
        return array_column(
            Db::getInstance()->executeS('
                SELECT id_category
                FROM `' . _DB_PREFIX_ . self::$definition['table'] . '_category_restriction`
                WHERE ' . self::$definition['primary'] . '=' . (int)$this->id . '
            '),
            'id_category'
        );
    }

    public function saveCategoryRestrictions()
    {
        $return = Db::getInstance()->execute('
            DELETE FROM `' . _DB_PREFIX_ . self::$definition['table'] . '_category_restriction`
            WHERE ' . self::$definition['primary'] . '=' . (int)$this->id . '
        ');

        if (!empty($this->category_restrictions)) {
            $idBanner = $this->id;
            $data = array_map(
                static function ($idCategory) use ($idBanner) {
                    return [
                        'id_webixa_content_banner' => $idBanner,
                        'id_category' => $idCategory,
                    ];
                },
                $this->category_restrictions
            );
            $return &= Db::getInstance()->insert(
                self::$definition['table'] . '_category_restriction',
                $data,
                false,
                false
            );
        }
        return $return;
    }

    public function getGroupRestrictions()
    {
        return array_column(
            Db::getInstance()->executeS('
                SELECT id_group
                FROM `' . _DB_PREFIX_ . self::$definition['table'] . '_group_restriction`
                WHERE ' . self::$definition['primary'] . '=' . (int)$this->id . '
            '),
            'id_group'
        );
    }

    public function saveGroupRestrictions()
    {
        $return = Db::getInstance()->execute('
            DELETE FROM `' . _DB_PREFIX_ . self::$definition['table'] . '_group_restriction`
            WHERE ' . self::$definition['primary'] . '=' . (int)$this->id . '
        ');

        if (!empty($this->group_restrictions)) {
            $idBanner = $this->id;
            $data = array_map(
                static function ($idGroup) use ($idBanner) {
                    return [
                        'id_webixa_content_banner' => (int)$idBanner,
                        'id_group' => (int)$idGroup,
                    ];
                },
                $this->group_restrictions
            );
            $return &= Db::getInstance()->insert(
                self::$definition['table'] . '_group_restriction',
                $data,
                false,
                false
            );
        }
        return $return;
    }

    public static function updatePositions(array $positions)
    {
        // todo: check if this work properly
        $query = 'UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '` SET `position` = CASE `' . static::$definition['primary'] . '` ';

        foreach ($positions as $pos => $args) {
            preg_match('/tr_\d+_(\d+)_\d+/', $args, $matches);
            if (!empty($matches[1])) {
                $query .= 'WHEN ' . $matches[1] . ' THEN ' . $pos . ' ';
            }
        }

        $query .= 'ELSE `position` END WHERE 1';

        return Db::getInstance()->execute($query);
    }

    public static function cleanPositions($blockId)
    {
        return Db::getInstance()->execute('
            UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '` psi1
            JOIN (
                SELECT `' . static::$definition['primary'] . '`, @i := @i+1 new_position
                FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`, (select @i:=-1) temp
                WHERE `' . WebixaContentBlock::$definition['primary'] . '`=' . (int)$blockId . '
                ORDER BY position asc
            ) psi2 ON psi1.`' . static::$definition['primary'] . '` = psi2.`' . static::$definition['primary'] . '`
            SET psi1.position = psi2.new_position
        ');
    }

    public static function getLastPosition($blockId)
    {
        $sql = '
		SELECT MAX(`position`)
		FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`
		WHERE `' . WebixaContentBlock::$definition['primary'] . '`=' . (int)$blockId;
        $value = Db::getInstance()->getValue($sql);

        return null === $value ? 0 : (int) $value + 1;
    }

    public static function getActiveByWebixaContentBlockIdAndContext($idBlock, $context)
    {
        $idShop = $context->shop->id;
        $idLang = $context->language->id;
        $idGroup = (int) Group::getCurrent()->id;
        $isGuest = $context->customer->isGuest();
        $isHome = $context->controller instanceof IndexController;
        $isCategory = $context->controller instanceof CategoryController;

        $banners = [];
        $query = new DbQuery();
        $query->select('a.' . static::$definition['primary']);
        $query->from(static::$definition['table'], 'a');
        $query->where('a.active=' . 1);
        if ($isGuest) {
            $query->where('a.show_to_guest=' . 1);
        } else {
            $query->where('a.show_to_logged=' . 1);

            $query->leftJoin(
                static::$definition['table'] . '_group_restriction',
                'gr',
                'a.' . static::$definition['primary'] . '=' . 'gr.' . static::$definition['primary'] . ' AND gr.id_group=' . (int) $idGroup
            );
            $query->where('a.has_group_restrictions=' . 0 . ' OR gr.id_group IS NOT NULL');
        }

        if ($isHome) {
            $query->where('a.show_on_home=' . 1);
        } elseif ($isCategory) {
            $category = $context->controller->getCategory();

            $query->leftJoin(
                static::$definition['table'] . '_category_restriction',
                'cr',
                'a.' . static::$definition['primary'] . '=' . 'cr.' . static::$definition['primary'] . ' AND cr.id_category=' . (int) $category->id
            );
            $query->where('a.has_category_restrictions=' . 0 . ' OR cr.id_category IS NOT NULL');
        }

        $query->where('a.' . WebixaContentBlock::$definition['primary'] . '=' . (int)$idBlock);

        if (null !== $idShop) {
            $query->innerJoin(
                static::$definition['table'] . '_shop',
                'sa',
                'a.' . static::$definition['primary'] . '=' . 'sa.' . static::$definition['primary'] . ' AND sa.id_shop=' . (int) $idShop
            );
        }
        if (null !== $idLang) {
            $query->innerJoin(
                static::$definition['table'] . '_lang',
                'al',
                'a.' . static::$definition['primary'] . '=' . 'al.' . static::$definition['primary'] . ' AND al.id_lang=' . (int) $idLang
            );
        }

        $query->orderBy('a.position ASC');
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
        foreach ($result as $row) {
            $webixaContentBanner = new WebixaContentBanner($row[static::$definition['primary']], $idLang, $idShop);
            if (Validate::isLoadedObject($webixaContentBanner)) {
                $banners[] = $webixaContentBanner;
            }
        }

        return $banners;
    }
}
