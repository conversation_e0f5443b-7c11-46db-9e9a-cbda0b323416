{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

{include file="./../../media.tpl"}

{*{include file=$phelpTop}*}
<br />

{*{if !$choose_shop}*}

    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css" />
    <link href="http://fonts.googleapis.com/css?family=Open+Sans:300italic,300,400italic,400,600italic,600,700italic,700,800italic,800" rel="stylesheet" type="text/css" />

    <div id="container" class="row">
        <div style="clear: both;display: block;position:relative;width: 100%;overflow: hidden;">
            <div class="slide navigation">
                <div class="slide-content">
                    <div class="slide-content-left" style="background-image: url({$moduleURI|strval}views/img/arrow.png);">
                    </div>
                    <div class="slide-content-right">
                    {l s='Click to expand' mod='massupdateproducts'}
                    </div>
                </div>
                <div class="slide-button" style="background-image: url({$moduleURI|strval}views/img/icon.png);" id="menu-left"></div>
                <script>
                    var $menuLeft = $('#menu-left');
                    $menuLeft.on('click', function() {
                    $('#menu-left-container').toggle(500);
                    });
                </script>
                <div  id="menu-left-container" class="categorieList menu-list">
                    {if count($update_tabs)}
                        {foreach $update_tabs as $tab}
                            {if $tab['name']}
                            <a class="menu-list-element {if (isset($tab['selected']) && $tab['selected'])} active{/if}" href="{$current|escape:'htmlall':'UTF-8'}&amp;token={$token|escape:'htmlall':'UTF-8'}&amp;tab={$tab['id_tab']|strval}">{$tab['name']|strval}</a>
                            {/if}
                        {/foreach}
                    {/if}
                </div>
            </div>
            <div class="backup pull-right">
                <a href="{$link->getAdminLink('AdminMassUpdateProductsBackUp')}" class="btn btn-warning">{l s='Utwórz kopię zapasową' mod='massupdateproducts'}</a>
            </div>
        </div>
        {if isset($filters) && !empty($filters)}
            <br />
            {$filters|strval}

        {/if}
        {if isset($fields_content) && $fields_content}
            {$fields_content|strval}
        {/if}
        {if $show_mass_main_content}
        <div id="massupdateproducts-products" class="ph-panel" style="overflow: inherit;">
            <div class="ph-panel-head">
                <div class="ph-panel-head-main">
                    {l s='Products' mod='massupdateproducts'}&nbsp;<span class="ph-panel-badge">0</span>
                </div>
            </div>
            <div class="ph-panel-content" style="overflow: inherit;">
                {if isset($content)}
                    {$content|strval}
                {/if}
            </div>
        </div>
        {else}
            {if isset($content)}
                {$content|strval}
            {/if}
        {/if}
    </div>
    <script type="text/javascript">
        var show_filter = '{$show_filters}';
    </script>
{*{else}*}
{*{if isset($content)}{$content|strval}{/if}*}
{*{/if}*}

{$banersHtml}
{include file=$phelpBtm}
