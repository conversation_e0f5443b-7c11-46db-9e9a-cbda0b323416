{"name": "webixa-pl/webixa_content", "homepage": "https://github.com/webixa-pl/ps-module-webixa_content", "license": "", "authors": [{"name": "Webixa sp. z o.o."}, {"name": "<PERSON><PERSON><PERSON>", "email": "piotr.jaw<PERSON><PERSON>@webixa.pl"}], "autoload": {"psr-4": {"Webixa\\Content\\": "src/"}, "classmap": ["webixa_content.php", "classes"], "exclude-from-classmap": []}, "config": {"preferred-install": "dist", "classmap-authoritative": true, "optimize-autoloader": true, "prepend-autoloader": false, "autoloader-suffix": "webixa_content"}, "type": "prestashop-module"}