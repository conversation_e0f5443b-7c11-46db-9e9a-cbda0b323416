(function(e,t){function v(e){var t=e.split(/\s+/),n=[];for(var r=0,i;i=t[r];r++){i=i[0].toUpperCase();n.push(i)}return n}function m(t){return t.id&&e('label[for="'+t.id+'"]').val()||t.name}function g(n,r,i){if(!i){i=0}r.each(function(){var r=e(this),s=this,o=this.nodeName.toLowerCase(),u,a;if(o=="label"&&r.find("input, textarea, select").length){u=r.text();r=r.children().first();s=r.get(0);o=s.nodeName.toLowerCase()}switch(o){case"menu":a={name:r.attr("label"),items:{}};i=g(a.items,r.children(),i);break;case"a":case"button":a={name:r.text(),disabled:!!r.attr("disabled"),callback:function(){return function(){r.click()}}()};break;case"menuitem":case"command":switch(r.attr("type")){case t:case"command":case"menuitem":a={name:r.attr("label"),disabled:!!r.attr("disabled"),callback:function(){return function(){r.click()}}()};break;case"checkbox":a={type:"checkbox",disabled:!!r.attr("disabled"),name:r.attr("label"),selected:!!r.attr("checked")};break;case"radio":a={type:"radio",disabled:!!r.attr("disabled"),name:r.attr("label"),radio:r.attr("radiogroup"),value:r.attr("id"),selected:!!r.attr("checked")};break;default:a=t}break;case"hr":a="-------";break;case"input":switch(r.attr("type")){case"text":a={type:"text",name:u||m(s),disabled:!!r.attr("disabled"),value:r.val()};break;case"checkbox":a={type:"checkbox",name:u||m(s),disabled:!!r.attr("disabled"),selected:!!r.attr("checked")};break;case"radio":a={type:"radio",name:u||m(s),disabled:!!r.attr("disabled"),radio:!!r.attr("name"),value:r.val(),selected:!!r.attr("checked")};break;default:a=t;break}break;case"select":a={type:"select",name:u||m(s),disabled:!!r.attr("disabled"),selected:r.val(),options:{}};r.children().each(function(){a.options[this.value]=e(this).text()});break;case"textarea":a={type:"textarea",name:u||m(s),disabled:!!r.attr("disabled"),value:r.val()};break;case"label":break;default:a={type:"html",html:r.clone(true)};break}if(a){i++;n["key"+i]=a}});return i}e.support.htmlMenuitem="HTMLMenuItemElement"in window;e.support.htmlCommand="HTMLCommandElement"in window;e.support.eventSelectstart="onselectstart"in document.documentElement;if(!e.ui||!e.ui.widget){var n=e.cleanData;e.cleanData=function(t){for(var r=0,i;(i=t[r])!=null;r++){try{e(i).triggerHandler("remove")}catch(s){}}n(t)}}var r=null,i=false,s=e(window),o=0,u={},a={},f={},l={selector:null,appendTo:null,trigger:"right",autoHide:false,delay:200,reposition:true,determinePosition:function(t){if(e.ui&&e.ui.position){t.css("display","block").position({my:"center top",at:"center bottom",of:this,offset:"0 5",collision:"fit"}).css("display","none")}else{var n=this.offset();n.top+=this.outerHeight();n.left+=this.outerWidth()/2-t.outerWidth()/2;t.css(n)}},position:function(e,t,n){var r=this,i;if(!t&&!n){e.determinePosition.call(this,e.$menu);return}else if(t==="maintain"&&n==="maintain"){i=e.$menu.position()}else{i={top:n,left:t}}var o=s.scrollTop()+s.height(),u=s.scrollLeft()+s.width(),a=e.$menu.height(),f=e.$menu.width();if(i.top+a>o){i.top-=a}if(i.left+f>u){i.left-=f}e.$menu.css(i)},positionSubmenu:function(t){if(e.ui&&e.ui.position){t.css("display","block").position({my:"left top",at:"right top",of:this,collision:"flipfit fit"}).css("display","")}else{var n={top:0,left:this.outerWidth()};t.css(n)}},zIndex:1,animation:{duration:50,show:"slideDown",hide:"slideUp"},events:{show:e.noop,hide:e.noop},callback:null,items:{}},c={timer:null,pageX:null,pageY:null},h=function(e){var t=0,n=e;while(true){t=Math.max(t,parseInt(n.css("z-index"),10)||0);n=n.parent();if(!n||!n.length||"html body".indexOf(n.prop("nodeName").toLowerCase())>-1){break}}return t},p={abortevent:function(e){e.preventDefault();e.stopImmediatePropagation()},contextmenu:function(t){var n=e(this);t.preventDefault();t.stopImmediatePropagation();if(t.data.trigger!="right"&&t.originalEvent){return}if(n.hasClass("context-menu-active")){return}if(!n.hasClass("context-menu-disabled")){r=n;if(t.data.build){var i=t.data.build(r,t);if(i===false){return}t.data=e.extend(true,{},l,t.data,i||{});if(!t.data.items||e.isEmptyObject(t.data.items)){if(window.console){(console.error||console.log)("No items specified to show in contextMenu")}throw new Error("No Items specified")}t.data.$trigger=r;d.create(t.data)}d.show.call(n,t.data,t.pageX,t.pageY)}},click:function(t){t.preventDefault();t.stopImmediatePropagation();e(this).trigger(e.Event("contextmenu",{data:t.data,pageX:t.pageX,pageY:t.pageY}))},mousedown:function(t){var n=e(this);if(r&&r.length&&!r.is(n)){r.data("contextMenu").$menu.trigger("contextmenu:hide")}if(t.button==2){r=n.data("contextMenuActive",true)}},mouseup:function(t){var n=e(this);if(n.data("contextMenuActive")&&r&&r.length&&r.is(n)&&!n.hasClass("context-menu-disabled")){t.preventDefault();t.stopImmediatePropagation();r=n;n.trigger(e.Event("contextmenu",{data:t.data,pageX:t.pageX,pageY:t.pageY}))}n.removeData("contextMenuActive")},mouseenter:function(t){var n=e(this),i=e(t.relatedTarget),s=e(document);if(i.is(".context-menu-list")||i.closest(".context-menu-list").length){return}if(r&&r.length){return}c.pageX=t.pageX;c.pageY=t.pageY;c.data=t.data;s.on("mousemove.contextMenuShow",p.mousemove);c.timer=setTimeout(function(){c.timer=null;s.off("mousemove.contextMenuShow");r=n;n.trigger(e.Event("contextmenu",{data:c.data,pageX:c.pageX,pageY:c.pageY}))},t.data.delay)},mousemove:function(e){c.pageX=e.pageX;c.pageY=e.pageY},mouseleave:function(t){var n=e(t.relatedTarget);if(n.is(".context-menu-list")||n.closest(".context-menu-list").length){return}try{clearTimeout(c.timer)}catch(t){}c.timer=null},layerClick:function(t){var n=e(this),r=n.data("contextMenuRoot"),i=false,o=t.button,u=t.pageX,a=t.pageY,f,l,c;t.preventDefault();t.stopImmediatePropagation();setTimeout(function(){var n,i,c;var h=r.trigger=="left"&&o===0||r.trigger=="right"&&o===2;if(document.elementFromPoint){r.$layer.hide();f=document.elementFromPoint(u-s.scrollLeft(),a-s.scrollTop());r.$layer.show()}if(r.reposition&&h){if(document.elementFromPoint){if(r.$trigger.is(f)||r.$trigger.has(f).length){r.position.call(r.$trigger,r,u,a);return}}else{l=r.$trigger.offset();n=e(window);l.top+=n.scrollTop();if(l.top<=t.pageY){l.left+=n.scrollLeft();if(l.left<=t.pageX){l.bottom=l.top+r.$trigger.outerHeight();if(l.bottom>=t.pageY){l.right=l.left+r.$trigger.outerWidth();if(l.right>=t.pageX){r.position.call(r.$trigger,r,u,a);return}}}}}}if(f&&h){r.$trigger.one("contextmenu:hidden",function(){e(f).contextMenu({x:u,y:a})})}r.$menu.trigger("contextmenu:hide")},50)},keyStop:function(e,t){if(!t.isInput){e.preventDefault()}e.stopPropagation()},key:function(e){var t=r.data("contextMenu")||{};switch(e.keyCode){case 9:case 38:p.keyStop(e,t);if(t.isInput){if(e.keyCode==9&&e.shiftKey){e.preventDefault();t.$selected&&t.$selected.find("input, textarea, select").blur();t.$menu.trigger("prevcommand");return}else if(e.keyCode==38&&t.$selected.find("input, textarea, select").prop("type")=="checkbox"){e.preventDefault();return}}else if(e.keyCode!=9||e.shiftKey){t.$menu.trigger("prevcommand");return};case 40:p.keyStop(e,t);if(t.isInput){if(e.keyCode==9){e.preventDefault();t.$selected&&t.$selected.find("input, textarea, select").blur();t.$menu.trigger("nextcommand");return}else if(e.keyCode==40&&t.$selected.find("input, textarea, select").prop("type")=="checkbox"){e.preventDefault();return}}else{t.$menu.trigger("nextcommand");return}break;case 37:p.keyStop(e,t);if(t.isInput||!t.$selected||!t.$selected.length){break}if(!t.$selected.parent().hasClass("context-menu-root")){var n=t.$selected.parent().parent();t.$selected.trigger("contextmenu:blur");t.$selected=n;return}break;case 39:p.keyStop(e,t);if(t.isInput||!t.$selected||!t.$selected.length){break}var i=t.$selected.data("contextMenu")||{};if(i.$menu&&t.$selected.hasClass("context-menu-submenu")){t.$selected=null;i.$selected=null;i.$menu.trigger("nextcommand");return}break;case 35:case 36:if(t.$selected&&t.$selected.find("input, textarea, select").length){return}else{(t.$selected&&t.$selected.parent()||t.$menu).children(":not(.disabled, .not-selectable)")[e.keyCode==36?"first":"last"]().trigger("contextmenu:focus");e.preventDefault();return}break;case 13:p.keyStop(e,t);if(t.isInput){if(t.$selected&&!t.$selected.is("textarea, select")){e.preventDefault();return}break}t.$selected&&t.$selected.trigger("mouseup");return;case 32:case 33:case 34:p.keyStop(e,t);return;case 27:p.keyStop(e,t);t.$menu.trigger("contextmenu:hide");return;default:var s=String.fromCharCode(e.keyCode).toUpperCase();if(t.accesskeys[s]){t.accesskeys[s].$node.trigger(t.accesskeys[s].$menu?"contextmenu:focus":"mouseup");return}break}e.stopPropagation();t.$selected&&t.$selected.trigger(e)},prevItem:function(t){t.stopPropagation();var n=e(this).data("contextMenu")||{};if(n.$selected){var r=n.$selected;n=n.$selected.parent().data("contextMenu")||{};n.$selected=r}var i=n.$menu.children(),s=!n.$selected||!n.$selected.prev().length?i.last():n.$selected.prev(),o=s;while(s.hasClass("disabled")||s.hasClass("not-selectable")){if(s.prev().length){s=s.prev()}else{s=i.last()}if(s.is(o)){return}}if(n.$selected){p.itemMouseleave.call(n.$selected.get(0),t)}p.itemMouseenter.call(s.get(0),t);var u=s.find("input, textarea, select");if(u.length){u.focus()}},nextItem:function(t){t.stopPropagation();var n=e(this).data("contextMenu")||{};if(n.$selected){var r=n.$selected;n=n.$selected.parent().data("contextMenu")||{};n.$selected=r}var i=n.$menu.children(),s=!n.$selected||!n.$selected.next().length?i.first():n.$selected.next(),o=s;while(s.hasClass("disabled")||s.hasClass("not-selectable")){if(s.next().length){s=s.next()}else{s=i.first()}if(s.is(o)){return}}if(n.$selected){p.itemMouseleave.call(n.$selected.get(0),t)}p.itemMouseenter.call(s.get(0),t);var u=s.find("input, textarea, select");if(u.length){u.focus()}},focusInput:function(t){var n=e(this).closest(".context-menu-item"),r=n.data(),i=r.contextMenu,s=r.contextMenuRoot;s.$selected=i.$selected=n;s.isInput=i.isInput=true},blurInput:function(t){var n=e(this).closest(".context-menu-item"),r=n.data(),i=r.contextMenu,s=r.contextMenuRoot;s.isInput=i.isInput=false},menuMouseenter:function(t){var n=e(this).data().contextMenuRoot;n.hovering=true},menuMouseleave:function(t){var n=e(this).data().contextMenuRoot;if(n.$layer&&n.$layer.is(t.relatedTarget)){n.hovering=false}},itemMouseenter:function(t){var n=e(this),r=n.data(),i=r.contextMenu,s=r.contextMenuRoot;s.hovering=true;if(t&&s.$layer&&s.$layer.is(t.relatedTarget)){t.preventDefault();t.stopImmediatePropagation()}(i.$menu?i:s).$menu.children(".hover").trigger("contextmenu:blur");if(n.hasClass("disabled")||n.hasClass("not-selectable")){i.$selected=null;return}n.trigger("contextmenu:focus")},itemMouseleave:function(t){var n=e(this),r=n.data(),i=r.contextMenu,s=r.contextMenuRoot;if(s!==i&&s.$layer&&s.$layer.is(t.relatedTarget)){s.$selected&&s.$selected.trigger("contextmenu:blur");t.preventDefault();t.stopImmediatePropagation();s.$selected=i.$selected=i.$node;return}n.trigger("contextmenu:blur")},itemClick:function(t){var n=e(this),r=n.data(),i=r.contextMenu,s=r.contextMenuRoot,o=r.contextMenuKey,u;if(!i.items[o]||n.is(".disabled, .context-menu-submenu, .context-menu-separator, .not-selectable")){return}t.preventDefault();t.stopImmediatePropagation();if(e.isFunction(s.callbacks[o])&&Object.prototype.hasOwnProperty.call(s.callbacks,o)){u=s.callbacks[o]}else if(e.isFunction(s.callback)){u=s.callback}else{return}if(u.call(s.$trigger,o,s)!==false){s.$menu.trigger("contextmenu:hide")}else if(s.$menu.parent().length){d.update.call(s.$trigger,s)}},inputClick:function(e){e.stopImmediatePropagation()},hideMenu:function(t,n){var r=e(this).data("contextMenuRoot");d.hide.call(r.$trigger,r,n&&n.force)},focusItem:function(t){t.stopPropagation();var n=e(this),r=n.data(),i=r.contextMenu,s=r.contextMenuRoot;n.addClass("hover").siblings(".hover").trigger("contextmenu:blur");i.$selected=s.$selected=n;if(i.$node){s.positionSubmenu.call(i.$node,i.$menu)}},blurItem:function(t){t.stopPropagation();var n=e(this),r=n.data(),i=r.contextMenu,s=r.contextMenuRoot;n.removeClass("hover");i.$selected=null}},d={show:function(t,n,i){var s=e(this),o,u={};e("#context-menu-layer").trigger("mousedown");t.$trigger=s;if(t.events.show.call(s,t)===false){r=null;return}d.update.call(s,t);t.position.call(s,t,n,i);if(t.zIndex){u.zIndex=h(s)+t.zIndex}d.layer.call(t.$menu,t,u.zIndex);t.$menu.find("ul").css("zIndex",u.zIndex+1);t.$menu.css(u)[t.animation.show](t.animation.duration,function(){s.trigger("contextmenu:visible")});s.data("contextMenu",t).addClass("context-menu-active");e(document).off("keydown.contextMenu").on("keydown.contextMenu",p.key);if(t.autoHide){e(document).on("mousemove.contextMenuAutoHide",function(e){var n=s.offset();n.right=n.left+s.outerWidth();n.bottom=n.top+s.outerHeight();if(t.$layer&&!t.hovering&&(!(e.pageX>=n.left&&e.pageX<=n.right)||!(e.pageY>=n.top&&e.pageY<=n.bottom))){t.$menu.trigger("contextmenu:hide")}})}},hide:function(n,i){var s=e(this);if(!n){n=s.data("contextMenu")||{}}if(!i&&n.events&&n.events.hide.call(s,n)===false){return}s.removeData("contextMenu").removeClass("context-menu-active");if(n.$layer){setTimeout(function(e){return function(){e.remove()}}(n.$layer),10);try{delete n.$layer}catch(o){n.$layer=null}}r=null;n.$menu.find(".hover").trigger("contextmenu:blur");n.$selected=null;e(document).off(".contextMenuAutoHide").off("keydown.contextMenu");n.$menu&&n.$menu[n.animation.hide](n.animation.duration,function(){if(n.build){n.$menu.remove();e.each(n,function(e,r){switch(e){case"ns":case"selector":case"build":case"trigger":return true;default:n[e]=t;try{delete n[e]}catch(i){}return true}})}setTimeout(function(){s.trigger("contextmenu:hidden")},10)})},create:function(n,r){if(r===t){r=n}n.$menu=e('<ul class="context-menu-list"></ul>').addClass(n.className||"").data({contextMenu:n,contextMenuRoot:r});e.each(["callbacks","commands","inputs"],function(e,t){n[t]={};if(!r[t]){r[t]={}}});r.accesskeys||(r.accesskeys={});e.each(n.items,function(t,i){var s=e('<li class="context-menu-item"></li>').addClass(i.className||""),o=null,u=null;s.on("click",e.noop);i.$node=s.data({contextMenu:n,contextMenuRoot:r,contextMenuKey:t});if(i.accesskey){var a=v(i.accesskey);for(var l=0,c;c=a[l];l++){if(!r.accesskeys[c]){r.accesskeys[c]=i;i._name=i.name.replace(new RegExp("("+c+")","i"),'<span class="context-menu-accesskey">$1</span>');break}}}if(typeof i=="string"){s.addClass("context-menu-separator not-selectable")}else if(i.type&&f[i.type]){f[i.type].call(s,i,n,r);e.each([n,r],function(n,r){r.commands[t]=i;if(e.isFunction(i.callback)){r.callbacks[t]=i.callback}})}else{if(i.type=="html"){s.addClass("context-menu-html not-selectable")}else if(i.type){o=e("<label></label>").appendTo(s);e("<span></span>").html(i._name||i.name).appendTo(o);s.addClass("context-menu-input");n.hasTypes=true;e.each([n,r],function(e,n){n.commands[t]=i;n.inputs[t]=i})}else if(i.items){i.type="sub"}switch(i.type){case"text":u=e('<input type="text" value="1" name="" value="">').attr("name","context-menu-input-"+t).val(i.value||"").appendTo(o);break;case"textarea":u=e('<textarea name=""></textarea>').attr("name","context-menu-input-"+t).val(i.value||"").appendTo(o);if(i.height){u.height(i.height)}break;case"checkbox":u=e('<input type="checkbox" value="1" name="" value="">').attr("name","context-menu-input-"+t).val(i.value||"").prop("checked",!!i.selected).prependTo(o);break;case"radio":u=e('<input type="radio" value="1" name="" value="">').attr("name","context-menu-input-"+i.radio).val(i.value||"").prop("checked",!!i.selected).prependTo(o);break;case"select":u=e('<select name="">').attr("name","context-menu-input-"+t).appendTo(o);if(i.options){e.each(i.options,function(t,n){e("<option></option>").val(t).text(n).appendTo(u)});u.val(i.selected)}break;case"sub":e("<span></span>").html(i._name||i.name).appendTo(s);i.appendTo=i.$node;d.create(i,r);s.data("contextMenu",i).addClass("context-menu-submenu");i.callback=null;break;case"html":e(i.html).appendTo(s);break;default:e.each([n,r],function(n,r){r.commands[t]=i;if(e.isFunction(i.callback)){r.callbacks[t]=i.callback}});e("<span></span>").html(i._name||i.name||"").appendTo(s);break}if(i.type&&i.type!="sub"&&i.type!="html"){u.on("focus",p.focusInput).on("blur",p.blurInput);if(i.events){u.on(i.events,n)}}if(i.icon){s.addClass("icon icon-"+i.icon)}}i.$input=u;i.$label=o;s.appendTo(n.$menu);if(!n.hasTypes&&e.support.eventSelectstart){s.on("selectstart.disableTextSelect",p.abortevent)}});if(!n.$node){n.$menu.css("display","none").addClass("context-menu-root")}n.$menu.appendTo(n.appendTo||document.body)},resize:function(t,n){t.css({position:"absolute",display:"block"});t.data("width",Math.ceil(t.width())+1);t.css({position:"static",minWidth:"0px",maxWidth:"100000px"});t.find("> li > ul").each(function(){d.resize(e(this),true)});if(!n){t.find("ul").andSelf().css({position:"",display:"",minWidth:"",maxWidth:""}).width(function(){return e(this).data("width")})}},update:function(n,r){var i=this;if(r===t){r=n;d.resize(n.$menu)}n.$menu.children().each(function(){var t=e(this),s=t.data("contextMenuKey"),o=n.items[s],u=e.isFunction(o.disabled)&&o.disabled.call(i,s,r)||o.disabled===true;t[u?"addClass":"removeClass"]("disabled");if(o.type){t.find("input, select, textarea").prop("disabled",u);switch(o.type){case"text":case"textarea":o.$input.val(o.value||"");break;case"checkbox":case"radio":o.$input.val(o.value||"").prop("checked",!!o.selected);break;case"select":o.$input.val(o.selected||"");break}}if(o.$menu){d.update.call(i,o,r)}})},layer:function(t,n){var r=t.$layer=e('<div id="context-menu-layer" style="position:fixed; z-index:'+n+'; top:0; left:0; opacity: 0; filter: alpha(opacity=0); background-color: #000;"></div>').css({height:s.height(),width:s.width(),display:"block"}).data("contextMenuRoot",t).insertBefore(this).on("contextmenu",p.abortevent).on("mousedown",p.layerClick);if(!e.support.fixedPosition){r.css({position:"absolute",height:e(document).height()})}return r}};e.fn.contextMenu=function(n){if(n===t){this.first().trigger("contextmenu")}else if(n.x&&n.y){this.first().trigger(e.Event("contextmenu",{pageX:n.x,pageY:n.y}))}else if(n==="hide"){var r=this.data("contextMenu").$menu;r&&r.trigger("contextmenu:hide")}else if(n==="destroy"){e.contextMenu("destroy",{context:this})}else if(e.isPlainObject(n)){n.context=this;e.contextMenu("create",n)}else if(n){this.removeClass("context-menu-disabled")}else if(!n){this.addClass("context-menu-disabled")}return this};e.contextMenu=function(n,r){if(typeof n!="string"){r=n;n="create"}if(typeof r=="string"){r={selector:r}}else if(r===t){r={}}var s=e.extend(true,{},l,r||{});var f=e(document);var c=f;var h=false;if(!s.context||!s.context.length){s.context=document}else{c=e(s.context).first();s.context=c.get(0);h=s.context!==document}switch(n){case"create":if(!s.selector){throw new Error("No selector specified")}if(s.selector.match(/.context-menu-(list|item|input)($|\s)/)){throw new Error('Cannot bind to selector "'+s.selector+'" as it contains a reserved className')}if(!s.build&&(!s.items||e.isEmptyObject(s.items))){throw new Error("No Items specified")}o++;s.ns=".contextMenu"+o;if(!h){u[s.selector]=s.ns}a[s.ns]=s;if(!s.trigger){s.trigger="right"}if(!i){f.on({"contextmenu:hide.contextMenu":p.hideMenu,"prevcommand.contextMenu":p.prevItem,"nextcommand.contextMenu":p.nextItem,"contextmenu.contextMenu":p.abortevent,"mouseenter.contextMenu":p.menuMouseenter,"mouseleave.contextMenu":p.menuMouseleave},".context-menu-list").on("mouseup.contextMenu",".context-menu-input",p.inputClick).on({"mouseup.contextMenu":p.itemClick,"contextmenu:focus.contextMenu":p.focusItem,"contextmenu:blur.contextMenu":p.blurItem,"contextmenu.contextMenu":p.abortevent,"mouseenter.contextMenu":p.itemMouseenter,"mouseleave.contextMenu":p.itemMouseleave},".context-menu-item");i=true}c.on("contextmenu"+s.ns,s.selector,s,p.contextmenu);if(h){c.on("remove"+s.ns,function(){e(this).contextMenu("destroy")})}switch(s.trigger){case"hover":c.on("mouseenter"+s.ns,s.selector,s,p.mouseenter).on("mouseleave"+s.ns,s.selector,s,p.mouseleave);break;case"left":c.on("click"+s.ns,s.selector,s,p.click);break}if(!s.build){d.create(s)}break;case"destroy":var v;if(h){var m=s.context;e.each(a,function(t,n){if(n.context!==m){return true}v=e(".context-menu-list").filter(":visible");if(v.length&&v.data().contextMenuRoot.$trigger.is(e(n.context).find(n.selector))){v.trigger("contextmenu:hide",{force:true})}try{if(a[n.ns].$menu){a[n.ns].$menu.remove()}delete a[n.ns]}catch(r){a[n.ns]=null}e(n.context).off(n.ns);return true})}else if(!s.selector){f.off(".contextMenu .contextMenuAutoHide");e.each(a,function(t,n){e(n.context).off(n.ns)});u={};a={};o=0;i=false;e("#context-menu-layer, .context-menu-list").remove()}else if(u[s.selector]){v=e(".context-menu-list").filter(":visible");if(v.length&&v.data().contextMenuRoot.$trigger.is(s.selector)){v.trigger("contextmenu:hide",{force:true})}try{if(a[u[s.selector]].$menu){a[u[s.selector]].$menu.remove()}delete a[u[s.selector]]}catch(g){a[u[s.selector]]=null}f.off(u[s.selector])}break;case"html5":if(!e.support.htmlCommand&&!e.support.htmlMenuitem||typeof r=="boolean"&&r){e('menu[type="context"]').each(function(){if(this.id){e.contextMenu({selector:"[contextmenu="+this.id+"]",items:e.contextMenu.fromMenu(this)})}}).css("display","none")}break;default:throw new Error('Unknown operation "'+n+'"')}return this};e.contextMenu.setInputValues=function(n,r){if(r===t){r={}}e.each(n.inputs,function(e,t){switch(t.type){case"text":case"textarea":t.value=r[e]||"";break;case"checkbox":t.selected=r[e]?true:false;break;case"radio":t.selected=(r[t.radio]||"")==t.value?true:false;break;case"select":t.selected=r[e]||"";break}})};e.contextMenu.getInputValues=function(n,r){if(r===t){r={}}e.each(n.inputs,function(e,t){switch(t.type){case"text":case"textarea":case"select":r[e]=t.$input.val();break;case"checkbox":r[e]=t.$input.prop("checked");break;case"radio":if(t.$input.prop("checked")){r[t.radio]=t.value}break}});return r};e.contextMenu.fromMenu=function(t){var n=e(t),r={};g(r,n.children());return r};e.contextMenu.defaults=l;e.contextMenu.types=f;e.contextMenu.handle=p;e.contextMenu.op=d;e.contextMenu.menus=a})(jQuery)