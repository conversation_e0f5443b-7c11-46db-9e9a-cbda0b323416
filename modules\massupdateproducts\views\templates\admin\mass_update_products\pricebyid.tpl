<style type="text/css">
    .menu-list {
        position: relative !important;
    }
</style>

<div id="priceByIdPanel" class="panel" style="clear:both;margin-top: 20px;">
    <div class="panel-heading">{l s='Change price by range id product' mod='massupdateproducts'}</div>
    <div class="panel-body">
        <div class="rows">
            <form method="post" id="priceByIdForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-lg-2 text-right">{l s='Start ID product' mod='massupdateproducts'}:</label>
                    <div class="col-md-2">
                        <input id="minId" type="text" class="form-control" name="minID" max="{$max_id}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-lg-2 text-right">{l s='End ID product' mod='massupdateproducts'}:</label>
                    <div class="col-md-2">
                        <input id="maxId" type="text" class="form-control" name="maxID" max="{$max_id}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-lg-2 text-right">{l s='Kind of discount' mod='massupdateproducts'}:</label>
                    <div class="col-lg-4">
                        <select id="kindDiscount" name="kindDiscount" class="form-control">
                            <option value="0">{l s='Choose' mod='massupdateproducts'}</option>
                            <option value="1">{l s='Amount' mod='massupdateproducts'}</option>
                            <option value="2">{l s='Percentage' mod='massupdateproducts'}</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-lg-2 text-right">{l s='Discount action type' mod='massupdateproducts'}:</label>
                    <div class="col-lg-4">
                        <select id="typeDiscount" name="typeDiscount" class="form-control">
                            <option value="0">{l s='Choose' mod='massupdateproducts'}</option>
                            <option value="1">{l s='Reduction' mod='massupdateproducts'}</option>
                            <option value="2">{l s='Increase' mod='massupdateproducts'}</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-lg-2 text-right">{l s='What price is to be changed' mod='massupdateproducts'}:</label>
                    <div class="col-lg-4">
                        <select id="typePrice" name="typePrice" class="form-control">
                            <option value="0">{l s='Choose' mod='massupdateproducts'}</option>
                            <option value="1">{l s='Price tax excl.' mod='massupdateproducts'}</option>
                            <option value="2">{l s='Price tax incl.' mod='massupdateproducts'}</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-lg-2 text-right">{l s='Amounts' mod='massupdateproducts'}:</label>
                    <div class="col-md-2">
                        <input id="amount" type="text" class="form-control" name="amount" value="" />
                    </div>
                </div>

            </form>
        </div>
    </div>
    <div class="panel-footer">
        <button id="priceChangeByIdSubmit" type="button" class="btn btn-default pull-right"><i class="process-icon-save"></i> {l s='Save' mod='massupdateproducts'}</button>
    </div>
</div>

<div class="priceByIdResult"></div>
<script type="text/javascript">
    var ajaxGetProductInfo = '{$ajaxGetProductInfo}';
    var langLoading = '{l s='Loading' mod='massupdateproducts'}';
    var langToUpdate = '{l s='To update is' mod='massupdateproducts'}';
    var langProducts = '{l s='products' mod='massupdateproducts'}';
    var langUpdateProduct = '{l s='Update product' mod='massupdateproducts'}';
    var langPriceFrom = '{l s='price from' mod='massupdateproducts'}';
    var langTaxExcl = '{l s='tax excl.' mod='massupdateproducts'}';
    var langTo = '{l s='to' mod='massupdateproducts'}';
    var langNoUpdateProduct = '{l s='NO update product' mod='massupdateproducts'}';
</script>