<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

namespace Webixa\Content\Module;

if (interface_exists('\PrestaShop\PrestaShop\Core\Module\WidgetInterface')) {
    interface WidgetInterface extends \PrestaShop\PrestaShop\Core\Module\WidgetInterface
    {
    }
} else {
    interface WidgetInterface
    {
        public function renderWidget($hookName, array $configuration);

        public function getWidgetVariables($hookName, array $configuration);
    }
}
