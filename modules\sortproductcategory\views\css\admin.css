.panel-heading {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.panel-heading-action {
    float: right;
    margin-top: -5px;
}



.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.position-cell {
    text-align: center;
    vertical-align: middle;
}

.new-position {
    width: 60px;
    transition: background-color 0.3s ease;
}

.new-position:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.new-position[data-changed="true"] {
    background-color: #fff3cd !important;
    border-color: #ffeaa7;
}

.new-position[data-saved="true"] {
    background-color: #d4edda !important;
    border-color: #c3e6cb;
}

/* Unified save button styles */
.save-changes-btn {
    padding: 15px 30px;
    font-size: 18px;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.3);
}

.save-changes-btn i {
    font-size: 20px;
    margin-right: 8px;
}

.save-changes-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.4);
}

.save-changes-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

.save-changes-btn.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
    color: white;
}

.save-changes-btn.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    border-color: #ffc107;
    color: #212529;
}

/* Floating button specific styles */
.save-changes-btn-floating {
    padding: 12px 20px;
    font-size: 16px;
}

/* Unified button styles */
.unified-btn {
    height: 40px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    border: 1px solid;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.unified-btn i {
    margin-right: 6px;
    font-size: 14px;
}

.unified-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.unified-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Button variants */
.unified-btn.btn-primary {
    background-color: #007cba;
    border-color: #007cba;
    color: white;
}

.unified-btn.btn-primary:hover {
    background-color: #005a87;
    border-color: #005a87;
}

.unified-btn.btn-warning {
    background-color: #f39c12;
    border-color: #f39c12;
    color: white;
}

.unified-btn.btn-warning:hover {
    background-color: #e67e22;
    border-color: #e67e22;
}

.unified-btn.btn-info {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

.unified-btn.btn-info:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.unified-btn.btn-success {
    background-color: #27ae60;
    border-color: #27ae60;
    color: white;
}

.unified-btn.btn-success:hover {
    background-color: #229954;
    border-color: #229954;
}

.unified-btn.btn-default {
    background-color: #95a5a6;
    border-color: #95a5a6;
    color: white;
}

.unified-btn.btn-default:hover {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
}

/* Small button variant */
.unified-btn.btn-xs {
    height: 30px;
    padding: 4px 8px;
    font-size: 12px;
}

.unified-btn.btn-xs i {
    margin-right: 0;
    font-size: 12px;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

#advanced-search-panel {
    border-top: 2px solid #ddd;
    background-color: #f9f9f9;
}

#advanced-search-panel .panel {
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#advanced-search-panel .panel-heading {
    background-color: #337ab7;
    color: white;
    border-bottom: 1px solid #2e6da4;
}

#advanced-search-panel .panel-heading h4 {
    margin: 0;
    font-size: 16px;
}

/* Ensure form controls are properly displayed */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    height: 20px; /* Fixed height for consistent alignment */
}

.form-group .form-control {
    display: block;
    width: 100%;
}

.form-control[style*="width"] {
    display: block !important;
}

/* Align all form elements to the same baseline */
.row .form-group {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.row .form-group > div {
    margin-top: auto;
}

#changes-counter {
    font-size: 14px;
    padding: 8px 12px;
}

.performance-indicator {
    font-size: 12px;
    margin-top: 5px;
}

.product-checkbox {
    margin: 0;
}

#category-select {
    max-width: 400px;
}

.form-control.input-sm {
    height: 28px;
    padding: 2px 6px;
    font-size: 12px;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-default {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-default:hover {
    background-color: #545b62;
    border-color: #545b62;
    color: white;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #212529;
}

.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.auto-sort-btn {
    margin: 2px;
    padding: 4px 8px;
    font-size: 11px;
}

.label {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.label-success {
    background-color: #28a745;
    color: #fff;
}

.label-danger {
    background-color: #dc3545;
    color: #fff;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.pagination {
    margin: 20px 0;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.pagination li {
    display: list-item;
}

.pagination li a {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #dee2e6;
}

.pagination li a:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination li.active a {
    z-index: 3;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.help-block {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    display: block;
}

#advanced-search-panel {
    margin-top: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#advanced-search-panel .panel-heading {
    background-color: #f5f5f5;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
}

#advanced-search-panel .panel-body {
    padding: 15px;
}

#success-message {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .panel-heading-action {
        float: none;
        margin-top: 10px;
    }
    
    .table-responsive {
        border: none;
    }
    
    .new-position {
        width: 60px;
    }
    
    .btn-xs {
        padding: 1px 5px;
        font-size: 10px;
    }
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    border-top-color: #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

input[type="checkbox"] {
    margin: 0 4px 0 0;
    vertical-align: middle;
}

.icon-arrow-left::before { content: "←"; }
.icon-arrow-right::before { content: "→"; }
.icon-search::before { content: "🔍"; }
.icon-refresh::before { content: "↻"; }
.icon-check::before { content: "✓"; }
.icon-save::before { content: "💾"; }
.icon-move::before { content: "↕"; }
.icon-filter::before { content: "🔽"; }
.icon-folder-open::before { content: "📁"; }
.icon-list::before { content: "📋"; }
.icon-cogs::before { content: "⚙"; }

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.drag-handle:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

@media print {
    .btn, .pagination, .panel-heading-action {
        display: none !important;
    }
    
    .table {
        border-collapse: collapse;
    }
    
    .table th, .table td {
        border: 1px solid #000;
        padding: 5px;
    }
}
