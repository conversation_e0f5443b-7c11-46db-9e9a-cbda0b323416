<?php

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/InspirationCategory.php';

class AdminInspirationCategoryController extends ModuleAdminController
{
    public function __construct()
    {
        $this->className = 'InspirationCategory';
        $this->table = 'inspiration_category';
        $this->identifier = 'id_inspiration_category';
        $this->lang = true;
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->module = Module::getInstanceByName('webixa_preview');

        parent::__construct();

        $this->fields_list = array(
            'id_inspiration_category' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'width' => 25
            ),
            'name' => array(
                'title' => $this->l('Name'),
                'width' => 'auto',
                'filter_key' => 'b!name'
            ),
            'id_parent' => array(
                'title' => $this->l('Parent Category'),
                'width' => 'auto',
                'search' => false,
                'orderby' => false,
                'callback' => 'getParentCategoryName'
            ),
            'active' => array(
                'title' => $this->l('Active'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'orderby' => false,
                'width' => 25
            ),
            'position' => array(
                'title' => $this->l('Position'),
                'width' => 70,
                'filter_key' => 'a!position',
                'align' => 'center',
                'position' => 'position'
            )
        );

        $this->_defaultOrderBy = 'position';
        $this->_defaultOrderWay = 'ASC';
        $this->orderBy = 'position';

        $this->bulk_actions = array(
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?')
            ),
            'enableSelection' => array('text' => $this->l('Enable selection')),
            'disableSelection' => array('text' => $this->l('Disable selection'))
        );
    }


    public function getParentCategoryName($id_parent, $row)
    {
        if ($id_parent == 0) {
            return $this->l('Root');
        }
        $category = new InspirationCategory($id_parent, $this->context->language->id);
        if (Validate::isLoadedObject($category)) {
            return $category->name;
        }
        return $this->l('Unknown');
    }


    public function renderForm()
    {
        $id_inspiration_category = (int)Tools::getValue('id_inspiration_category');

        if ($id_inspiration_category) {
            $category = new InspirationCategory($id_inspiration_category, $this->context->language->id);
            if (Validate::isLoadedObject($category)) {
            } else {
            }
        }

        $this->fields_form = array(
            'legend' => array(
                'title' => $this->l('Category Information'),
                'icon' => 'icon-list-alt'
            ),
            'input' => array(
                array(
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'lang' => true,
                    'required' => true,
                    'hint' => $this->l('Invalid characters:').' <>;=#{}',
                ),
                array(
                    'type' => 'select',
                    'label' => $this->l('Parent Category'),
                    'name' => 'id_parent',
                    'options' => array(
                        'query' => $this->getParentCategoriesOptions(),
                        'id' => 'id_inspiration_category',
                        'name' => 'name'
                    ),
                    'hint' => $this->l('Select parent category')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'class' => 't',
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        )
                    ),
                ),
            ),
            'submit' => array(
                'title' => $this->l('Save'),
            ),
            'buttons' => array(
                array(
                    'href' => $this->context->link->getAdminLink('AdminInspiration'),
                    'title' => $this->l('Back to list'),
                    'icon' => 'process-icon-back'
                )
            )
        );


        if (!Tools::getValue('id_inspiration_category')) {
            $this->fields_form['input'][] = array(
                'type' => 'hidden',
                'name' => 'position'
            );
        }

        return parent::renderForm();
    }


    protected function getParentCategoriesOptions()
    {
        $id_lang = $this->context->language->id;
        $categories = InspirationCategory::getActiveCategories($id_lang);
        $options = array(
            array(
                'id_inspiration_category' => 0,
                'name' => $this->l('Root')
            )
        );
        foreach ($categories as $category) {
            $options[] = array(
                'id_inspiration_category' => $category['id_inspiration_category'],
                'name' => $category['name']
            );
        }
        return $options;
    }


     public function initPageHeader()
     {
         parent::initPageHeader();

         unset($this->page_header_toolbar_btn['new']);
     }


    public function ajaxProcessUpdatePositions()
    {
        if ($this->tabAccess['edit'] === '1') {
            $id_inspiration_category = (int)Tools::getValue('id');
            $id_parent = (int)Tools::getValue('id_parent');
            $way = (int)Tools::getValue('way');
            $positions = Tools::getValue($this->table);

            if (is_array($positions)) {
                foreach ($positions as $key => $value) {
                    $pos = explode('_', $value);
                    $inspiration_categories[(int)$pos[1]] = $key;
                }
            }

            $category = new InspirationCategory($id_inspiration_category);
            if (Validate::isLoadedObject($category)) {
                if (isset($inspiration_categories) && is_array($inspiration_categories)) {
                    foreach ($inspiration_categories as $key => $value) {
                        $category = new InspirationCategory($key);
                        $category->position = (int)$value;
                        if (!Validate::isLoadedObject($category) || !$category->updatePosition($way, $id_parent)) {
                            die(json_encode(array('hasError' => true, 'errors' => $this->displayError('Can not update positions'))));
                        }
                    }
                    $this->reOrderPositions($id_parent);
                    die(json_encode(array('result' => true)));
                }
            } else {
                die(json_encode(array('hasError' => true, 'errors' => $this->displayError('This category can not be loaded'))));
            }
        }
    }


    public function reOrderPositions($id_parent)
    {
        $id_parent = (int)$id_parent;
        $max = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue('
            SELECT MAX(ic.position)
            FROM `'._DB_PREFIX_.'inspiration_category` ic
            WHERE ic.id_parent = '.$id_parent
        );

        if ($max === false) {
            return true;
        }

        $sql = 'SELECT ic.position, ic.id_inspiration_category
                FROM `'._DB_PREFIX_.'inspiration_category` ic
                WHERE ic.id_parent = '.$id_parent.'
                ORDER BY ic.position ASC';

        if (!($res = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql))) {
            return false;
        }

        $i = 0;
        foreach ($res as $category) {
            $sql = 'UPDATE `'._DB_PREFIX_.'inspiration_category`
                    SET position = '.(int)$i++.'
                    WHERE id_inspiration_category = '.(int)$category['id_inspiration_category'];
            Db::getInstance()->execute($sql);
        }

        return true;
    }


    public function updatePosition($way, $position)
    {
        if (!Validate::isInt($way) || !Validate::isInt($position)) {
            die(false);
        }

        $sql = '
            SELECT ic.`id_inspiration_category`, ic.`position`, ic.`id_parent`
            FROM `'._DB_PREFIX_.'inspiration_category` ic
            WHERE ic.`id_parent` = '.(int)Tools::getValue('id_parent', 1).'
            ORDER BY ic.`position` ASC';
        if (!($res = Db::getInstance()->executeS($sql))) {
            return false;
        }

        foreach ($res as $category) {
            $categories[(int)$category['id_inspiration_category']] = (int)$category['position'];
        }

        if (!isset($categories[(int)Tools::getValue('id_inspiration_category')])) {
            return false;
        }

        $id_inspiration_category_source = (int)Tools::getValue('id_inspiration_category');
        $id_inspiration_category_target = 0;

        $max = max($categories);

        if ($way == 0) {
            if ($position <= 0) {
                return false;
            }

            foreach ($categories as $key => $value) {
                if ($value == $position - 1 && $position - 1 >= 0) {
                    $id_inspiration_category_target = $key;
                    break;
                }
            }
        } else {
            if ($position >= $max) {
                return false;
            }

            foreach ($categories as $key => $value) {
                if ($value == $position + 1) {
                    $id_inspiration_category_target = $key;
                    break;
                }
            }
        }

        if (!$id_inspiration_category_target) {
            return false;
        }

        $sql = 'UPDATE `'._DB_PREFIX_.'inspiration_category`
                SET position = '.(int)$categories[$id_inspiration_category_target].'
                WHERE id_inspiration_category = '.(int)$id_inspiration_category_source;
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        $sql = 'UPDATE `'._DB_PREFIX_.'inspiration_category`
                SET position = '.(int)$position.'
                WHERE id_inspiration_category = '.(int)$id_inspiration_category_target;
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        return true;
    }


    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        parent::getList($id_lang, $order_by, $order_way, $start, $limit, $id_lang_shop);
    }

    public function processSave()
    {

        $errors = [];


        $defaultLangId = (int)Configuration::get('PS_LANG_DEFAULT');
        if (empty(Tools::getValue('name_' . $defaultLangId))) {
            $errors[] = $this->l('Name is required for default language');
        }


        if (!empty($errors)) {
            foreach ($errors as $error) {
                $this->errors[] = $error;
            }
            return false;
        }


        foreach (Language::getIDs(false) as $id_lang) {
            if (empty(Tools::getValue('link_rewrite_'.$id_lang))) {
                $_POST['link_rewrite_'.$id_lang] = Tools::link_rewrite(Tools::getValue('name_'.$id_lang));
            }
        }


        $_POST['id_shop'] = Context::getContext()->shop->id;


        if (!Tools::getValue('id_inspiration_category')) {
            $id_parent = (int)Tools::getValue('id_parent', 0);
            $maxPosition = Db::getInstance()->getValue('
                SELECT MAX(position)
                FROM `'._DB_PREFIX_.'inspiration_category`
                WHERE id_parent = '.(int)$id_parent
            );
            $_POST['position'] = $maxPosition !== false ? (int)$maxPosition + 1 : 0;
        }


        if ($id_inspiration_category = (int)Tools::getValue('id_inspiration_category')) {

            $category = new InspirationCategory($id_inspiration_category);
            if (Validate::isLoadedObject($category)) {

                $category->id_parent = (int)Tools::getValue('id_parent');
                $category->active = (int)Tools::getValue('active');
                $category->position = (int)Tools::getValue('position');
                $category->date_upd = date('Y-m-d H:i:s');
                $category->id_shop = (int)Context::getContext()->shop->id;


                foreach (Language::getIDs(false) as $id_lang) {
                    $category->name[$id_lang] = Tools::getValue('name_' . $id_lang);
                    $category->link_rewrite[$id_lang] = Tools::getValue('link_rewrite_' . $id_lang);
                }


                Db::getInstance()->delete('inspiration_to_category', 'id_inspiration_category = ' . (int)$id_inspiration_category);


                $return = $category->update();



                return $return;
            } else {
                $this->errors[] = $this->l('Cannot load category for editing');
                return false;
            }
        } else {

            $return = parent::processSave();



            return $return;
        }
    }
}
