{if isset($homepage_inspirations) && $homepage_inspirations}
<div id="homepage-inspirations" class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-8 text-center">{l s='Our Inspirations' d='Modules.Webixapreview.Shop'}</h2>

    {foreach from=$homepage_inspirations item=inspiration name=inspirations}
        {assign var="isEven" value=($smarty.foreach.inspirations.iteration|intval) % 2 == 0}

        <div class="inspiration-block mb-12 rounded-lg overflow-hidden shadow-lg"
             style="background-color: {$inspiration.rgb_color|escape:'htmlall':'UTF-8'};">

            <div class="flex flex-col md:flex-row {if $isEven}flex-row-reverse{/if}">
                <div class="w-full md:w-1/2 p-6 justify-center inspiration-text-content" style="flex: 0 0 50%;" data-bg-color="{$inspiration.rgb_color|escape:'htmlall':'UTF-8'}">
                    <h3 class="text-xl font-bold mb-3 inspiration-title">{$inspiration.title|escape:'htmlall':'UTF-8'}</h3>
                    <div class="prose mb-4 inspiration-description">
                        {if isset($inspiration.short_description) && $inspiration.short_description}
                            {$inspiration.short_description|strip_tags}
                        {else}
                            {$inspiration.description|truncate:200:'...'|strip_tags}
                        {/if}
                    </div>
                    <a href="{$inspiration.url}" class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition duration-200 self-start inspiration-button">
                        {l s='Discover more' d='Modules.Webixapreview.Shop'}
                    </a>
                </div>                
                <div class="w-full md:w-1/2 relative" style="flex: 0 0 50%;">
                    {* Display only main image on homepage - no slider *}
                    {if isset($inspiration.images) && $inspiration.images|count > 0}
                        {* Find main image *}
                        {assign var="mainImage" value=null}
                        {foreach from=$inspiration.images item=image}
                            {if $image.main}
                                {assign var="mainImage" value=$image}
                                {break}
                            {/if}
                        {/foreach}

                        {* If no main image found, use first image *}
                        {if !$mainImage}
                            {assign var="mainImage" value=$inspiration.images[0]}
                        {/if}

                        {if $mainImage}
                            <div class="relative inspiration-image-container">
                                <img src="{if isset($mainImage.url)}{$mainImage.url}{else}{$inspiration_img_dir_uri}{$mainImage.image}{/if}"
                                     alt="{$inspiration.title|escape:'htmlall':'UTF-8'}"
                                     class="w-full h-auto">


                                {* Hotspots for Products on main image *}
                                {if isset($mainImage.products) && $mainImage.products}
                                    {foreach from=$mainImage.products item=product}
                                        {if isset($product.position_x) && isset($product.position_y)}
                                            {assign var="posX" value=$product.position_x}
                                            {assign var="posY" value=$product.position_y}
                                        {else}
                                            {continue}
                                        {/if}

                                        <div class="inspiration-hotspot"
                                             style="left: {$posX}%; top: {$posY}%;"
                                             data-id-product="{$product.id_product}"
                                             data-toggle="tooltip"
                                             data-placement="auto"
                                             data-html="true"
                                             data-animation="true"
                                             data-trigger="manual"
                                             data-container="body"
                                             data-content='<div class="tooltip-product-content">
                                               <div class="d-flex align-items-start">
                                                 <img src="{$product.image_url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="tooltip-product-image">
                                                 <div class="tooltip-product-info">
                                                   <h5 class="tooltip-product-name">{$product.name|escape:'htmlall':'UTF-8'}</h5>
                                                   <div class="tooltip-product-price">{$product.price}</div>
                                                   <div class="tooltip-product-actions">
                                                     <form method="post" action="{url entity='cart' params=['add' => 1, 'id_product' => $product.id_product, 'token' => $static_token]}" style="margin: 0;">
                                                       <input type="hidden" name="id_product" value="{$product.id_product}">
                                                       <input type="hidden" name="qty" value="1">
                                                       <button class="tooltip-btn tooltip-btn-primary" data-button-action="add-to-cart" type="submit">
                                                         {l s='Add to cart' d='Shop.Theme.Actions'}
                                                       </button>
                                                     </form>
                                                   </div>
                                                 </div>
                                               </div>
                                             </div>'>
                                            <div class="hotspot-cross">
                                                <span>+</span>
                                            </div>
                                        </div>
                                    {/foreach}
                                {/if}
                            </div>
                        {/if}
                    {elseif $inspiration.image_url}
                        <div class="relative inspiration-image-container">
                            <img src="{$inspiration.image_url}" alt="{$inspiration.title|escape:'htmlall':'UTF-8'}" class="w-full h-auto">

                            {if isset($inspiration.products) && $inspiration.products}
                                {foreach from=$inspiration.products item=product}
                                    {if isset($product.position_x) && isset($product.position_y)}
                                        {assign var="posX" value=$product.position_x}
                                        {assign var="posY" value=$product.position_y}
                                    {else}
                                        {continue}
                                    {/if}

                                    <div class="inspiration-hotspot"
                                         style="left: {$posX}%; top: {$posY}%;"
                                         data-id-product="{$product.id_product}"
                                         data-toggle="tooltip"
                                         data-placement="auto"
                                         data-html="true"
                                         data-animation="true"
                                         data-trigger="manual"
                                         data-container="body"
                                         data-content='<div class="tooltip-product-content">
                                           <div class="d-flex align-items-start">
                                             <img src="{$product.image_url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="tooltip-product-image">
                                             <div class="tooltip-product-info">
                                               <h5 class="tooltip-product-name">{$product.name|escape:'htmlall':'UTF-8'}</h5>
                                               <div class="tooltip-product-price">{$product.price}</div>
                                               <div class="tooltip-product-actions">
                                                 <form method="post" action="{url entity='cart' params=['add' => 1, 'id_product' => $product.id_product, 'token' => $static_token]}" style="margin: 0;">
                                                   <input type="hidden" name="id_product" value="{$product.id_product}">
                                                   <input type="hidden" name="qty" value="1">
                                                   <button class="tooltip-btn tooltip-btn-primary" data-button-action="add-to-cart" type="submit">
                                                     {l s='Add to cart' d='Shop.Theme.Actions'}
                                                   </button>
                                                 </form>
                                               </div>
                                             </div>
                                           </div>
                                         </div>'>
                                        <div class="hotspot-cross">
                                            <span>+</span>
                                        </div>
                                    </div>
                                {/foreach}
                            {/if}
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    {/foreach}
</div>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {    
    function adjustTextColors() {
        var containers = document.querySelectorAll('.inspiration-text-content');

        for (var i = 0; i < containers.length; i++) {
            var container = containers[i];
            var bgColor = container.getAttribute('data-bg-color') || '';

            // Simple check if background is dark (this is a simplified approach)
            var isDark = false;

            // Check if it starts with # and convert to lowercase
            if (bgColor.charAt(0) === '#') {
                bgColor = bgColor.toLowerCase();

                // Very simple check - if first char after # is 0-7, it's likely dark
                if ('01234567'.indexOf(bgColor.charAt(1)) > -1) {
                    isDark = true;
                }
            }
            // Check if it's rgb format with low values
            else if (bgColor.indexOf('rgb') === 0) {
                // If it contains "rgb(0," or "rgb(1," etc, it's likely dark
                if (bgColor.indexOf('rgb(0,') > -1 ||
                    bgColor.indexOf('rgb(1,') > -1 ||
                    bgColor.indexOf('rgb(2,') > -1 ||
                    bgColor.indexOf('rgb(3,') > -1) {
                    isDark = true;
                }
            }

            // Apply colors based on darkness
            if (isDark) {
                container.style.color = '#ffffff';
                var title = container.querySelector('.inspiration-title');
                var desc = container.querySelector('.inspiration-description');
                if (title) title.style.color = '#ffffff';
                if (desc) desc.style.color = '#f0f0f0';
            } else {
                container.style.color = '#333333';
                var title = container.querySelector('.inspiration-title');
                var desc = container.querySelector('.inspiration-description');
                if (title) title.style.color = '#333333';
                if (desc) desc.style.color = '#555555';
            }
        }
    }

    // Call the function
    adjustTextColors();

    // No slider on homepage - only main images with hotspots

    // Initialize tooltips for hotspots
    initCustomTooltips();

    if (typeof inspirationModule !== 'undefined') {
        inspirationModule.init();
    }

    // No lazy loading needed - only main images are displayed
});
</script>

<style>
/* No slider styles needed on homepage */

.inspiration-image-container {
    position: relative;
}

.inspiration-hotspot {
    position: absolute;
    transform: translate(-50%, -50%);
    z-index: 100;
    cursor: pointer;
    width: 30px;
    height: 30px;
    pointer-events: auto;
}

.hotspot-cross {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    border: 3px solid #2fb5d2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    color: #2fb5d2;
    transition: all 0.3s ease;
    z-index: 500;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(47, 181, 210, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(47, 181, 210, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(47, 181, 210, 0);
    }
}

.inspiration-hotspot:hover .hotspot-cross {
    transform: scale(1.2) !important;
    background-color: #2fb5d2 !important;
    color: white !important;
    animation: none;
}

/* Custom tooltip styles */
.custom-tooltip {
    background: white !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    padding: 0 !important;
}

.custom-tooltip .tooltip-product-content {
    padding: 15px;
}

.custom-tooltip .d-flex {
    display: flex !important;
}

.custom-tooltip .align-items-start {
    align-items: flex-start !important;
}

/* Bootstrap Tooltip Styles */
.tooltip {
    z-index: 99999 !important;
}

.tooltip .tooltip-inner {
    background-color: white !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    padding: 0 !important;
    max-width: 300px !important;
}

.tooltip .tooltip-arrow {
    border-top-color: #ddd !important;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow {
    border-bottom-color: #ddd !important;
}

.tooltip.bs-tooltip-left .tooltip-arrow {
    border-left-color: #ddd !important;
}

.tooltip.bs-tooltip-right .tooltip-arrow {
    border-right-color: #ddd !important;
}

.tooltip-product-content {
    padding: 15px;
    max-width: 300px;
    text-align: left;
}

.tooltip-product-image {
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 12px;
    flex-shrink: 0;
}

.tooltip-product-info {
    flex: 1;
}

.tooltip-product-name {
    font-size: 14px;
    font-weight: 600;
    color: #232323;
    line-height: 1.2;
    margin-bottom: 8px;
}

.tooltip-product-price {
    color: #2fb5d2;
    font-weight: 700;
    font-size: 12px;
    margin-bottom: 12px;
}

.tooltip-product-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.tooltip-btn {
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    transition: all 0.2s;
    display: block;
    border: none;
    cursor: pointer;
}

.tooltip-btn-primary {
    background-color: #2fb5d2;
    color: white;
}

.tooltip-btn-primary:hover {
    background-color: #2592a9;
    text-decoration: none;
    color: white;
}

/* Custom tooltip styles */
.custom-tooltip {
    background: white !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    padding: 0 !important;
}

.custom-tooltip .tooltip-product-content {
    padding: 15px;
}

.custom-tooltip .d-flex {
    display: flex !important;
}

.custom-tooltip .align-items-start {
    align-items: flex-start !important;
}




.inspiration-block {
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
    .flex-col {
        flex-direction: column !important;
    }
}
@media (min-width: 768px) {
    .flex-col {
        flex-direction: row !important;
    }
    .flex-row-reverse {
        flex-direction: row-reverse !important;
    }
}
.inspiration-text-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;    
}

</style>

<script>
// Custom tooltip implementation moved to main script above

// Custom tooltip implementation
function initCustomTooltips() {
    const hotspots = document.querySelectorAll('[data-toggle="tooltip"]');

    hotspots.forEach(function(hotspot) {
        let tooltip = null;
        let hideTimeout = null;

        function showTooltip() {
            if (tooltip) return; // Already showing

            // Clear any pending hide
            if (hideTimeout) {
                clearTimeout(hideTimeout);
                hideTimeout = null;
            }

            // Create tooltip
            tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip fade show';

            // Get and parse the HTML content
            const htmlContent = hotspot.getAttribute('data-content') || hotspot.getAttribute('title');
            tooltip.innerHTML = htmlContent;
            tooltip.style.cssText = `
                position: fixed;
                z-index: 99999;
                background: white;
                color: #333;
                border: 1px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                font-size: 12px;
                max-width: 300px;
                pointer-events: auto;
                opacity: 0;
                transition: opacity 0.3s ease;
                padding: 0;
            `;

            document.body.appendChild(tooltip);

            // Position tooltip intelligently
            const rect = hotspot.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            const margin = 15;

            let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
            let top = rect.top - tooltipRect.height - margin;

            // Adjust if tooltip goes off screen
            if (left < margin) left = margin;
            if (left + tooltipRect.width > window.innerWidth - margin) {
                left = window.innerWidth - tooltipRect.width - margin;
            }
            if (top < margin) {
                top = rect.bottom + margin;
            }

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';

            // Show tooltip
            setTimeout(() => {
                if (tooltip) tooltip.style.opacity = '1';
            }, 10);

            // Add hover listeners to tooltip
            tooltip.addEventListener('mouseenter', function() {
                if (hideTimeout) {
                    clearTimeout(hideTimeout);
                    hideTimeout = null;
                }
            });

            tooltip.addEventListener('mouseleave', function() {
                hideTooltip();
            });
        }

        function hideTooltip() {
            if (hideTimeout) return; // Already hiding

            hideTimeout = setTimeout(() => {
                if (tooltip) {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        if (tooltip && tooltip.parentNode) {
                            tooltip.parentNode.removeChild(tooltip);
                            tooltip = null;
                        }
                    }, 300);
                }
                hideTimeout = null;
            }, 100);
        }

        hotspot.addEventListener('mouseenter', showTooltip);
        hotspot.addEventListener('mouseleave', hideTooltip);
    });
}


</script>

{/if}
