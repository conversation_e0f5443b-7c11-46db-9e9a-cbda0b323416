{extends file='page.tpl'}

{block name='page_title'}
  <h1 class="text-2xl font-semibold mb-4">{$page_title}</h1>
{/block}

{block name='page_content'}  
  <style>
    .inspiration-item {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    .inspiration-item:hover {
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .inspiration-item img {
      transition: transform 0.5s ease;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .inspiration-item:hover img {
      transform: scale(1.05);
    }
    .inspiration-title {
      color: #fff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      text-align: center;
      padding: 0 15px;
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 15px;
      transform: translateY(20px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    .inspiration-overlay {
      position: absolute;
      inset: 0;
      background-color: rgba(47, 181, 210, 0);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      opacity: 0;
      visibility: hidden;
    }
    .inspiration-item:hover .inspiration-overlay {
      background-color: rgba(47, 181, 210, 0.8);
      opacity: 1;
      visibility: visible;
    }
    .circular-arrow {
      width: 60px;
      height: 60px;
      background-color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: scale(0.5);
      opacity: 0;
      transition: all 0.3s ease 0.1s;
    }
    .circular-arrow svg {
      width: 30px;
      height: 30px;
      fill: #2fb5d2;
    }
    .inspiration-item:hover .circular-arrow {
      transform: scale(1);
      opacity: 1;
    }
    .inspiration-item:hover .inspiration-title {
      transform: translateY(0);
      opacity: 1;
    }

    .inspiration-title-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 10px 15px;
      font-weight: 600;
      transform: translateY(100%);
      transition: transform 0.3s ease;
    }
    .inspiration-item:hover .inspiration-title-overlay {
      transform: translateY(0);
    }
    .flex-row-reverse {
        flex-direction: row-reverse !important;
    }
	.md14 {
		width: 25% !important;
	}
	.md34 {
		width: 75% !important;
	}
	.ml5 {
		margin-left: 5px;
	}
	.category-item ul {
		margin-left: 15px;
	}
  </style>

  <section id="inspirations-list" class="container mx-auto px-4">
    <div class="flex flex-col md:flex-row flex-row-reverse gap-8">

      {if isset($categories) && $categories}
        <aside class="w-full md14 ml5">
          <nav class="inspirations-categories bg-gray-100 p-4 rounded shadow">
            <h3 class="text-lg font-semibold mb-3 border-b pb-2">{l s='Categories' d='Modules.Webixapreview.Shop'}</h3>
            <ul class="space-y-1">
              {*<li>
                <a href="{url entity='module' name='webixa_preview' controller='inspiration'}"
                   class="block px-2 py-1 rounded hover:bg-gray-200 {if !$currentCategory}font-bold text-blue-700{/if}">
                  {l s='All Inspirations' d='Modules.Webixapreview.Shop'}
                </a>
              </li>*}

              {foreach from=$categories item=category}
                {if $category.id_parent == 0}
                  <li class="category-item">
                    <a href="{url entity='module' name='webixa_preview' controller='inspiration' params=['category_rewrite' => $category.link_rewrite]}"
                       class="block px-2 py-1 rounded hover:bg-gray-200 {if isset($currentCategory) && $currentCategory->id == $category.id_inspiration_category}font-bold text-blue-700{/if}">
                      {$category.name}
                    </a>

                    {assign var="hasSubcategories" value=false}
                    {foreach from=$categories item=subcategory}
                      {if $subcategory.id_parent == $category.id_inspiration_category}
                        {assign var="hasSubcategories" value=true}
                        {break}
                      {/if}
                    {/foreach}

                    {if $hasSubcategories}
                      <ul class="pl-4 mt-1 space-y-1 border-l border-gray-300">
                        {foreach from=$categories item=subcategory}
                          {if $subcategory.id_parent == $category.id_inspiration_category}
                            <li>
                              <a href="{url entity='module' name='webixa_preview' controller='inspiration' params=['category_rewrite' => $subcategory.link_rewrite]}"
                                 class="block px-2 py-1 text-sm rounded hover:bg-gray-200 {if isset($currentCategory) && $currentCategory->id == $subcategory.id_inspiration_category}font-bold text-blue-700{/if}">
                                {$subcategory.name}
                              </a>
                            </li>
                          {/if}
                        {/foreach}
                      </ul>
                    {/if}
                  </li>
                {/if}
              {/foreach}
            </ul>
          </nav>
        </aside>
      {/if}
      <div class="{if isset($categories) && $categories}w-full md34{else}w-full{/if}">
        {if isset($inspirations) && $inspirations}
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {foreach from=$inspirations item=inspiration}
              <article class="inspiration-item border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-lg" data-id-inspiration="{$inspiration.id_inspiration}">
                <a href="{$inspiration.url}" class="block relative">
                  {if $inspiration.image_url}
                    <div class="relative h-64">
                      <img src="{$inspiration.image_url}" alt="{$inspiration.title|escape:'htmlall':'UTF-8'}" loading="lazy" class="w-full h-full object-cover">
                    </div>
                  {else}
                    <div class="w-full h-64 bg-gray-200 flex items-center justify-center text-gray-500 italic">
                        <span>{$inspiration.title|escape:'htmlall':'UTF-8'}</span>
                    </div>
                  {/if}

                  {* Title overlay at bottom of image *}
                  <div class="inspiration-title-overlay">
                    <h4 class="text-white font-semibold text-base">
                      {$inspiration.title|escape:'htmlall':'UTF-8'}
                    </h4>
                  </div>

                  {* Hover overlay with circular arrow *}
                  <div class="inspiration-overlay">
                    <h4 class="inspiration-title">
                      {$inspiration.title|escape:'htmlall':'UTF-8'}
                    </h4>
                    <div class="circular-arrow">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                      </svg>
                    </div>
                  </div>
                </a>
              </article>
            {/foreach}
          </div>
          
          <div class="mt-8">
            {include file='module:webixa_preview/views/templates/front/pagination.tpl' pagination=$pagination}
          </div>

        {else}
          <div class="bg-yellow-100 border border-yellow-300 text-yellow-800 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{l s='No inspirations found in this category.' d='Modules.Webixapreview.Shop'}</span>
          </div>
        {/if}
      </div>

    </div>
  </section>
{/block}
