<?php
/**
 * BlockBot Module Override
 * 
 * <AUTHOR> Name
 * @copyright 2025
 * @license   Commercial
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class FrontController extends FrontControllerCore
{
    public function postProcess()
    {
        parent::postProcess();

        // BlockBot logging functionality - only for front-end pages
        if (!$this->ajax && !Tools::isSubmit('ajax')) {
            $this->logVisitForBlockBot();
        }
    }

    private function logVisitForBlockBot()
    {
        // Check if BlockBot module is installed and enabled
        if (!Module::isInstalled('blockbot') || !Configuration::get('BLOCKBOT_ENABLED')) {
            return;
        }

        // Skip admin pages - only log front-end visits
        if (defined('_PS_ADMIN_DIR_') ||
            strpos($_SERVER['REQUEST_URI'], '/admin') !== false ||
            strpos($_SERVER['REQUEST_URI'], 'AdminBlockBot') !== false ||
            isset($_GET['controller']) && strpos($_GET['controller'], 'Admin') === 0 ||
            Context::getContext()->controller instanceof AdminController ||
            (isset($_SERVER['PHP_SELF']) && strpos($_SERVER['PHP_SELF'], '/admin') !== false)) {
            return;
        }

        // Skip AJAX requests and API calls
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            return;
        }

        // Skip common non-page requests
        $skipExtensions = ['.css', '.js', '.jpg', '.jpeg', '.png', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot'];
        $requestUri = $_SERVER['REQUEST_URI'];
        foreach ($skipExtensions as $ext) {
            if (strpos($requestUri, $ext) !== false) {
                return;
            }
        }

        // Get BlockBot module instance to use its methods
        $blockBotModule = Module::getInstanceByName('blockbot');
        if (!$blockBotModule) {
            return;
        }

        $agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

        // Check if empty user agent blocking is enabled
        if ($agent == '' && Configuration::get('BLOCKBOT_BLOCK_EMPTY_AGENT')) {
            echo 'bad request';
            exit;
        }

        $ip = $blockBotModule->getClientIp();
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

        // Log to database
        $blockBotModule->logVisit($ip, $agent, $referer);

        // Auto-block if enabled and thresholds are met
        if (Configuration::get('BLOCKBOT_AUTO_BLOCK')) {
            $blockBotModule->autoBlockSuspiciousTraffic();
        }

        // Check and send email alerts if enabled
        if (Configuration::get('BLOCKBOT_EMAIL_NOTIFICATIONS')) {
            $blockBotModule->checkAndSendAlerts();
        }
    }
}
