/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

var $massupdateproductsProcess = false;

var massProcess = function() {
    if ($massupdateproductsProcess)
	$.notify(busy, '', {
	    autoHideDelay: 2000
	});
    return $massupdateproductsProcess;
};

window.onbeforeunload = function() {
    if (massProcess()) {
	return exitProcess;
    }
}

$(function() {
    if (show_filter ) {
        $('#massupdateproducts-filters').massupdate_filters();
    }
});
