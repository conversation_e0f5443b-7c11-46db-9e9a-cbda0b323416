<div class='mass-table-text-container'>
	<textarea {$attr|strval} class="to-send validation mass-table-text {$class_mass|strval}" style="{if isset($lang)} padding-left: 20px; {/if}" id='{if isset($product)}{$product->id}_{/if}{$name|strval}{if isset($lang)}_{$lang|strval}{/if}' send-name="{$name|strval}{if isset($lang)}_{$lang|strval}{/if}" validation="{if $validate}{$validate|strval}{else}isCleanHtml{/if}">{$value|strval}</textarea>
	<a href="#" class="btn btn-default btn-sm edit" data-target="{if isset($product)}{$product->id}_{/if}{$name|strval}{if isset($lang)}_{$lang|strval}{/if}"><i class="fa fa-pencil"></i></a>
	<a style="display:none" href="#" class="btn btn-success btn-sm save" data-target="{if isset($product)}{$product->id}_{/if}{$name|strval}{if isset($lang)}_{$lang|strval}{/if}"><i class="fa fa-check"></i></a>
</div>
<script>
	$(function () {
		$(".mass-table-text-container .edit").unbind('click').click(function (e) {
			e.preventDefault();
			var self = $(this), target = $(this).data('target');
			self.hide();
			self.parent().find('.save').show();
			tinyMCE.execCommand('mceAddEditor', false, target);
			$("html, body").animate({ 
				scrollTop: $(this).offset().top
			}, 300);
		});
		$(".mass-table-text-container .save").unbind('click').click(function (e) {
			e.preventDefault();
			var self = $(this), target = $(this).data('target');
			self.hide();
			self.parent().find('.edit').show();
			tinyMCE.triggerSave();
			tinyMCE.execCommand('mceRemoveEditor', false, target);
			$(target).trigger('change');			
		});
	});
</script>