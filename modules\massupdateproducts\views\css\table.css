.product-row, .product-row-mask, .product-row-raport, .combination-row, .combination-row-raport
{
    min-height: 105px;
}

.product-row
{
    background-color: #CFD3D6;
}
.product-row.inactive
{
    background-color: #FD9696 !important;
}

.product-row td {
    background-color: transparent !important;
}

.legend {
    width: 15px;
    height: 15px;
    border: 1px solid #000;
}

.footer-mass 
{
    display: none;
    position: relative;
    min-height: 44px;
}

.pagination-link {
    cursor: pointer;
}

.product-row-mask {
    display: none;
}

.product-row-raport {
    display: none;
    background-color: #F00 !important;
}

.product-row-mask .saving
{
    color: #F00;
    font-weight: bold;
}

.product-row-raport td p, .combination-row-raport {
    color: #FFF !important;
}


.combination-row-raport {
    display: none;
    background-color: #E20000 !important;
}

.product-row-raport td {
    background-color: transparent !important;
}

.combination-row-raport td {
    background-color: transparent !important;
}

.saving-table-mask
{
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    background-color: #FFF;
    z-index: 400;
    top: 0;
    opacity: 0.3;
}

.saving-mask
{
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    background-color: #000;
    z-index: 400;
    top: 0;
    opacity: 0.5;
}

.saving-loading
{
    display: none;
    position: absolute;
    width: 100%;
    text-align: center;
    height: 20px;
    z-index: 400;
    top: 40%;
    color: #FFF;
    font-size: 80px;
}

.header-row a.active
{
    color: #000;
}

.image-show-contents
{
    display: block;
    position: relative;
}

.image-show-content
{
    overflow: hidden;
    background-color: #FFF;
}

.image-show-contents.active .image-show-content
{
    height: auto;
    width: 423px;
    position: absolute;
    z-index: 400;
    box-sizing: border-box;
}

.image-show-contents.inactive .image-show-content
{
    height: 127px;
    width: 141px;
}

.image-show-content-block
{
    display: inline-block;
    position: relative;
    width: 141px;
    float: left;
    overflow: hidden;
}

.image-show-content-tools
{
    display: inline-block;
    float: left;
    width: 14px;
    background-color: #FFF;
    text-align: center;
}

.image-show-content-tools .cover-active,
.image-show-content-tools .rem-inactive,
.image-show-content-tools .add-from-main-inactive
{
    color: #000;
}

.image-show-content-tools .rem-inactive,
.image-show-content-tools .add-from-main-inactive
{
    cursor: pointer;
}

.image-show-content-tools .cover-inactive, 
.image-show-content-tools .rem-active,
.image-show-content-tools .add-from-main-active
{
    color: #00aff0 !important;
    cursor: pointer;
}

.image-show-content-picture
{
    display: inline-block;
    float: right;
    width: 127px;
}

.file-upload-block.inactive
{
    height: 125px;
    overflow: hidden;
}

.file-upload-block.active
{
    height: auto;
    min-height: 125px;
}

.header-row-multi td
{
    background-color: #FFA43D !important;
    vertical-align: middle!important;
}

.ph-panel .header-row,
.ph-panel .header-row-multi,
.ph-panel .product-row,
.ph-panel .product-row-mask,
.ph-panel .product-row-raport,
.ph-panel .combination-row,
.ph-panel .combination-row-raport {
    display: block;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    min-height: 104px;
}

.ph-panel .combination-row {
    border-top: 1px solid #00aff0;
    padding-top: 20px;
    padding-bottom: 20px;
}


.ph-panel .header-row {
    background-color: #ECF6FB;
    text-align: center;
}

.ph-panel .product-row {
    background-color: #CFD3D6;
    border-top: 2px solid #00aff0;
    padding-top: 20px;
    padding-bottom: 20px;
}

.ph-panel .header-row-multi {
    background-color: #FFA43D;
    padding: 20px 0px;
}

.ph-panel .product-row-mask,
.ph-panel .product-row-raport,
.ph-panel .combination-row-raport{
    display: none;
}

.ph-panel .first-td-element {
    position: relative;
    width: 25px;
    line-height: 104px;
}

.ph-panel .save-s {
    background-color: #31A50A !important;
}

.ph-panel .save-e {
    background-color: #F00 !important;
}

.ph-panel .td-element-inside {
    display: table-cell;
    vertical-align: middle;
    height: 104px;
}

.ph-panel .main_head.fixed {
    position: fixed;
    z-index: 2000;
    top: 0px;
    padding: 0px;
}

.field_handler {
    position: relative;
    display: block;
    height: 23px;
}

.field_handler_flag {
    position: absolute;
    top: 4px;
    left: 3px;
    z-index: 201;
}

.field_handler_tiny .field_handler_flag {
    left: 3px;
    right: auto;
}

.field_handler_tiny.field_handler {
    height: auto;
    min-height: 23px;
}

.field_handler .expand, .field_handler .collapse, .field_handler .expand_all, .field_handler .collapse_all {
    position: absolute;
    top: 4px;
    right: 40px;
    z-index: 150;
}

.tiny-mass {
    display: none;
}