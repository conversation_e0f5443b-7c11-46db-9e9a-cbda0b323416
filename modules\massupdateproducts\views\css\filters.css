.filters-panel .filters-panel-left, 
.filters-panel .filters-panel-middle,
.filters-panel .filters-panel-right
{
    width: 30%;
    float: left;
}

.filters-panel .filters-panel-middle
{
    margin-left: 5%;
    margin-right: 5%;
}

.filters-panel .filter-categories, 
.filters-panel .filter-manufacturers,
.filters-panel .filter-categories-default
{
    height: 150px;
    overflow: scroll;
    position: relative;
    border: 1px solid #EEE;
}

.filters-panel .filter-date
{
    width: 40%;
}

.filters-panel .filters-panel-middle .input_range
{
    border: 0; 
    color: #f6931f; 
    font-weight: bold;
    background-color: #FFF !important;
    border-radius: 0;
    box-shadow: none;
}

.filter-mask
{
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    background-color: #000;
    z-index: 400;
    top: 0;
    opacity: 0.5;
}

.filter-loading
{
    display: none;
    position: absolute;
    width: 100%;
    text-align: center;
    height: 20px;
    z-index: 400;
    top: 40%;
    color: #FFF;
    font-size: 80px;
}

.filters-panel-element {
    display: block;
    position: relative;
    overflow: hidden;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
    padding: 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
}

.filters-panel-element-head {
    font-size: 16px;
    display: block;
    margin-bottom: 10px;
    border-bottom: 1px solid #D3D8DB;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
}

.sort-link {
    cursor: pointer;
}

.sort-link.active {
    color: #F00;
}