<?php

class InspirationCategory extends ObjectModel
{
    public $id;
    public $id_inspiration_category;
    public $id_parent;
    public $active = 1;
    public $position;
    public $date_add;
    public $date_upd;
    public $id_shop;

    // Lang fields
    public $name;
    public $link_rewrite;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'inspiration_category',
        'primary' => 'id_inspiration_category',
        'multilang' => true,
        'multishop' => true,
        'fields' => array(
            'id_parent' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'),
            'active' => array('type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => true),
            'position' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'),
            'date_add' => array('type' => self::TYPE_DATE, 'validate' => 'isDate', 'copy_post' => false),
            'date_upd' => array('type' => self::TYPE_DATE, 'validate' => 'isDate', 'copy_post' => false),
            'id_shop' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true, 'shop' => true),

            // Lang fields
            'name' => array('type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isCatalogName', 'required' => true, 'size' => 255),
            'link_rewrite' => array('type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isLinkRewrite', 'required' => true, 'size' => 255),
        ),
        'associations' => array(
            'shops' => array('type' => self::HAS_MANY, 'field' => 'id_shop', 'object' => 'Shop')
        ),
    );

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        Shop::addTableAssociation('inspiration_category', array('type' => 'shop'));
        parent::__construct($id, $id_lang, $id_shop);
        if (Shop::isFeatureActive() && !$this->id_shop) {
            $this->id_shop = Context::getContext()->shop->id;
        }

        // Automatically generate link_rewrite if empty
    }

    public function add($auto_date = true, $null_values = false)
    {
        if ($this->position <= 0) {
            $this->position = self::getHighestPosition() + 1;
        }
        if ($auto_date && !$this->date_add) {
            $this->date_add = date('Y-m-d H:i:s');
        }
        if ($auto_date && !$this->date_upd) {
            $this->date_upd = date('Y-m-d H:i:s');
        }

        if (Shop::isFeatureActive() && !$this->id_shop) {
            $this->id_shop = Context::getContext()->shop->id;
        }

        $result = parent::add($auto_date, $null_values);

        $this->link_rewrite = array();
        // Generate link_rewrite after adding
        foreach (Language::getIDs(false) as $id_lang) {
            $this->link_rewrite[$id_lang] = Tools::link_rewrite($this->name[$id_lang]);
        }

        $this->updateLinkRewrite();

        return $result;
    }

    public function update($null_values = false)
    {
        $this->date_upd = date('Y-m-d H:i:s');

        $result = parent::update($null_values);

         // Generate link_rewrite before updating if needed
         foreach (Language::getIDs(false) as $id_lang) {
             if (!isset($this->link_rewrite[$id_lang]) || empty($this->link_rewrite[$id_lang])) {
                 $this->link_rewrite[$id_lang] = Tools::link_rewrite($this->name[$id_lang]);
                 // Add uniqueness check here if necessary
             }
         }

        $this->updateLinkRewrite();

        // Check if the category is already associated with the shop
        $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'inspiration_category_shop`
                WHERE `id_inspiration_category` = ' . (int)$this->id . '
                AND `id_shop` = ' . (int)$this->id_shop;

        if (!Db::getInstance()->getValue($sql)) {
            Db::getInstance()->insert('inspiration_category_shop', array(
                'id_inspiration_category' => (int)$this->id,
                'id_shop' => (int)$this->id_shop
            ));
        }

        return $result;
    }

    protected function updateLinkRewrite()
    {
        // Update link_rewrite for all languages
        foreach (Language::getIDs(false) as $id_lang) {
            $sql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration_category_lang`
                    SET `link_rewrite` = \'' . pSQL($this->link_rewrite[$id_lang]) . '\'
                    WHERE `id_inspiration_category` = ' . (int)$this->id . '
                    AND `id_lang` = ' . (int)$id_lang;
            Db::getInstance()->execute($sql);
        }
    }

    public function delete()
    {
        if (!parent::delete()) {
            return false;
        }
        // Clean positions after delete
        self::cleanPositions($this->id_parent);
        // Delete associations (e.g., inspiration_to_category)
        Db::getInstance()->delete('inspiration_to_category', 'id_inspiration_category = ' . (int)$this->id);
        // Add deletion of children categories if required
        return true;
    }

    /**
     * Gets the highest position for a category within a parent.
     * @param int $id_parent Parent category ID
     * @return int Highest position
     */
    public static function getHighestPosition($id_parent = 0)
    {
        $sql = 'SELECT MAX(`position`)
                FROM `' . _DB_PREFIX_ . 'inspiration_category`
                WHERE `id_parent` = ' . (int)$id_parent;
        $position = Db::getInstance()->getValue($sql);
        return (is_numeric($position)) ? $position : -1;
    }

    /**
     * Reorders positions for categories within a parent.
     * @param int $id_parent Parent category ID
     * @return bool Success
     */
    public static function cleanPositions($id_parent = 0)
    {
        $sql = 'SELECT `id_inspiration_category`
                FROM `' . _DB_PREFIX_ . 'inspiration_category`
                WHERE `id_parent` = ' . (int)$id_parent . '
                ORDER BY `position` ASC';
        $result = Db::getInstance()->executeS($sql);

        if (!$result) {
            return true; // No categories to reorder
        }

        $i = 0;
        foreach ($result as $row) {
            $updateSql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration_category`
                          SET `position` = ' . (int)$i++ . '
                          WHERE `id_inspiration_category` = ' . (int)$row['id_inspiration_category'];
            Db::getInstance()->execute($updateSql);
        }
        return true;
    }

    /**
     * Get category ID by link_rewrite
     * @param string $rewrite
     * @param int $id_lang
     * @return int|false
     */
    public static function getIdByRewrite($rewrite, $id_lang)
    {
        if (empty($rewrite) || !Validate::isLinkRewrite($rewrite)) {
            return false;
        }

        $sql = new DbQuery();
        $sql->select('ic.id_inspiration_category');
        $sql->from('inspiration_category', 'ic');
        $sql->innerJoin('inspiration_category_lang', 'icl', 'ic.id_inspiration_category = icl.id_inspiration_category');
        $sql->where('icl.link_rewrite = \'' . pSQL($rewrite) . '\'');
        $sql->where('icl.id_lang = ' . (int)$id_lang);
        $sql->where('ic.active = 1');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get all active categories for the front office
     * @param int $id_lang
     * @param int $id_parent Optional parent ID for tree structure
     * @return array|false
     */
    public static function getActiveCategories($id_lang, $id_parent = null)
    {
        $sql = new DbQuery();
        $sql->select('ic.*, icl.*');
        $sql->from('inspiration_category', 'ic');
        $sql->leftJoin('inspiration_category_lang', 'icl', 'ic.id_inspiration_category = icl.id_inspiration_category AND icl.id_lang = ' . (int)$id_lang);
        $sql->where('ic.active = 1');

        if ($id_parent !== null) {
            $sql->where('ic.id_parent = ' . (int)$id_parent);
        }

        $sql->orderBy('ic.position ASC');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        return $results;
    }

     /**
      * Get parent category details
      * @param int $id_lang
      * @return array|false
      */
     public function getParentCategory($id_lang)
     {
         if ($this->id_parent <= 0) {
             return false;
         }
         $parent = new InspirationCategory($this->id_parent, $id_lang);
         if (!Validate::isLoadedObject($parent)) {
             return false;
         }
         return (array)$parent; // Or return the object itself
     }
}
