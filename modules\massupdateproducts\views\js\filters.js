/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

(function ($) {
	$.fn.massupdate_filters = function () {
		var $main = $(this);
		var $scope = $main.find('#filters-panel');
		var $button_on = $main.find('.show_checkbox');
		var $button_off = $main.find('.hide_checkbox');
		var $search = $main.find('#filter');
		var $reset = $main.find('#reset');
		var $products = $('#massupdateproducts-products');
		var $products_content = $products.find('.panel-content');
		var $products_list = $('#products_list').find('.main_body');
		var $mask = $('.filter-mask');
		var $loading = $('.filter-loading');
		var $footer_mass = $('.footer-mass');
		var $pager_mass = $('.pager-mass');
		var $products_list_header = $('#products_list').find('.main_head');
		var $product_table_left = $('#massupdateproducts-products').offset().left;
		var $check_all_c_d = $('.filter_category_default_all');
		var $check_all_c = $('.filter_category_all');
		var $check_all_m = $('.filter_manufacturer_all');
		var $massupdateproducts_sort = $('#massupdateproducts-sort');
		var $sort = $massupdateproducts_sort.find('.sort');
		var $sort_type = 0;
		var $sort_by = 'ps.id_product';
		var $pagination_select = $('.pagination-select');
		var $containter_offset = $('#container.row').offset();

		return this.each(function ()
		{
			$button_on.on('click', function () {
				$button_on.addClass('hidden');
				$button_off.removeClass('hidden');
				$scope.removeClass('hidden');
			});

			$button_off.on('click', function () {
				$button_off.addClass('hidden');
				$button_on.removeClass('hidden');
				$scope.addClass('hidden');
			});

			$button_on.trigger('click');

			$main.find("#slider-range-price").slider({
				range: true,
				min: $price_from,
				max: $price_to,
				values: [$price_from, $price_to],
				slide: function (event, ui)
				{
					$main.find(".filter_range_price").val("" + ui.values[ 0 ] + " - " + ui.values[ 1 ]);
				}
			});

			$main.find(".filter_range_price").val("" + $main.find("#slider-range-price").slider("values", 0) +
					" - " + $main.find("#slider-range-price").slider("values", 1));

			$main.find("#slider-range-quantity").slider({
				range: true,
				min: $quantity_from,
				max: $quantity_to,
				values: [$quantity_from, $quantity_to],
				slide: function (event, ui)
				{
					$main.find(".filter_range_quantity").val("" + ui.values[ 0 ] + " - " + ui.values[ 1 ]);
				}
			});

			$main.find(".filter_range_quantity").val("" + $main.find("#slider-range-quantity").slider("values", 0) +
					" - " + $main.find("#slider-range-quantity").slider("values", 1));

			$main.find("#slider-range-weight").slider({
				range: true,
				min: $weight_from,
				max: $weight_to,
				values: [$weight_from, $weight_to],
				slide: function (event, ui)
				{
					$main.find(".filter_range_weight").val("" + ui.values[ 0 ] + " - " + ui.values[ 1 ]);
				}
			});

			$main.find(".filter_range_weight").val("" + $main.find("#slider-range-weight").slider("values", 0) +
					" - " + $main.find("#slider-range-weight").slider("values", 1));

			$main.find(".filter_date_add_from").datepicker({
				defaultDate: "+1w",
				changeMonth: true,
				numberOfMonths: 3,
				onClose: function (selectedDate) {
					$main.find(".filter_date_add_to").datepicker("option", "minDate", selectedDate);
				}
			});
			$main.find(".filter_date_add_to").datepicker({
				defaultDate: "+1w",
				changeMonth: true,
				numberOfMonths: 3,
				onClose: function (selectedDate) {
					$main.find(".filter_date_add_from").datepicker("option", "maxDate", selectedDate);
				}
			});

			$main.find(".filter_date_update_from").datepicker({
				defaultDate: "+1w",
				changeMonth: true,
				numberOfMonths: 3,
				onClose: function (selectedDate) {
					$main.find(".filter_date_update_to").datepicker("option", "minDate", selectedDate);
				}
			});
			$main.find(".filter_date_update_to").datepicker({
				defaultDate: "+1w",
				changeMonth: true,
				numberOfMonths: 3,
				onClose: function (selectedDate) {
					$main.find(".filter_date_update_from").datepicker("option", "maxDate", selectedDate);
				}
			});

			$('.pagination-items-page').on('click', function () {
				var $pagination = $(this);
				$('.elements').html($pagination.html());
				$elements = $pagination.html();
				$page = 1;
				$pagination.closest('ul').toggle();
				$search.trigger('click');
			});


			$pagination_select.on('click', function () {
				var $scope = $(this);
				$scope.closest('.ph-panel').find('.dropdown-menu-panel').toggle();
			});

			$pager_mass.on('click', '.pagination-link', function (e) {
				var $pagination = $(this);
				if ($pagination.hasClass('current'))
					return false;
				e.preventDefault();
				$page = $pagination.attr('data-page');
				$search.trigger('click');
			});

			$search.on('click', function () {
				if (!massProcess())
				{
					$massupdateproductsProcess = true;
					$.notify(search, '', {
						autoHideDelay: 2000
					});

					var $sorted_type = !(parseInt($sort.val()) % 2);
					var $sorted_by = parseInt($sort.val());

					$mask.show();
					$loading.show();

					$products_list.html('');
					$footer_mass.hide();
					$products_list_header.hide();

					$.ajax({
						data: {
							categories: $main.find('.filter_category_element').map(function () {
								var $scope = $(this);
								return $scope.prop('checked') ? $(this).val() : null;
							}).get(),
							manufacturer: $main.find('.filter_manufacturer_element').map(function () {
								var $scope = $(this);
								return $scope.prop('checked') ? $(this).val() : null;
							}).get(),
							product: $main.find('.filter_product_name').val(),
							filter_ean: $main.find('.filter_ean').val(),
							price: $main.find('.filter_range_price').val(),
							quantity: $main.find('.filter_range_quantity').val(),
							weight: $main.find('.filter_range_weight').val(),
							date_add_from: $main.find('.filter_date_add_from').val(),
							date_add_to: $main.find('.filter_date_add_to').val(),
							date_update_from: $main.find('.filter_date_update_from').val(),
							date_update_to: $main.find('.filter_date_update_to').val(),
							active: $main.find('.filter_active').val(),
							categories_default: $main.find('.filter-category-default-input').map(function () {
								var $scope = $(this);
								return $scope.prop('checked') ? $(this).val() : null;
							}).get(),
							shop: $main.find('.filter_shop').val(),
							promotion: $main.find('.filter_promotion').val(),
							show_empty: $main.find('.filter_show_empty').val(),
							page: $page,
							elements: $elements,
							sorted_type: $sort_type,
							sorted_by: $sort_by,
							filter: true
						},
						type: 'POST',
						dataType: 'json',
						success: function ($response)
						{
							if ($response.datas.product_count > 0)
							{
								/*$main.find("#slider-range-price").slider("option", "values", [Math.floor($response.datas.p_min), Math.ceil($response.datas.p_max)]);
								 $main.find(".filter_range_price").val("" + $main.find("#slider-range-price").slider("values", 0) +
								 " - " + $main.find("#slider-range-price").slider("values", 1));
								 
								 $main.find("#slider-range-quantity").slider("option", "values", [Math.floor($response.datas.q_min), Math.ceil($response.datas.q_max)]);
								 $main.find(".filter_range_quantity").val("" + $main.find("#slider-range-quantity").slider("values", 0) +
								 " - " + $main.find("#slider-range-quantity").slider("values", 1));
								 
								 $main.find("#slider-range-weight").slider("option", "values", [Math.floor($response.datas.w_min), Math.ceil($response.datas.w_max)]);
								 $main.find(".filter_range_weight").val("" + $main.find("#slider-range-weight").slider("values", 0) +
								 " - " + $main.find("#slider-range-weight").slider("values", 1));*/
								$pager_mass.html($response.pager);
								$.notify(productsCountFound + ': ' + $response.datas.product_count, 'success', {
									autoHideDelay: 2000
								});
								$footer_mass.show();
								$products_list_header.show();
							} else
								$.notify(productsNotFound, '', {
									autoHideDelay: 2000
								});
							if ($response.table)
							{
								if ($response.result)
									$products_list.html($response.result);
							} else
								$products.data('products', $response.result2);

							$('html, body').animate({
								scrollTop: ($products.offset().top - 150)
							}, 1000);
							$products.find('.ph-panel-badge').html($response.datas.product_count);
							$products.find('.ph-panel-form-field:first-child').after($response.info);

							/*var $header_mass = $products.find('.header-row-multi');
							 
							 var $i = 0;
							 $products.find('.header-row th').each(function () {
							 var $sync = $(this);
							 var $value = $sync.width() - 14;
							 $sync.css({width: $value});
							 $sync.find('p').css({width: $value});
							 $header_mass.find('td:eq(' + $i + ')').css({minWidth: $value});
							 $i++;
							 });*/

							$products_list.trigger('filter');
							if ($response.datas.product_count > 0) {
								$products_list_header.trigger('on_scroll');
							}
							$
						},
						error: function ()
						{
							$.notify(serverError, 'error', {
								autoHideDelay: 2000
							});
						},
						complete: function ()
						{
							$massupdateproductsProcess = false;
							$mask.hide();
							$loading.hide();
						}
					});
				}
			});

			$reset.on('click', function () {
				$main.find('.filter_category_element').prop('checked', false);
				$main.find('.filter_manufacturer_element').prop('checked', false);
				$main.find('.filter-category-default-input').prop('checked', false);
				$main.find('.filter_product_name').val('');
				$main.find('.filter_ean').val('');
				$main.find('.filter-date').val('');
				$main.find('.filter_active').val(0);
				$main.find('.filter_shop').val(0);
				$main.find('.filter_promotion').val(0);
				$main.find("#slider-range-price").slider("option", "values", [$("#slider-range-price").slider("option", "min"), $("#slider-range-price").slider("option", "max")]);
				$main.find(".filter_range_price").val("" + $main.find("#slider-range-price").slider("values", 0) +
						" - " + $main.find("#slider-range-price").slider("values", 1));
				$main.find("#slider-range-quantity").slider("option", "values", [$("#slider-range-quantity").slider("option", "min"), $("#slider-range-quantity").slider("option", "max")]);
				$main.find(".filter_range_quantity").val("" + $main.find("#slider-range-quantity").slider("values", 0) +
						" - " + $main.find("#slider-range-quantity").slider("values", 1));
				$main.find("#slider-range-weight").slider("option", "values", [$("#slider-range-weight").slider("option", "min"), $("#slider-range-weight").slider("option", "max")]);
				$main.find(".filter_range_weight").val("" + $main.find("#slider-range-weight").slider("values", 0) +
						" - " + $main.find("#slider-range-weight").slider("values", 1));

				$search.trigger('click');
			});

			$check_all_c_d.on('click', function () {
				$main.find('.filter-category-default-input').prop('checked', $(this).prop('checked'));
			});

			$check_all_c.on('click', function () {
				$main.find('.filter_category_element').prop('checked', $(this).prop('checked'));
			});

			$check_all_m.on('click', function () {
				$main.find('.filter_manufacturer_element').prop('checked', $(this).prop('checked'));
			});

			$search.trigger('click');

			$products_list_header.on('click', '.sort-link.up', function () {
				var $scope = $(this);
				$('.sort-link').removeClass('active');
				$scope.addClass('active');
				$sort_type = 0;
				$sort_by = $scope.parent('span').attr('sort-name');
				$search.trigger('click');
			});

			$products_list_header.on('click', '.sort-link.down', function () {
				var $scope = $(this);
				$('.sort-link').removeClass('active');
				$scope.addClass('active');
				$sort_type = 1;
				$sort_by = $scope.parent('span').attr('sort-name');
				$search.trigger('click');
			});
			/*
			 $(window).on('scroll', function () {
			 var $window = $(this);
			 var $header_mass = $('.header-row-multi');
			 if ($header_mass.length) {
			 var $header_sort = $('.header-row');
			 if ($window.scrollTop() > $header_sort.offset().top) {
			 $header_mass.addClass('header-fixed');
			 $header_mass.css({left: $product_table_left});
			 } else {
			 $header_mass.removeClass('header-fixed');
			 $header_mass.css({left: 0});
			 }
			 }
			 });*/
		});
	};
})($);
