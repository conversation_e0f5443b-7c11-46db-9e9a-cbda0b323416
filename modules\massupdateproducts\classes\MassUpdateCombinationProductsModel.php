<?php

/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateCombinationProductsModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateCombinationProductsModel');
        parent::__construct($module, $object, $context);

        //price
        $this->fields['price'] = array(
            'display_name' => $this->module->l('Price', $this->languages_name),
            'name' => 'price',
            'active' => true,
            'type' => self::TYPE_PRI,
            'combination' => true,
            'sort' => true,
            'base_name' => 'ps.price'
        );

        //quantity
        $this->fields['quantity'] = array(
            'display_name' => $this->module->l('Quantity', $this->languages_name),
            'name' => 'quantity',
            'active' => true,
            'type' => self::TYPE_INT,
            'combination' => true,
            'sort' => true,
            'base_name' => !$this->object->shop_group->share_stock ? 'sa.quantity ' : 'p.quantity '
        );

        //minimal_quantity
        $this->fields['minimal_quantity'] = array(
            'display_name' => $this->module->l('Minimal quantity', $this->languages_name),
            'name' => 'minimal_quantity',
            'active' => true,
            'combination' => true,
            'type' => self::TYPE_INT,
            'sort' => true,
            'base_name' => 'p.minimal_quantity'
        );

        //tax
        $this->fields['tax'] = array(
            'display_name' => $this->module->l('Tax', $this->languages_name),
            'name' => 'tax',
            'active' => true,
            'type' => self::TYPE_SEL,
            'sort' => true,
            'base_name' => 'ps.id_tax_rules_group'
        );

        $tax_selected = array(
            0 => $this->module->l('none', $this->languages_name)
        );
        if ($this->object->tax_rules_group)
            foreach ($this->object->tax_rules_group as $tax) $tax_selected[$tax['id_tax_rules_group']] = $tax['name'];
        $this->fields['tax']['select'] = $tax_selected;
        $this->fields['tax']['extra'] = $this->object->tax_rates;

        //weight
        $this->fields['weight'] = array(
            'display_name' => $this->module->l('Weight', $this->languages_name),
            'name' => 'weight',
            'active' => true,
            'type' => self::TYPE_INT,
            'combination' => true,
            'sort' => true,
            'base_name' => 'p.weight'
        );

        //code
        $this->fields['code'] = array(
            'display_name' => $this->module->l('Code', $this->languages_name),
            'name' => 'code',
            'active' => true,
            'type' => self::TYPE_STR,
            'validate' => 'isReference',
            'combination' => true,
            'sort' => true,
            'base_name' => 'p.reference'
        );

        //ean
        $this->fields['ean'] = array(
            'display_name' => $this->module->l('Ean', $this->languages_name),
            'name' => 'ean',
            'active' => true,
            'type' => self::TYPE_STR,
            'validate' => 'isEan',
            'combination' => true,
            'sort' => true,
            'base_name' => 'p.ean13'
        );
    }

    public function display($result)
    {
        $ids_product = $result['result'];
        $result['result'] = '';
        $result['table'] = true;
		
        if ($ids_product)
            foreach ($ids_product as $product_arr) {
                $product = new Product((int)$product_arr['id_product'], false, null, (int)$product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['datas']['product_count'] --;
                    continue;
                }
		
                $combinations = $product->getAttributeCombinations($this->context->language->id);
	
                if (!$combinations || empty($combinations)){
					$result['datas']['product_count'] --;
                    continue;
				}

                $result['result'] .= $this->displayProduct($product);

                /* $combination_tmp = array();

                  if ($combinations)
                  foreach ($combinations as $combination_arr)
                  {
                  if (array_key_exists($combination_arr['id_product_attribute'], $combination_tmp))
                  continue;
                  $combination = new Combination($combination_arr['id_product_attribute']);
                  if (!ValidateCore::isLoadedObject($combination))
                  continue;
                  $result['result'] .= $this->displayCombination($product, $combination);
                  $combination_tmp[$combination->id] = true;
                  } */
            }

        return $result;
    }

    public function displayProduct(&$product)
    {
        parent::displayProduct($product);
        $product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
                        $this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
        $product->quantity = $this->object->shop_group->share_stock ? $product->quantity : StockAvailable::getQuantityAvailableByProduct($product->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $product->full_name = $product->name[$this->context->language->id];
        $product->has_combination = !!$product->getAttributeCombinations($this->context->language->id);

        $this->fields['weight']['display'] = false;
        $this->fields['code']['display'] = false;
        $this->fields['ean']['display'] = false;
        $this->fields['price']['class'] = 'multi-com';
        $this->fields['tax']['class'] = 'multi-com';
        $this->fields['quantity']['class'] = 'multi-com';
        $this->fields['minimal_quantity']['class'] = 'multi-com';
        $this->fields['tax']['value'] = $product->id_tax_rules_group;
        $this->fields['tax']['extra'] = $this->object->tax_rates;

        $this->context->smarty->assign(array(
            'product' => $product,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages
        ));

        return $this->object->createTemplate('tr-product.tpl')->fetch();
    }

    public function displayCombination(&$product, &$combination)
    {
        if (!parent::displayCombination($product, $combination))
            return '';

        $cover = $combination->getWsImages();
        $combination->image_link = isset($cover[0]['id']) && ($cover[0]['id']) ? $this->context->link->getImageLink(
                        $this->object->img_type, isset($cover[0]['id']) ? $cover[0]['id'] : 0, $this->object->img_type) : '';
        $combination->quantity = $this->object->shop_group->share_stock ?
                $combination->quantity : StockAvailable::getQuantityAvailableByProduct($product->id, $combination->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $names = $combination->getAttributesName($this->context->cookie->id_lang);
        $tmp_n = array();
        if ($names)
            foreach ($names as $name) $tmp_n[] = $name['name'];
        $combination->full_name = implode(' - ', $tmp_n);

        $this->fields['price']['class'] = 'to-send';
        $this->fields['tax']['class'] = 'to-send';
        $this->fields['quantity']['class'] = 'to-send';
        $this->fields['minimal_quantity']['class'] = 'to-send';
        $this->fields['price']['display'] = true;
        $this->fields['quantity']['display'] = true;
        $this->fields['minimal_quantity']['display'] = true;
        $this->fields['weight']['display'] = true;
        $this->fields['code']['display'] = true;
        $this->fields['ean']['display'] = true;
        $this->fields['tax']['display'] = true;

        $this->fields['price']['value'] = array(
            0 => round($combination->price, 2),
            1 => round($combination->price * (1 + ($product->rate / 100)), 2)
        );
        $this->fields['code']['value'] = $combination->reference;
        $this->fields['quantity']['value'] = $combination->quantity;
        $this->fields['minimal_quantity']['value'] = $combination->minimal_quantity;
        $this->fields['weight']['value'] = $combination->weight;

        $this->context->smarty->assign(array(
            'product' => $product,
            'combination' => $combination,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
            'widths' => MassUpdateProductsAbstract::getWidths()
        ));
        return $this->object->createTemplate('tr-combination.tpl')->fetch();
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product => $params) {
                $result[$id_product] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );

                $product_params = array_key_exists('data', $params) ? $params['data'] : null;
                $combinations = array_key_exists('combinations', $params) ? $params['combinations'] : null;

                $product = new Product($id_product);

                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                if ($product_params) {
                    /*
                    $languages = Language::getLanguages(false);

                    if ($languages)
                        foreach ($languages as $language) if (isset($product_params['name_'.$language['iso_code']]))
                                $product->name[$language['id_lang']] = $product_params['name_'.$language['iso_code']];

                    $product->reference = isset($product_params['code']) ? $product_params['code'] : $product->reference;
                    $product->ean13 = isset($product_params['ean']) ? $product_params['ean'] : $product->ean13;
                    $product->price = isset($product_params['price']) ? $product_params['price'] : $product->price;
                    $product->id_tax_rules_group = isset($product_params['tax']) ? $product_params['tax'] : $product->id_tax_rules_group;
                    $product->weight = isset($product_params['weight']) ? $product_params['weight'] : $product->weight;
                    if (!!(int)$this->object->shop_group->share_stock)
                        $product->quantity = isset($product_params['quantity']) ? $product_params['quantity'] : $product->quantity;
                    elseif (isset($product_params['quantity']))
                        StockAvailable::setQuantity($id_product, 0, $product_params['quantity']);

                    $errors = $product->validateFields(false, true);
                    $errors2 = $product->validateFieldsLang(false, true);
                    if ($errors !== true || $errors2 !== true) {
                        $result[$id_product]['message'] = '';
                        if ($errors !== true)
                            $result[$id_product]['message'] .= '<p style="color: #FFF;">'.(is_bool($errors) ?
                                            $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)).'</p>';
                        if ($errors2 !== true)
                            $result[$id_product]['message'] .= '<p style="color: #FFF;">'.(is_bool($errors2) ?
                                            $this->module->l('Validate error', $this->languages_name) : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)).'</p>';
                        continue;
                    }*/
                    /*else {
                        if ($product->update()) {*/
                            $result[$id_product]['error'] = false;
                            $result[$id_product]['message'] = $this->displayProduct($product);
                        /*} else {
                            $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                            continue;
                        }*/
                    //}
                } else {
                    $result[$id_product]['error'] = false;
                    $result[$id_product]['message'] = $this->displayProduct($product);
                }
                
                if ($combinations) {
                    foreach ($combinations as $id_combination => $combination_params) {
                        $result[$id_product]['combinations'][$id_combination] = array(
                            'error' => true,
                            'message' => ''
                        );
                        $combination = new Combination($id_combination);

                        if (!Validate::isLoadedObject($combination)) {
                            $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Combination not found', $this->languages_name);
                            continue;
                        }

                        $combination->reference = isset($combination_params['code']) ? $combination_params['code'] : $combination->reference;
                        $combination->ean13 = isset($combination_params['ean']) ? $combination_params['ean'] : $combination->ean13;
                        $combination->price = isset($combination_params['price']) ? $combination_params['price'] : $combination->price;
                        $combination->weight = isset($combination_params['weight']) ? $combination_params['weight'] : $combination->weight;
                        if (!!(int)$this->object->shop_group->share_stock)
                            $combination->quantity = isset($combination_params['quantity']) ? $combination_params['quantity'] : $combination->quantity;
                        elseif (isset($combination_params['quantity']))
                            StockAvailable::setQuantity($id_product, $id_combination, $combination_params['quantity'], $this->context->shop->id);
                        $combination->minimal_quantity = isset($combination_params['minimal_quantity']) ? $combination_params['minimal_quantity'] : $combination->minimal_quantity;

                        $errors = $combination->validateFields(false, true);
                        if ($errors !== true) {
                            $result[$id_product]['combinations'][$id_combination]['message'] = is_bool($errors) ?
                                    $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode('<br>', $errors) : $errors);
                            continue;
                        } else {
                            if ($combination->update()) {
                                $result[$id_product]['combinations'][$id_combination]['error'] = false;
                                $result[$id_product]['combinations'][$id_combination]['message'] = $this->displayCombination($product, $combination);
                            } else {
                                $result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Problem with update', $this->languages_name);
                                continue;
                            }
                        }

                        if (!!(int)$this->object->shop_group->share_stock) {
                            if (Db::getInstance()->getValue('SELECT id_stock_available FROM `'._DB_PREFIX_.'stock_available` WHERE `id_product` = '
                                            .pSQL($id_product).' AND `id_product_attribute` = '
                                            .pSQL($id_combination).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id)))
                                Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'stock_available` SET `quantity` = '
                                        .pSQL(isset($combination_params['quantity']) ? $combination_params['quantity'] : $combination->quantity)
                                        .' WHERE `id_product` = '.pSQL($id_product).' AND `id_product_attribute` = '
                                        .pSQL($id_combination).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id));
                            else
                                Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_
                                        .'stock_available` (`id_product`, `id_product_attribute`, `id_shop`, `id_shop_group`, `quantity`, `depends_on_stock`, `out_of_stock`) VALUES ('
                                        .pSQL($id_product).','.pSQL($id_combination).',0,'.pSQL($this->object->shop_group->id).','.pSQL(isset($combination_params['quantity']) ?
                                                        $combination_params['quantity'] : $combination->quantity).',0,2)');
                            Cache::clean('StockAvailable::getQuantityAvailableByProduct_'.(int)$id_product.'*');
                            $new_combination = new Combination($id_combination, null, $id_shop);
                            $new_combination->quantity = isset($combination_params['quantity']) ? $combination_params['quantity'] : $combination->quantity;
                            $result[$id_product]['combinations'][$id_combination]['message'] = $this->displayCombination($product, $new_combination);
                        }

                        if (!!(int)$this->object->shop_group->share_stock) {
                            if (Db::getInstance()->getValue('SELECT id_stock_available FROM `'._DB_PREFIX_.'stock_available` WHERE `id_product` = '.pSQL($id_product)
                                            .' AND `id_product_attribute` = '
                                            .pSQL(0).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id)))
                                Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'stock_available` SET `quantity` = '.pSQL(isset($product_params['quantity']) ?
                                                        $product_params['quantity'] : $this->quantity).' WHERE `id_product` = '.pSQL($id_product).' AND `id_product_attribute` = '
                                        .pSQL(0).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id));
                            else
                                Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_
                                        .'stock_available` (`id_product`, `id_product_attribute`, `id_shop`, `id_shop_group`, `quantity`, `depends_on_stock`, `out_of_stock`) VALUES ('
                                        .pSQL($id_product).',0,0,'.pSQL($this->object->shop_group->id).','
                                        .pSQL(isset($product_params['quantity']) ? $product_params['quantity'] : $this->quantity).',0,2)');
                            Cache::clean('StockAvailable::getQuantityAvailableByProduct_'.(int)$id_product.'*');
                            $new_product = new Product($id_product, false, null, $id_shop);
                            $new_product->quantity = isset($product_params['quantity']) ? $product_params['quantity'] : $this->quantity;
                            $result[$id_product]['message'] = $this->displayProduct($new_product);
                        }
                    }
                }
                if (!!(int)$this->object->shop_group->share_stock) {
                    if (Db::getInstance()->getValue('SELECT id_stock_available FROM `'._DB_PREFIX_.'stock_available` WHERE `id_product` = '.pSQL($id_product)
                                    .' AND `id_product_attribute` = '
                                    .pSQL(0).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id)))
                        Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'stock_available` SET `quantity` = '.pSQL(isset($product_params['quantity']) ?
                                                $product_params['quantity'] : $this->quantity).' WHERE `id_product` = '.pSQL($id_product).' AND `id_product_attribute` = '
                                .pSQL(0).' AND `id_shop` = 0 AND `id_shop_group` = '.pSQL($this->object->shop_group->id));
                    else
                        Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_
                                .'stock_available` (`id_product`, `id_product_attribute`, `id_shop`, `id_shop_group`, `quantity`, `depends_on_stock`, `out_of_stock`) VALUES ('
                                .pSQL($id_product).',0,0,'.pSQL($this->object->shop_group->id).','
                                .pSQL(isset($product_params['quantity']) ? $product_params['quantity'] : $this->quantity).',0,2)');
                    Cache::clean('StockAvailable::getQuantityAvailableByProduct_'.(int)$id_product.'*');
                    $new_product = new Product($id_product, false, null, $id_shop);
                    $new_product->quantity = isset($product_params['quantity']) ? $product_params['quantity'] : $this->quantity;
                    $result[$id_product]['message'] = $this->displayProduct($new_product);
                }
            } else
            $end = true;

        return array(
            'raport' => $result,
            'end' => $end
        );
    }
	
	public function filter($count = false, $ids = array())
    {
		
        if (Tools::isSubmit('filter')) {
			
            $categories = Tools::getValue('categories', null);
            $categories_default = Tools::getValue('categories_default', null);
            $manufacturer = Tools::getValue('manufacturer', null);
            $help_arr = explode(' - ', Tools::getValue('price', ''));
            $price_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $price_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('quantity', ''));
            $quantity_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[0]) : null;
            $quantity_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('weight', ''));
            $weight_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $weight_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $date_add_from = Tools::getValue('date_add_from', null);
            $date_add_to = Tools::getValue('date_add_to', null);
            $date_upd_from = Tools::getValue('date_update_from', null);
            $date_upd_to = Tools::getValue('date_update_to', null);
            $active = (int)Tools::getValue('active', null);
            $promotion = (int)Tools::getValue('promotion', null);
            $show_empty = (int)Tools::getValue('show_empty', null);
            $page = (int)Tools::getValue('page', 0);
            $elements = (int)Tools::getValue('elements', 20);
            $product_name = pSQL(Tools::getValue('product', null));
            $sorted_type = (int)Tools::getValue('sorted_type', 0);
            $sorted_by = pSQL(Tools::getValue('sorted_by', ''));

            $select = '';
            $limit = '';
            $method = '';

            $from = 'FROM `'._DB_PREFIX_.'product` as p ';
            $join = 'JOIN `'._DB_PREFIX_.'product_shop` as ps ON p.id_product = ps.id_product AND ps.id_shop = '.pSQL($this->context->shop->id).' ';
            $join .= 'JOIN `'._DB_PREFIX_.'product_lang` as pl ON p.id_product = pl.id_product AND ps.id_shop = pl.id_shop AND pl.id_lang = '
                    .(int)$this->context->language->id.' ';

            $where = 'WHERE 1=1 ';
            $where_e = '';
            $where_e .= $price_from ? ('AND ps.price >= '.$price_from.' ') : '';
            $where_e .= $price_to ? ('AND ps.price <= '.$price_to.' ') : '';
            $where .= $categories_default ? ('AND ps.id_category_default IN ('.implode(',', $categories_default).') ') : '';
            $where_e .= $weight_from ? ('AND p.weight >= '.$weight_from.' ') : '';
            $where_e .= $weight_to ? ('AND p.weight <= '.$weight_to.' ') : '';
            $where .= $product_name ? ('AND (pl.name LIKE \'%'.$product_name.'%\' OR p.reference LIKE \'%'.$product_name.'%\') ') : '';
            $where .= $date_add_from ? ('AND ps.date_add >= \''.$date_add_from.'\' ') : '';
            $where .= $date_add_to ? ('AND ps.date_add <= \''.$date_add_to.'\' ') : '';
            $where .= $date_upd_from ? ('AND ps.date_upd >= \''.$date_upd_from.'\' ') : '';
            $where .= $date_upd_to ? ('AND ps.date_upd <= \''.$date_upd_to.'\' ') : '';
            $where .= $ids ? 'AND ps.id_product IN ('.implode(',', $ids).') ' : '';
            $where .= $active ? ($active == 1 ? 'AND ps.active = 1 ' : 'AND ps.active =  0 ') : '';
            $where .= $manufacturer ? ('AND p.id_manufacturer IN ('.implode(',', $manufacturer).') ') : '';
            $where .= $categories ? ('AND ps.id_product IN (SELECT cp.id_product FROM `'
                    ._DB_PREFIX_.'category_product` as cp WHERE cp.id_category IN ('.
                    implode(',', $categories).'))') : '';

            if (!(int)$this->object->shop_group->share_stock) {
                $join .= 'JOIN `'._DB_PREFIX_
                        .'stock_available` as sa ON p.id_product = sa.id_product AND sa.id_product_attribute = 0 AND sa.id_shop = ps.id_shop ';
                $where_e .= $quantity_from ? ('AND sa.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND sa.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND sa.quantity = 0 ' : ($show_empty == 2 ? ' AND sa.quantity != 0 ' : '') );
            } else {
                $where_e .= $quantity_from ? ('AND p.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND p.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND p.quantity = 0 ' : ($show_empty == 2 ? ' AND p.quantity != 0 ' : '') );
            }

            if ($promotion == 1)
                $where .= ' AND p.id_product IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp) ';
            elseif ($promotion == 2)
                $where .= ' AND p.id_product NOT IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp WHERE sp.id_product_attribute = 0) ';

			/* 
			
				Just for this tab (combinations)
				Mod by Tomasz Dacka

			*/

			$where .= ' AND p.id_product IN (SELECT pa.id_product FROM `'._DB_PREFIX_.'product_attribute` pa '.Shop::addSqlAssociation('product_attribute', 'pa').' WHERE pa.`id_product` = p.id_product) ';
			
			/* MOD END */
			
            $sort = ' ORDER BY '.($sorted_by ? $sorted_by.' ' : 'ps.id_product ');

            if ($sorted_type)
                $sort .= 'DESC ';
            else
                $sort .= 'ASC ';

            if (!$count) {
                if ($page < 1)
                    $page = 1;
                $select = 'SELECT p.id_product, ps.id_shop ';
                $limit = ' LIMIT '.(($page - 1) * $elements).','.$elements;
                $method = 'executeS';
            }
            else {
                $select = 'SELECT COUNT(p.id_product) as q ';
                $limit = '';
                $method = 'getValue';
            }
			//Tools::d(Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort.$limit));
		
            return array(
                'datas' => array_merge($this->object->getDatas($from.$join.$where.$where_e), array('product_count' =>
                    Db::getInstance()->getValue('SELECT COUNT(p.id_product) as product_count '.
                            $from.$join.$where.$where_e))),
                'result' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort.$limit),
                'result2' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort),
                'sql' => $select.$from.$join.$where.$where_e.$sort.$limit
            );
			
        }
        return null;
    }

    public function hasCombination()
    {
        return true;
    }

}
