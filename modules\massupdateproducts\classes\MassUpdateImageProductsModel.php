<?php

/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateImageProductsModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateImageProductsModel');
        parent::__construct($module, $object, $context);
        //pictures
        $this->fields['pictures'] = array(
            'display_name' => $this->module->l('Pictures', ToolsCore::strtolower($this->settings_name)),
            'name' => 'pictures',
            'active' => true,
            'type' => self::TYPE_FIL,
            'combination' => true,
            'none-copy' => true
        );

        //add
        $this->fields['add'] = array(
            'display_name' => $this->module->l('Download', $this->languages_name),
            'name' => 'add',
            'active' => true,
            'type' => self::TYPE_FIL_DOWN,
            'combination' => true,
            'none-copy' => true
        );
    }

    public function save(array $data)
	{
		$result = array();
		$end = false;

		if ($data){
			foreach ($data as $id_product => $params)
			{
				$result[$id_product] = array(
					'combinations' => array(),
					'error' => true,
					'message' => ''
				);

				$product_params = array_key_exists('data', $params) ? $params['data'] : null;
				$combinations = array_key_exists('combinations', $params) ? $params['combinations'] : null;

				$product = new Product($id_product, false, null, $this->context->shop->id);

				if (!Validate::isLoadedObject($product))
				{
					$result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
					continue;
				}

				if ($product_params)
				{
					$languages = Language::getLanguages(false);

					$cover = (int)$product_params['cover'];
					$picture_belong = $product_params['picture_belong'];

					$picture_belong_arr = array();
					if (!is_array($picture_belong))
						$picture_belong = array($picture_belong);

					if ($picture_belong)
						foreach ($picture_belong as $pic)
						{
							$image = null;
							if (file_exists(_PS_MODULE_DIR_.$this->module->name.'/views/img/upload/'.$pic))
							{
								$images_types = ImageType::getImagesTypes('products');
								$image = new Image();
								$image->id_shop_list = array($this->context->shop->id);
								$image->id_product = $product->id;
								if ($languages)
									foreach ($languages as $language) if (isset($product_params['pictures_'.$pic.'_'.$language['iso_code']]))
											$image->legend[$language['id_lang']] = $product_params['pictures_'.$pic.'_'.$language['iso_code']];
								$image->add();
								if (!$cover || $pic == $cover)
									$cover = $image->id;
								$new_path = explode('/', $image->getPathForCreation());
								unset($new_path[count($new_path) - 1]);
								$new_path = implode('/', $new_path).'/';
								$extension = '.jpg';//Tools::substr($pic, Tools::strlen($pic) - 4);
								Tools::copy(_PS_MODULE_DIR_.$this->module->name.'/views/img/upload/'.$pic, $new_path.$image->id.$extension);
								foreach ($images_types as $image_type) ImageManager::resize($new_path.$image->id.$extension, $new_path.$image->id.'-'.
											Tools::stripslashes($image_type['name']).
											$extension, $image_type['width'], $image_type['height'], Tools::strlen($extension));
							}
							else
							{
								$image = new Image($pic, null, $this->context->shop->id);
								$image->id_shop_list = array($this->context->shop->id);
							}

							if (!Validate::isLoadedObject($image))
								continue;

							$picture_belong_arr[] = $image->id;
						}

					$image_cover = new Image($cover, null, $this->context->shop->id);
					$image_cover->id_shop_list = array($this->context->shop->id);

					if (!Validate::isLoadedObject($image_cover))
					{
						$result[$id_product]['message'] = $this->module->l('Cover not found', $this->languages_name);
						continue;
					}

					if ($languages)
						foreach ($languages as $language) if (($images = $product->getImages($this->context->language->id)))
								foreach ($images as $image)
								{
									$image_obj = new Image($image['id_image'], null, $this->context->shop->id);

									if (!Validate::isLoadedObject($image_obj))
										continue;
                                    if($image_obj->id != $image_cover->id){
                                        $image_obj->cover = false;
                                    }
									if (isset($product_params['pictures_'.$image['id_image'].'_'.$language['iso_code']]))
									{
										$image_obj->legend[$language['id_lang']] = $product_params['pictures_'.$image['id_image'].'_'.$language['iso_code']];
										$image_obj->id_shop_list = array($this->context->shop->id);
										$image_obj->update();
									}
								}

					$errors = $product->validateFields(false, true);
					if ($errors !== true)
					{
						$result[$id_product]['message'] = '<p>'.(is_bool($errors) ?
										$this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)).'</p>';
						continue;
					}
					else
					{
						//$product->setCoverWs($image_cover->id);

						$products_image_tmp = Db::getInstance()->executeS('SELECT id_image FROM `'._DB_PREFIX_.'image` WHERE id_product = '.pSQL($product->id));
						$products_image_list = array();
						if ($products_image_tmp)
						{
							foreach ($products_image_tmp as $pit)
								$products_image_list[] = $pit['id_image'];

							Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'image_shop` SET cover = NULL WHERE id_shop = '.pSQL($this->context->shop->id).'
									AND `id_image` IN ('.implode(',', $products_image_list).') ');
						}
						Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'image_shop` SET cover = 1 WHERE id_shop = '.pSQL($this->context->shop->id)
								.' AND id_image = '.pSQL($image_cover->id));
                        Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'image` SET cover = 1 WHERE id_product = '.pSQL($product->id)
                        .' AND id_image = '.pSQL($image_cover->id));
						$this->deleteImages($picture_belong_arr, $product->id);
						if ($product->update())
						{
							$result[$id_product]['error'] = false;
							$result[$id_product]['message'] = $this->displayProduct($product);
						}
						else
						{
							$result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
							continue;
						}
					}
				}
				else
				{
					$result[$id_product]['error'] = false;
					$result[$id_product]['message'] = $this->displayProduct($product);
				}

				if ($combinations)
					foreach ($combinations as $id_combination => $combination_params)
					{
						$result[$id_product]['combinations'][$id_combination] = array(
							'error' => true,
							'message' => ''
						);
						$combination = new Combination($id_combination);

						if (!Validate::isLoadedObject($combination))
						{
							$result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Combination not found', $this->languages_name);
							continue;
						}

						$picture_belong = $combination_params['picture_belong'];

						if (!is_array($picture_belong))
							$picture_belong = array($picture_belong);

						$errors = $combination->validateFields(false, true);
						if ($errors !== true)
						{
							$result[$id_product]['combinations'][$id_combination]['message'] = is_bool($errors) ?
									$this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode('<br>', $errors) : $errors);
							continue;
						}
						else
						{
							$this->deleteImagesFromCombination($picture_belong, $combination->id);
							if ($combination->update())
							{
								$result[$id_product]['combinations'][$id_combination]['error'] = false;
								$result[$id_product]['combinations'][$id_combination]['message'] = $this->displayCombination($product, $combination);
							}
							else
							{
								$result[$id_product]['combinations'][$id_combination]['message'] = $this->module->l('Problem with update', $this->languages_name);
								continue;
							}
						}
					}
			}

        }
		else{
            // Cleanup tmp images
            $this->deleteTemporaryImages();
			$end = true;
        }
		return array(
			'raport' => $result,
			'end' => $end
		);
	}

    protected function deleteTemporaryImages(){
        $files = glob(_PS_MODULE_DIR_.$this->module->name.'/views/img/upload/*');
        if($files && is_array($files) && !empty($files)){
            foreach($files as $file){
                $filename = basename($file);
                if($filename != 'index.php'){
                    unlink($file);
                }
            }
        }

    }

    protected function deleteImages($images, $id_product)
    {
        if ($images)
            $images_tmp = Db::getInstance()->executeS('SELECT id_image FROM `'.
                    _DB_PREFIX_.'image` WHERE id_image NOT IN ('.implode(',', $images).') AND id_product = '.
                    ((int)$id_product));

        if ($images_tmp)
            foreach ($images_tmp as $id_image) {
                $image = new Image($id_image['id_image'], null, $this->context->shop->id);
                if (Validate::isLoadedObject($image)) {
                    $image->id_shop_list = array($this->context->shop->id);
                    $image->delete();
                    Db::getInstance()->execute('DELETE FROM `'._DB_PREFIX_.'product_attribute_image` WHERE id_image = '.((int)$id_image['id_image']));
                }
            }
    }

    protected function deleteImagesFromCombination($images, $id_combination)
    {
        Db::getInstance()->execute('DELETE FROM `'._DB_PREFIX_.'product_attribute_image` WHERE id_product_attribute = '.((int)$id_combination));

        $sql = 'INSERT IGNORE INTO `'._DB_PREFIX_.'product_attribute_image` (`id_product_attribute`, `id_image`) VALUES ';
        $tmp = array();

        if ($images && is_array($images)) {
            foreach ($images as $image) {
                if ((int)$image) {
                    $tmp[] = '('.$id_combination.','.$image.')';
                }
            }
        }
        if ($tmp) {
            Db::getInstance()->execute($sql.implode(',', $tmp));
        }
    }

    public function display($result)
    {
        $ids_product = $result['result'];
        $result['result'] = '';
        $result['table'] = true;

        if ($ids_product)
            foreach ($ids_product as $product_arr) {
                $product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['dates']['products_count'] --;
                    continue;
                }

                $result['result'] .= $this->displayProduct($product);

                /* $combination_tmp = array();

                  if (($combinations = $product->getAttributeCombinations($this->context->language->id)))
                  foreach ($combinations as $combination_arr)
                  {
                  if (array_key_exists($combination_arr['id_product_attribute'], $combination_tmp))
                  continue;
                  $combination = new Combination($combination_arr['id_product_attribute']);
                  if (!ValidateCore::isLoadedObject($combination))
                  continue;
                  $result['result'] .= $this->displayCombination($product, $combination);
                  $combination_tmp[$combination->id] = true;
                  }
                 */
            }

        return $result;
    }

    public function displayCombination(&$product, &$combination)
    {
        if (!parent::displayCombination($product, $combination))
            return '';
        $cover = $combination->getWsImages();
        $combination->image_link = isset($cover[0]['id']) && $cover[0]['id'] ? $this->context->link->getImageLink(
                        $this->object->img_type_home, isset($cover[0]['id']) ? $cover[0]['id'] : 0, $this->object->img_type_home) : '';
        $combination->quantity = $this->object->shop_group->share_stock ?
                $combination->quantity : StockAvailable::getQuantityAvailableByProduct($product->id, $combination->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $names = $combination->getAttributesName($this->context->cookie->id_lang);
        $tmp_n = array();
        if ($names)
            foreach ($names as $name) $tmp_n[] = $name['name'];
        $combination->full_name = implode(' - ', $tmp_n);

        $pictures = array();
        $pictures_belong = array();
        if ($pictures_arr = $combination->getWsImages())
            foreach ($pictures_arr as $picture) {
                $image = new Image($picture['id'], null, $this->context->shop->id);
                if (!Validate::isLoadedObject($image))
                    continue;
                $pictures[$image->id] = array(
                    'link' => $this->context->link->getImageLink($this->object->img_type_home, $image->id, $this->object->img_type_home)
                );
                $pictures_belong[] = $image->id;
            }

        $this->fields['pictures']['extra'] = false;
        $this->fields['pictures']['value'] = $pictures;
        $this->fields['pictures']['belong'] = $pictures_belong;

        $products_images_tmp = $product->getImages($this->context->language->id);
        $products_images = array();
        if ($products_images_tmp) {
            foreach ($products_images_tmp as $product_image) {
                if (!in_array($product_image['id_image'], $pictures_belong)) {
                    $image = new Image($product_image['id_image'], $this->context->shop->id);
                    if (Validate::isLoadedObject($image)) {
                        $products_images[] = array(
                            'id' => $product_image['id_image'],
                            'link' => $this->context->link->getImageLink($this->object->img_type_home, $image->id, $this->object->img_type_home)
                        );
                    }
                }
            }
        }

        $this->context->smarty->assign(array(
            'product' => $product,
            'products_images' => $products_images,
            'combination' => $combination,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
            'widths' => MassUpdateProductsAbstract::getWidths()
        ));

        return $this->object->createTemplate('tr-combination.tpl')->fetch();
    }

    public function displayProduct(&$product)
    {
        parent::displayProduct($product);
        $product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
                        $this->object->img_type_home, $product->getCoverWs(), $this->object->img_type_home) : '';
        $product->quantity = $this->object->shop_group->share_stock ? $product->quantity : StockAvailable::getQuantityAvailableByProduct($product->id);
        $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
        $product->full_name = $product->name[$this->context->language->id];
        $product->has_combination = !!$product->getAttributeCombinations($this->context->language->id);

        $pictures = array();
        if ($pictures_arr = $product->getImages($this->context->language->id))
            foreach ($pictures_arr as $picture) {
                $image = new Image($picture['id_image'], null, $this->context->shop->id);
                if (!Validate::isLoadedObject($image))
                    continue;
                $pictures[$image->id] = array(
                    'cover' => $image->cover,
                    'legend' => $image->legend,
                    'link' => $this->context->link->getImageLink(
                            $this->object->img_type_home, $image->id, $this->object->img_type_home)
                );
            }

        $this->fields['pictures']['extra'] = true;
        $this->fields['pictures']['value'] = $pictures;

        $this->context->smarty->assign(array(
            'product' => $product,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages
        ));

        return $this->object->createTemplate('tr-product.tpl')->fetch();
    }

    public function upload()
    {
        $result = array(
            'error' => true,
            'field' => '',
            'file_name' => ''
        );

        if (isset($_FILES[0])) {
            $file = $_FILES[0];
            if ($file['error'] == 0) {
                $file_name = md5(time().$file['name']);
                $extension = false;
                switch ($file['type']) {
                    case 'image/jpeg':
                        $extension = '.jpg';
                        break;
                    case 'image/gif':
                        $extension = '.gif';
                        break;
                    case 'image/png':
                        $extension = '.png';
                        break;
                }
                if ($extension)
                    if (move_uploaded_file($file['tmp_name'], _PS_MODULE_DIR_.$this->module->name.'/views/img/upload/'.$file_name.$extension)) {
                        $legend = array();

                        foreach ($this->object->languages as $language) $legend[$language['id_lang']] = '';

                        $this->context->smarty->assign(array(
                            'extra' => true,
                            'name' => 'pictures',
                            'id_image' => $file_name.$extension,
                            'class_mass' => 'to-send',
                            'image' => array(
                                'cover' => false,
                                'link' => _MODULE_DIR_.$this->module->name.'/views/img/upload/'.$file_name.$extension,
                                'legend' => $legend
                            ),
                            'languages' => $this->object->languages
                        ));

                        $result = array(
                            'error' => false,
                            'file_name' => $file_name.$extension,
                            'field' => $this->object->createTemplate('fields/single_picture.tpl')->fetch()
                        );

                    }
            }
        }
        return $result;
    }

    public function hasCombination()
    {
        return true;
    }

}
