{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

{literal}
    <script type="text/javascript">
    var iso = '{/literal}{$iso}{literal}';
    var pathCSS = '{/literal}{$path_css}{literal}';
    var ad = '{/literal}{$ad}{literal}';
    </script>
{/literal}
{assign var='temp' value='product'}
<div class="product-row element-row {if !$product->active} inactive {/if}" p-id="{$product->id|intval}" data-tax="{if array_key_exists($product->id_tax_rules_group, $tax_rate_product)}{$tax_rate_product[$product->id_tax_rules_group]|floatval}{else}0{/if}">
    <div class="td-element first-td-element"  style="width: 25px;">  
	<div class="td-element-inside"  style="text-align: center;width: 25px;">
	    <i style="cursor: pointer;" class="fa fa-refresh ir-prod"></i>
	    <br/>
	    {if isset($product->has_combination) && $product->has_combination}<i style="position: absolute; bottom: 2px; cursor: pointer;" class="fa fa-plus"></i>{/if}
	</div>
    </div>
    <div class="td-element" style="width: 27px;">
	<div class="td-element-inside" style="text-align: center;width: 27px;">
	    <input type="checkbox" checked class="check_single check_product" />
	</div>
    </div>
    <div class="td-element" style="width: 27px;">
	<div class="td-element-inside" style="text-align: center;width: 27px;">
	    {$product->id|intval}
	</div>
    </div>
    <div class="td-element" style="width: 114px;">
	<div class="td-element-inside" style="width: 114px;">
	    <img src="{$product->image_link|strval}" style="width: 98px;height: 98px;border: 1px solid #000;" />
	</div>
    </div>
    <div class="td-element" style="width: 208px;">
	<div class="td-element-inside" style="width: 208px;">
	    {$product->full_name|strval}{if $product->reference}&nbsp;[{$product->reference|strval}]{/if}
	</div>
    </div>
    {assign var='counter' value='1'}
    {if $fields}
        {foreach $fields as $field}
            {if $field['active']}
                {assign var='counter' value=$counter + 1}
                <div class="td-element" style="width: {if $field['type'] eq 7}200px{else if $field['type'] eq 12}auto{else}{$widths[$field['type']]}px{/if};">
		    <div class="td-element-inside" style="{if $field['type'] neq 0}text-align: center;{/if}width: {if $field['type'] eq 7}200px{else if $field['type'] eq 12}auto{else}{$widths[$field['type']]}px{/if};">
			{assign var=file value=$field['type']}
			{if $field['display']}
			    {if $field['lang']}
                    {foreach $languages as $key2 => $language}
                        <span class="field_handler{if $field['type'] eq 12} field_handler_tiny{/if}">
                        {include file="./fields/field_$file.tpl" id_element=$product->id key=$key2 select=$field['select'] name=$field['name'] value=$field['value'][$language['id_lang']] lang=$language['iso_code'] extra=$field['extra'] class_mass=$field['class'] validate=$field['validate'] attr=$field['attr']}
                        <span class="field_handler_flag">
                            <img title="{$language['name']|strval}" style="width: 16px; height: 10px;" src="{$img_lang_src|strval}{$language['id_lang']|intval}.jpg" />
                        </span>
                        </span>
                    {/foreach}
			    {else}
				    {include file="./fields/field_$file.tpl" select=$field['select'] name=$field['name'] value=$field['value'] class_mass=$field['class'] extra=$field['extra'] validate=$field['validate'] attr=$field['attr']}
			    {/if}
			{/if}
		    </div>
                </div>
            {/if}
        {/foreach}
    {/if}
    <div class="td-element" style="width: 25px;">
	<div class="td-element-inside" style="width: 25px;">
	    <a style="padding: 5px;background-color: #FFF;border-radius: 5px;" href="index.php?controller=adminproducts&amp;id_product={$product->id|strval}&amp;updateproduct&amp;token={getAdminToken tab='AdminProducts'}" target="_blank" class="btn btn-default">
		<i class="fa fa-pencil"></i>
	    </a>
	</div>
    </div>
</div>
<div class="product-row-mask" p-id="{$product->id|intval}" style="line-height: 104px;">
    <div class="td-element" ></div>
    <div class="td-element" >{$product->id|intval}</div>
    <div class="td-element" ><img src="{$product->image_link|strval}" style="width: 98px; height: 98px;" /></div>
    <div class="td-element" >{$product->full_name|strval}</div>
    <div class="td-element"  colspan="{$counter|intval}"><span class="saving">{l s='Saving, please wait' mod='massupdateproducts'}...</span></div>
    <div class="td-element" ></div>
</div>
<div class="product-row-raport" style="line-height: 104px;" p-id="{$product->id|intval}">
    <div class="td-element" style="color: #FFF !important;font-weight: bold;"></div>
</div>
