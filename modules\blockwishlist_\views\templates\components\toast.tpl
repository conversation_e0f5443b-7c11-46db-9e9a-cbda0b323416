{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License version 3.0
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 *}

<div
    class="wishlist-toast"
    data-rename-wishlist-text="{l s='Wishlist name modified!' d='Modules.Blockwishlist.Shop'}"
    data-added-wishlist-text="{l s='Product added to wishlist!' d='Modules.Blockwishlist.Shop'}"
    data-create-wishlist-text="{l s='Wishlist created!' d='Modules.Blockwishlist.Shop'}"
    data-delete-wishlist-text="{l s='Wishlist deleted!' d='Modules.Blockwishlist.Shop'}"
    data-copy-text="{l s='Share link copied!' d='Modules.Blockwishlist.Shop'}"
    data-delete-product-text="{l s='Product deleted!' d='Modules.Blockwishlist.Shop'}"
  ></div>
