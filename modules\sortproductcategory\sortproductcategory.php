<?php
/**
 * Sort Product Category Module
 * 
 * <AUTHOR> IT
 * @copyright 2025
 * @license   Commercial
 */
 
if (!defined('_PS_VERSION_')) {
    exit;
}

class SortProductCategory extends Module
{
    public function __construct()
    {
        $this->name = 'sortproductcategory';
        $this->tab = 'administration';
        $this->version = '1.0.0';
        $this->author = 'Kardo IT';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '*******',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Sort Product Category - Zarządzanie kolejnością produktów');
        $this->description = $this->l('Zaawansowane zarządzanie kolejnością produktów w kategoriach z obsługą dużej liczby produktów, wyszukiwaniem i bulk operations.');
        $this->confirmUninstall = $this->l('Czy na pewno chcesz odinstalować ten moduł?');
    }

    public function install()
    {
        if (Shop::isFeatureActive()) {
            Shop::setContext(Shop::CONTEXT_ALL);
        }

        return parent::install() &&
            $this->installTab() &&
            Configuration::updateValue('SORTPRODUCTCATEGORY_ITEMS_PER_PAGE', 50) &&
            Configuration::updateValue('SORTPRODUCTCATEGORY_ENABLE_SEARCH', 1) &&
            Configuration::updateValue('SORTPRODUCTCATEGORY_ENABLE_BULK', 1) &&
            Configuration::updateValue('SORTPRODUCTCATEGORY_AUTO_SAVE', 1);
    }

    public function uninstall()
    {
        return parent::uninstall() &&
            $this->uninstallTab() &&
            Configuration::deleteByName('SORTPRODUCTCATEGORY_ITEMS_PER_PAGE') &&
            Configuration::deleteByName('SORTPRODUCTCATEGORY_ENABLE_SEARCH') &&
            Configuration::deleteByName('SORTPRODUCTCATEGORY_ENABLE_BULK') &&
            Configuration::deleteByName('SORTPRODUCTCATEGORY_AUTO_SAVE');
    }

    private function installTab()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminSortProductCategory';
        $tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'Sortowanie produktów';
        }
        $tab->id_parent = (int)Tab::getIdFromClassName('AdminCatalog');
        $tab->module = $this->name;
        return $tab->add();
    }

    private function uninstallTab()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminSortProductCategory');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            return $tab->delete();
        }
        return true;
    }

    public function getCategoryProducts($id_category, $page = 1, $limit = 50, $search = '', $order_by = 'position', $order_way = 'ASC')
    {
        $offset = ($page - 1) * $limit;
        
        $sql = 'SELECT p.id_product, pl.name, p.reference, p.price, p.active, cp.position, p.quantity
                FROM ' . _DB_PREFIX_ . 'category_product cp
                LEFT JOIN ' . _DB_PREFIX_ . 'product p ON (p.id_product = cp.id_product)
                LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE cp.id_category = ' . (int)$id_category;
        
        if ($search) {
            $sql .= ' AND (pl.name LIKE "%' . pSQL($search) . '%" OR p.reference LIKE "%' . pSQL($search) . '%")';
        }
        
        $sql .= ' ORDER BY ';
        switch ($order_by) {
            case 'name':
                $sql .= 'pl.name';
                break;
            case 'reference':
                $sql .= 'p.reference';
                break;
            case 'price':
                $sql .= 'p.price';
                break;
            case 'active':
                $sql .= 'p.active';
                break;
            case 'quantity':
                $sql .= 'p.quantity';
                break;
            default:
                $sql .= 'cp.position';
                break;
        }
        
        $sql .= ' ' . ($order_way == 'DESC' ? 'DESC' : 'ASC');
        $sql .= ' LIMIT ' . (int)$offset . ', ' . (int)$limit;
        
        return Db::getInstance()->executeS($sql);
    }

    public function getCategoryProductsCount($id_category, $search = '')
    {
        $sql = 'SELECT COUNT(*) as total
                FROM ' . _DB_PREFIX_ . 'category_product cp
                LEFT JOIN ' . _DB_PREFIX_ . 'product p ON (p.id_product = cp.id_product)
                LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE cp.id_category = ' . (int)$id_category;
        
        if ($search) {
            $sql .= ' AND (pl.name LIKE "%' . pSQL($search) . '%" OR p.reference LIKE "%' . pSQL($search) . '%")';
        }
        
        $result = Db::getInstance()->getRow($sql);
        return $result ? (int)$result['total'] : 0;
    }

    /**
     * Update product position in category
     */
    public function updateProductPosition($id_product, $id_category, $new_position)
    {
        // Get current position
        $current_position = $this->getProductPosition($id_product, $id_category);
        
        if ($current_position === false) {
            return false;
        }
        
        // If position is the same, no need to update
        if ($current_position == $new_position) {
            return true;
        }
        
        $db = Db::getInstance();
        
        if ($new_position > $current_position) {
            // Moving down - decrease positions of products between current and new position
            $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                    SET position = position - 1
                    WHERE id_category = ' . (int)$id_category . '
                    AND position > ' . (int)$current_position . '
                    AND position <= ' . (int)$new_position;
        } else {
            // Moving up - increase positions of products between new and current position
            $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                    SET position = position + 1
                    WHERE id_category = ' . (int)$id_category . '
                    AND position >= ' . (int)$new_position . '
                    AND position < ' . (int)$current_position;
        }

        $db->execute($sql);

        // Update the product's position
        $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                SET position = ' . (int)$new_position . '
                WHERE id_product = ' . (int)$id_product . '
                AND id_category = ' . (int)$id_category;

        return $db->execute($sql);
    }

    /**
     * Get product position in category
     */
    public function getProductPosition($id_product, $id_category)
    {
        $sql = 'SELECT position 
                FROM ' . _DB_PREFIX_ . 'category_product 
                WHERE id_product = ' . (int)$id_product . ' 
                AND id_category = ' . (int)$id_category;
        
        $result = Db::getInstance()->getRow($sql);
        return $result ? (int)$result['position'] : false;
    }

    /**
     * Bulk update product positions
     */
    public function bulkUpdatePositions($id_category, $positions)
    {
        if (empty($positions) || !is_array($positions)) {
            return false;
        }

        // Ultra-fast: Single UPDATE with CASE statement for all positions
        if (count($positions) > 0) {
            $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product SET position = CASE id_product ';

            $product_ids = [];
            foreach ($positions as $id_product => $new_position) {
                $id_product = (int)$id_product;
                $new_position = (int)$new_position;
                $sql .= 'WHEN ' . $id_product . ' THEN ' . $new_position . ' ';
                $product_ids[] = $id_product;
            }

            $sql .= 'END WHERE id_category = ' . (int)$id_category . ' AND id_product IN (' . implode(',', $product_ids) . ')';

            return Db::getInstance()->execute($sql);
        }

        return true;
    }

    /**
     * Get categories tree for selection
     */
    public function getCategoriesTree($id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = $this->context->language->id;
        }
        
        return Category::getNestedCategories(null, $id_lang, false);
    }

    /**
     * Clean positions in category (reorder from 0)
     */
    public function cleanCategoryPositions($id_category)
    {
        $db = Db::getInstance();

        // Ultra-fast: Use MySQL variables to renumber positions in one query
        $sql = 'SET @row_number = -1';
        $db->execute($sql);

        $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                SET position = (@row_number := @row_number + 1)
                WHERE id_category = ' . (int)$id_category . '
                ORDER BY position ASC';

        return $db->execute($sql);
    }

    /**
     * Auto-sort products by criteria
     */
    public function autoSortProducts($id_category, $sort_by = 'name', $direction = 'ASC')
    {
        $valid_sorts = ['name', 'reference', 'price', 'date_add', 'quantity'];
        if (!in_array($sort_by, $valid_sorts)) {
            return false;
        }

        $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';

        $sql = 'SELECT p.id_product
                FROM ' . _DB_PREFIX_ . 'category_product cp
                LEFT JOIN ' . _DB_PREFIX_ . 'product p ON (p.id_product = cp.id_product)
                LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE cp.id_category = ' . (int)$id_category . '
                ORDER BY ';

        switch ($sort_by) {
            case 'name':
                $sql .= 'pl.name';
                break;
            case 'reference':
                $sql .= 'p.reference';
                break;
            case 'price':
                $sql .= 'p.price';
                break;
            case 'date_add':
                $sql .= 'p.date_add';
                break;
            case 'quantity':
                $sql .= 'p.quantity';
                break;
        }

        $sql .= ' ' . $direction;

        $products = Db::getInstance()->executeS($sql);

        if (!$products) {
            return false;
        }

        // Build one massive UPDATE with CASE statement for ultra-fast execution
        if (count($products) > 0) {
            $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product SET position = CASE id_product ';

            foreach ($products as $index => $product) {
                $sql .= 'WHEN ' . (int)$product['id_product'] . ' THEN ' . (int)$index . ' ';
            }

            $sql .= 'END WHERE id_category = ' . (int)$id_category . ' AND id_product IN (';
            $product_ids = array_map(function($p) { return (int)$p['id_product']; }, $products);
            $sql .= implode(',', $product_ids) . ')';

            return Db::getInstance()->execute($sql);
        }

        return true;
    }

    /**
     * Move products to specific positions with gap handling
     */
    public function moveProductsToPositions($id_category, $product_positions, $insert_mode = 'replace')
    {
        if (empty($product_positions) || !is_array($product_positions)) {
            return false;
        }

        $db = Db::getInstance();

        if ($insert_mode === 'insert') {
            // Insert mode: make space for new products
            foreach ($product_positions as $id_product => $new_position) {
                // Move existing products down to make space
                $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                        SET position = position + 1
                        WHERE id_category = ' . (int)$id_category . '
                        AND position >= ' . (int)$new_position . '
                        AND id_product != ' . (int)$id_product;

                $db->execute($sql);

                // Set new position for the product
                $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                        SET position = ' . (int)$new_position . '
                        WHERE id_product = ' . (int)$id_product . '
                        AND id_category = ' . (int)$id_category;

                $db->execute($sql);
            }
        } else {
            // Replace mode: direct position assignment
            foreach ($product_positions as $id_product => $new_position) {
                $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                        SET position = ' . (int)$new_position . '
                        WHERE id_product = ' . (int)$id_product . '
                        AND id_category = ' . (int)$id_category;

                $db->execute($sql);
            }
        }

        return true;
    }

    /**
     * Get products with advanced filtering
     */
    public function getProductsAdvanced($id_category, $filters = [])
    {
        $page = isset($filters['page']) ? (int)$filters['page'] : 1;
        $limit = isset($filters['limit']) ? (int)$filters['limit'] : 50;
        $search = isset($filters['search']) ? pSQL($filters['search']) : '';
        $order_by = isset($filters['order_by']) ? $filters['order_by'] : 'position';
        $order_way = isset($filters['order_way']) ? $filters['order_way'] : 'ASC';
        $active_only = isset($filters['active_only']) ? (bool)$filters['active_only'] : false;
        $min_price = isset($filters['min_price']) ? (float)$filters['min_price'] : null;
        $max_price = isset($filters['max_price']) ? (float)$filters['max_price'] : null;
        $min_quantity = isset($filters['min_quantity']) ? (int)$filters['min_quantity'] : null;

        $offset = ($page - 1) * $limit;

        $sql = 'SELECT p.id_product, pl.name, p.reference, p.price, p.active, cp.position, p.quantity, p.date_add
                FROM ' . _DB_PREFIX_ . 'category_product cp
                LEFT JOIN ' . _DB_PREFIX_ . 'product p ON (p.id_product = cp.id_product)
                LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE cp.id_category = ' . (int)$id_category;

        // Apply filters
        if ($search) {
            $sql .= ' AND (pl.name LIKE "%' . $search . '%" OR p.reference LIKE "%' . $search . '%")';
        }

        if ($active_only) {
            $sql .= ' AND p.active = 1';
        }

        if ($min_price !== null) {
            $sql .= ' AND p.price >= ' . (float)$min_price;
        }

        if ($max_price !== null) {
            $sql .= ' AND p.price <= ' . (float)$max_price;
        }

        if ($min_quantity !== null) {
            $sql .= ' AND p.quantity >= ' . (int)$min_quantity;
        }

        // Apply sorting
        $sql .= ' ORDER BY ';
        switch ($order_by) {
            case 'name':
                $sql .= 'pl.name';
                break;
            case 'reference':
                $sql .= 'p.reference';
                break;
            case 'price':
                $sql .= 'p.price';
                break;
            case 'active':
                $sql .= 'p.active';
                break;
            case 'quantity':
                $sql .= 'p.quantity';
                break;
            case 'date_add':
                $sql .= 'p.date_add';
                break;
            default:
                $sql .= 'cp.position';
                break;
        }

        $sql .= ' ' . ($order_way == 'DESC' ? 'DESC' : 'ASC');
        $sql .= ' LIMIT ' . (int)$offset . ', ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get position gaps in category
     */
    public function getPositionGaps($id_category)
    {
        $sql = 'SELECT position
                FROM ' . _DB_PREFIX_ . 'category_product
                WHERE id_category = ' . (int)$id_category . '
                ORDER BY position ASC';

        $positions = Db::getInstance()->executeS($sql);

        if (!$positions) {
            return [];
        }

        $gaps = [];
        $expected = 0;

        foreach ($positions as $pos) {
            $current = (int)$pos['position'];
            if ($current != $expected) {
                for ($i = $expected; $i < $current; $i++) {
                    $gaps[] = $i;
                }
            }
            $expected = $current + 1;
        }

        return $gaps;
    }

    /**
     * Duplicate product positions from one category to another
     */
    public function duplicatePositions($source_category, $target_category)
    {
        $sql = 'SELECT cp1.id_product, cp1.position
                FROM ' . _DB_PREFIX_ . 'category_product cp1
                INNER JOIN ' . _DB_PREFIX_ . 'category_product cp2 ON (cp1.id_product = cp2.id_product)
                WHERE cp1.id_category = ' . (int)$source_category . '
                AND cp2.id_category = ' . (int)$target_category . '
                ORDER BY cp1.position ASC';

        $products = Db::getInstance()->executeS($sql);

        if (!$products) {
            return false;
        }

        $db = Db::getInstance();

        foreach ($products as $product) {
            $sql = 'UPDATE ' . _DB_PREFIX_ . 'category_product
                    SET position = ' . (int)$product['position'] . '
                    WHERE id_product = ' . (int)$product['id_product'] . '
                    AND id_category = ' . (int)$target_category;

            $db->execute($sql);
        }

        return true;
    }
}
