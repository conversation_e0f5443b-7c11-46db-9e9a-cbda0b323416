<?php
if (!defined('_PS_VERSION_')) {
    exit;
}

class Webixa_Preview extends Module
{


    public function __construct()
    {
        $this->name = 'webixa_preview';
        $this->tab = 'front_office_features';
        $this->version = '1.0.2'; // Updated version number
        $this->author = 'Webixa';
        $this->need_instance = 0;
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Webixa inspiracje');
        $this->description = $this->l('Moduł inspiracji.');
    }

    /**
     * Delete inspiration and all associated data
     * Includes security checks and proper cleanup of all related resources
     *
     * @return bool Success
     */
    public function delete()
    {
        // Validate object before deletion
        if (!$this->id || !Validate::isUnsignedId($this->id)) {
            return false;
        }

        // Verify shop ownership if multishop is active
        if (Shop::isFeatureActive() && !$this->isOwner()) {
            return false;
        }

        // Begin transaction to ensure data consistency
        $db = Db::getInstance();
        $db->execute('START TRANSACTION');

        try {
            // Call parent delete method first
            if (!parent::delete()) {
                $db->execute('ROLLBACK');
                return false;
            }

            // Clean positions after delete
            self::cleanPositions();

            // Delete category associations
            $db->delete('inspiration_to_category', 'id_inspiration = ' . (int)$this->id);

            // Delete product associations
            $db->delete('inspiration_product', 'id_inspiration = ' . (int)$this->id);

            // Get all images before deleting them
            $sql = 'SELECT `id_inspiration_image`, `image` FROM `' . _DB_PREFIX_ . 'inspiration_image`
                    WHERE `id_inspiration` = ' . (int)$this->id;
            $images = $db->executeS($sql);

            if ($images && is_array($images)) {
                // Delete physical image files
                foreach ($images as $image) {
                    // Validate image filename for security
                    if (!isset($image['image']) || !preg_match('/^inspiration_[a-zA-Z0-9_]+\.(jpg|jpeg|png|gif)$/', $image['image'])) {
                        continue; // Skip invalid filenames
                    }

                    $imagePath = _PS_MODULE_DIR_ . 'webixa_preview/views/img/' . $image['image'];
                    if (file_exists($imagePath) && is_file($imagePath)) {
                        @unlink($imagePath);
                    }

                    // Delete image-product associations for this specific image
                    if (isset($image['id_inspiration_image']) && (int)$image['id_inspiration_image'] > 0) {
                        $db->delete(
                            'inspiration_image_product',
                            'id_inspiration_image = ' . (int)$image['id_inspiration_image']
                        );
                    }
                }
            }

            // Delete image records
            $db->delete('inspiration_image', 'id_inspiration = ' . (int)$this->id);

            // Delete any remaining image-product associations using a join
            $sql = 'DELETE ip.* FROM `' . _DB_PREFIX_ . 'inspiration_image_product` ip
                    INNER JOIN `' . _DB_PREFIX_ . 'inspiration_image` ii ON ip.id_inspiration_image = ii.id_inspiration_image
                    WHERE ii.id_inspiration = ' . (int)$this->id;
            $db->execute($sql);

            // Commit the transaction
            $db->execute('COMMIT');
            return true;

        } catch (Exception $e) {
            // Rollback on any error
            $db->execute('ROLLBACK');

            // Log the error
            if (class_exists('PrestaShopLogger')) {
                PrestaShopLogger::addLog(
                    'Error deleting inspiration ID ' . (int)$this->id . ': ' . $e->getMessage(),
                    3, // Error severity
                    null,
                    'Inspiration',
                    (int)$this->id
                );
            }

            return false;
        }
    }

    /**
     * Check if current user/shop is the owner of this inspiration
     *
     * @return bool
     */
    protected function isOwner()
    {
        if (!Shop::isFeatureActive()) {
            return true;
        }

        $context = Context::getContext();
        if (!$context || !isset($context->shop) || !$context->shop->id) {
            return false;
        }

        // For shop employees, check shop ownership
        return (int)$this->id_shop === (int)$context->shop->id;
    }

    public function install()
    {
        // Include SQL install script and check its return value
        if (!include(dirname(__FILE__).'/sql/install.php')) {
            $this->_errors[] = $this->l('Failed to include install SQL script.');
            return false;
        }
        // $success variable should be defined and returned by install.php
        if (!isset($success) || !$success) {
             $this->_errors[] = $this->l('Database table creation failed.');
             // No need to return false here if install.php already logged errors and returned false
        }

        // Install Tabs and check return value
        if (!$this->installTabs()) {
            $this->_errors[] = $this->l('Failed to install admin tab (Inspirations).');
            return false; // Stop installation if tab fails
        }

        // Call parent install AFTER database setup and tab installation
        // Register hooks only if everything else succeeded
        return parent::install()
            && $this->registerHook('displayHeader')
            && $this->registerHook('moduleRoutes')
            && $this->registerHook('displayHome')
            && $this->registerHook('displayHomeContent');
            // Removed && $this->installTabs() as it's called earlier now
    }



    public function uninstall()
    {
        // Include SQL uninstall script
        include(dirname(__FILE__).'/sql/uninstall.php');
        // $success variable should be defined and returned by uninstall.php
        if (!isset($success) || !$success) {
             $this->_errors[] = $this->l('Database table removal failed.');
             // Log error? Decide if uninstall should fail completely.
             // For now, we attempt to continue uninstalling other parts.
        }

        // Uninstall Tabs
        if (!$this->uninstallTabs()) {
            $this->_errors[] = $this->l('Failed to uninstall admin tab.');
            // Decide if uninstall should fail completely.
        }

        // Call parent uninstall and unregister hooks
        return parent::uninstall();
            // Hooks are automatically unregistered by parent::uninstall()
    }

    public function installTabs()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminInspiration';
        $tab->name = array();
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'Inspiracje';
        }
        $tab->id_parent = (int)Tab::getIdFromClassName('AdminCatalog');
        $tab->module = $this->name;
        $tab->icon = '<i class="material-icons mi-trending_up">trending_up</i>';
        $tab->add();

		$tab2 = new Tab();
        $tab2->active = 1;
        $tab2->class_name = 'AdminInspirationCategory';
        $tab2->name = array();
        foreach (Language::getLanguages(true) as $lang) {
            $tab2->name[$lang['id_lang']] = 'Inspiration Categories';
        }
        $tab2->id_parent = (int)Tab::getIdFromClassName('AdminInspiration');
        $tab2->module = $this->name;
        $tab2->icon = '<i class="material-icons mi-folder-open">folder_open</i>';
        $tab2->add();

        $tab2 = new Tab();
        $tab2->active = 1;
        $tab2->class_name = 'AdminInspiration';
        $tab2->name = array();
        foreach (Language::getLanguages(true) as $lang) {
            $tab2->name[$lang['id_lang']] = 'Inspiration';
        }
        $tab2->id_parent = (int)Tab::getIdFromClassName('AdminInspiration');
        $tab2->module = $this->name;
        $tab2->icon = '<i class="material-icons mi-folder-open">folder_open</i>';
        return $tab2->add();
    }

    public function uninstallTabs()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminInspiration');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            if (Validate::isLoadedObject($tab)) {
                $tab->delete();
            }
        }
        $id_tab = (int)Tab::getIdFromClassName('AdminInspirationCategory');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            if (Validate::isLoadedObject($tab)) {
                $tab->delete();
            }
        }
        // Return true if tab doesn't exist or deletion failed (to allow module uninstall)
        return true;
    }

    public function hookDisplayHeader()
    {
        $this->context->controller->addJS($this->_path.'views/js/inspirations.js');
    }

    /**
     * Hook to display inspirations on homepage
     *
     * @return string HTML content to display
     */
    public function hookDisplayHome()
    {
        // Simply call the hookDisplayHomeContent method
        return $this->hookDisplayHomeContent();
    }

    /**
     * Hook to display inspirations on homepage content section
     *
     * @return string HTML content to display
     */
    public function hookDisplayHomeContent()
    {
        // Include necessary ObjectModel classes if not already included
        require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/Inspiration.php';
        require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/InspirationCategory.php';

        $id_lang = $this->context->language->id;

        // Get inspirations marked for homepage display
        $sql = new DbQuery();
        $sql->select('i.id_inspiration');
        $sql->from('inspiration', 'i');
        $sql->where('i.active = 1');
        $sql->where('i.display_on_homepage = 1');
        $sql->orderBy('i.position ASC');

        $ids = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if (!$ids) {
            return ''; // No inspirations to display
        }

        $inspirations = [];
        foreach ($ids as $id_row) {
            $inspiration = new Inspiration((int)$id_row['id_inspiration'], $id_lang);
            if (Validate::isLoadedObject($inspiration)) {
                // Get all images with their products
                $images = $inspiration->getAllImages($id_lang, $this->context);

                // Get products with coordinates for this inspiration
                $products = $inspiration->getProductsWithCoordinates($id_lang, $this->context);

                // Add to inspirations array with all needed data
                $inspirations[] = [
                    'id_inspiration' => $inspiration->id,
                    'title' => $inspiration->title,
                    'description' => $inspiration->description,
                    'short_description' => $inspiration->short_description ?: $this->truncateHTML($inspiration->description, 150), // Use short_description or truncated description
                    'link_rewrite' => $inspiration->link_rewrite,
                    'image_url' => $inspiration->getMainImageUrl(),
                    'rgb_color' => $inspiration->rgb_color ?: '#FFFFFF', // Default to white if not set
                    'url' => $this->context->link->getModuleLink('webixa_preview', 'inspiration', ['inspiration_rewrite' => $inspiration->link_rewrite]),
                    'products' => $products, // Add products with coordinates
                    'images' => $images // Add images array for slider
                ];
            }
        }

        $this->context->smarty->assign([
            'homepage_inspirations' => $inspirations,
            'inspiration_img_dir_uri' => $this->getPathUri().'views/img/', // URI path for templates
            'static_token' => Tools::getToken(false), // Add static token for cart operations
        ]);

        return $this->display(__FILE__, 'views/templates/hook/homepage_inspirations.tpl');
    }


    /**
     * Truncate HTML content to a specific length while preserving HTML tags
     *
     * @param string $html HTML content to truncate
     * @param int $length Maximum length of the truncated content
     * @param string $ending String to append to truncated content
     * @return string Truncated HTML content
     */
    protected function truncateHTML($html, $length = 150, $ending = '...')
    {
        if (!$html || strlen(strip_tags($html)) <= $length) {
            return $html;
        }

        // Load HTML into DOMDocument
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        $body = $dom->getElementsByTagName('body')->item(0);

        // If no body tag, return simple truncated text
        if (!$body) {
            return Tools::truncateString(strip_tags($html), $length) . $ending;
        }

        // Get all text nodes
        $textNodes = [];
        $this->getTextNodes($body, $textNodes);

        $totalLength = 0;
        $truncated = false;

        // Truncate text nodes until we reach the desired length
        foreach ($textNodes as $node) {
            $nodeText = $node->nodeValue;
            $nodeLength = mb_strlen($nodeText);

            if ($totalLength + $nodeLength > $length) {
                // Calculate how many characters we can take from this node
                $remainingLength = $length - $totalLength;
                if ($remainingLength > 0) {
                    $node->nodeValue = mb_substr($nodeText, 0, $remainingLength) . $ending;
                } else {
                    $node->nodeValue = '';
                }
                $truncated = true;
                break;
            }

            $totalLength += $nodeLength;
        }

        // If we truncated, remove all following text nodes
        if ($truncated) {
            $currentNode = $textNodes[array_search($node, $textNodes) + 1] ?? null;
            while ($currentNode) {
                $nextNode = $currentNode->nextSibling;
                $currentNode->parentNode->removeChild($currentNode);
                $currentNode = $nextNode;
            }
        }

        // Get the HTML content
        $truncatedHTML = '';
        $children = $body->childNodes;
        foreach ($children as $child) {
            $truncatedHTML .= $dom->saveHTML($child);
        }

        return $truncatedHTML;
    }

    /**
     * Helper method to get all text nodes in a DOM element
     *
     * @param DOMNode $node The DOM node to search
     * @param array &$textNodes Array to store found text nodes
     */
    protected function getTextNodes($node, &$textNodes)
    {
        if ($node->nodeType === XML_TEXT_NODE) {
            $textNodes[] = $node;
        } elseif ($node->childNodes) {
            foreach ($node->childNodes as $child) {
                $this->getTextNodes($child, $textNodes);
            }
        }
    }

    public function hookModuleRoutes()
    {
        // Define routes for the Inspiration controller
        return [
            // Route for the main inspiration list page
            'module-webixa_preview-inspiration-list' => [
                'controller' => 'inspiration', // Points to InspirationController.php
                'rule' => 'inspiracje',         // URL: /inspiracje
                'keywords' => [],
                'params' => [
                    'fc' => 'module',
                    'module' => 'webixa_preview',
                ]
            ],
            // Route for inspiration category pages
            'module-webixa_preview-inspiration-category' => [
                 'controller' => 'inspiration', // Points to InspirationController.php
                 'rule' => 'inspiracje/{category_rewrite}', // URL: /inspiracje/category-name
                 'keywords' => [
                     'category_rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'category_rewrite'],
                 ],
                 'params' => [
                     'fc' => 'module',
                     'module' => 'webixa_preview',
                 ]
             ],
            // Route for the inspiration details page with category
            'module-webixa_preview-inspiration-details-with-category' => [
                'controller' => 'inspiration', // Points to InspirationController.php
                'rule' => 'inspiracje/{category_rewrite}/{inspiration_rewrite}', // URL: /inspiracje/category-name/inspiration-name
                'keywords' => [
                    'category_rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'category_rewrite'],
                    'inspiration_rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'inspiration_rewrite'],
                ],
                'params' => [
                    'fc' => 'module',
                    'module' => 'webixa_preview',
                ]
            ],
            // Fallback route for the inspiration details page without category (for backward compatibility)
            'module-webixa_preview-inspiration-details' => [
                'controller' => 'inspiration', // Points to InspirationController.php
                'rule' => 'inspiracje/i/{inspiration_rewrite}', // URL: /inspiracje/i/inspiration-name
                'keywords' => [
                    'inspiration_rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'inspiration_rewrite'],
                ],
                'params' => [
                    'fc' => 'module',
                    'module' => 'webixa_preview',
                ]
            ]
        ];
    }
}
