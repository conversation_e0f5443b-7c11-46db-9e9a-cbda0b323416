<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */
$sql = [];
$charset = 'utf8';
if (version_compare(_PS_VERSION_, '1.7.7', '>=')) {
    $charset = 'utf8mb4';
}
$mysql_row_format = '';
if (_MYSQL_ENGINE_ == 'InnoDB') {
    $mysql_row_format = ' ROW_FORMAT=DYNAMIC';
}

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_block` (
    `id_webixa_content_block` INT(11) unsigned NOT NULL AUTO_INCREMENT,
    `active` TINYINT(1) unsigned NOT NULL,
    `name` VARCHAR(128) NOT NULL DEFAULT "",
    `type` VARCHAR(128) NOT NULL DEFAULT "",
    `slide_time` SMALLINT(3) unsigned NOT NULL DEFAULT 0,
    PRIMARY KEY (`id_webixa_content_block`),
    INDEX (`active`)
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_block_shop` (
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `id_shop` INT(11) NOT NULL,
    PRIMARY KEY (`id_webixa_content_block`, `id_shop`),
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_shop`)
        REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_block_lang` (
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `id_lang` INT(11) NOT NULL,
    `title` VARCHAR(128) NOT NULL DEFAULT "",
    `description` TEXT NOT NULL,
    PRIMARY KEY (`id_webixa_content_block`, `id_lang`),
    INDEX (`title`),
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_lang`)
        REFERENCES `' . _DB_PREFIX_ . 'lang`(`id_lang`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_hook` (
    `id_webixa_content_hook` INT(11) unsigned NOT NULL AUTO_INCREMENT,
    `hook` VARCHAR(128) NOT NULL DEFAULT "",
    PRIMARY KEY (`id_webixa_content_hook`),
    UNIQUE KEY (`hook`)
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_hook_block` (
    `id_webixa_content_hook` INT(11) unsigned NOT NULL,
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `position` INT(11) unsigned NOT NULL,
    `template` VARCHAR(128) NOT NULL DEFAULT "",
    PRIMARY KEY (`id_webixa_content_hook`, `id_webixa_content_block`),
    INDEX (`position`),
    FOREIGN KEY (`id_webixa_content_hook`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_hook`(`id_webixa_content_hook`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_item` (
    `id_webixa_content_item` INT(11) unsigned NOT NULL AUTO_INCREMENT,
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `position` INT(11) unsigned NOT NULL,
    `active` TINYINT(1) unsigned NOT NULL,
    PRIMARY KEY (`id_webixa_content_item`),
    INDEX (`id_webixa_content_block`, `position`),
    INDEX (`active`),
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_item_shop` (
    `id_webixa_content_item` INT(11) unsigned NOT NULL,
    `id_shop` INT(11) NOT NULL,
    PRIMARY KEY (`id_webixa_content_item`, `id_shop`),
    FOREIGN KEY (`id_webixa_content_item`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_item`(`id_webixa_content_item`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_shop`)
        REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_item_lang` (
    `id_webixa_content_item` INT(11) unsigned NOT NULL,
    `id_lang` INT(11) NOT NULL,
    `title` VARCHAR(128) NOT NULL DEFAULT "",
    `description` TEXT NOT NULL,
    PRIMARY KEY (`id_webixa_content_item`, `id_lang`),
    INDEX (`title`),
    FOREIGN KEY (`id_webixa_content_item`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_item`(`id_webixa_content_item`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_lang`)
        REFERENCES `' . _DB_PREFIX_ . 'lang`(`id_lang`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_slide` (
    `id_webixa_content_slide` INT(11) unsigned NOT NULL AUTO_INCREMENT,
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `position` INT(11) unsigned NOT NULL,
    `is_gradient` TINYINT(1) unsigned NOT NULL DEFAULT 0,
    `active` TINYINT(1) unsigned NOT NULL,
    PRIMARY KEY (`id_webixa_content_slide`),
    INDEX (`id_webixa_content_block`, `position`),
    INDEX (`active`),
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_slide_shop` (
    `id_webixa_content_slide` INT(11) unsigned NOT NULL,
    `id_shop` INT(11) NOT NULL,
    PRIMARY KEY (`id_webixa_content_slide`, `id_shop`),
    FOREIGN KEY (`id_webixa_content_slide`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_slide`(`id_webixa_content_slide`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_shop`)
        REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_slide_lang` (
    `id_webixa_content_slide` INT(11) unsigned NOT NULL,
    `id_lang` INT(11) NOT NULL,
    `title` VARCHAR(128) NOT NULL DEFAULT "",
    `description` TEXT NOT NULL,
    `button_text` VARCHAR(128) NOT NULL DEFAULT "",
    `link` VARCHAR(512) NOT NULL DEFAULT "",
    PRIMARY KEY (`id_webixa_content_slide`, `id_lang`),
    INDEX (`title`),
    FOREIGN KEY (`id_webixa_content_slide`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_slide`(`id_webixa_content_slide`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_lang`)
        REFERENCES `' . _DB_PREFIX_ . 'lang`(`id_lang`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_link` (
    `id_webixa_content_link` INT(11) unsigned NOT NULL AUTO_INCREMENT,
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `position` INT(11) unsigned NOT NULL,
    `active` TINYINT(1) unsigned NOT NULL,
    PRIMARY KEY (`id_webixa_content_link`),
    INDEX (`id_webixa_content_block`, `position`),
    INDEX (`active`),
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_link_shop` (
    `id_webixa_content_link` INT(11) unsigned NOT NULL,
    `id_shop` INT(11) NOT NULL,
    PRIMARY KEY (`id_webixa_content_link`, `id_shop`),
    FOREIGN KEY (`id_webixa_content_link`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_link`(`id_webixa_content_link`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_shop`)
        REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_link_lang` (
    `id_webixa_content_link` INT(11) unsigned NOT NULL,
    `id_lang` INT(11) NOT NULL,
    `title` VARCHAR(128) NOT NULL DEFAULT "",
    `description` TEXT NOT NULL,
    `link` VARCHAR(512) NOT NULL DEFAULT "",
    PRIMARY KEY (`id_webixa_content_link`, `id_lang`),
    INDEX (`title`),
    FOREIGN KEY (`id_webixa_content_link`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_link`(`id_webixa_content_link`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_lang`)
        REFERENCES `' . _DB_PREFIX_ . 'lang`(`id_lang`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_categoryproducts` (
    `id_webixa_content_categoryproducts` INT(11) unsigned NOT NULL AUTO_INCREMENT,
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `id_category` INT(11) unsigned NOT NULL,
    `id_group` INT(11) unsigned NULL DEFAULT NULL,
    `id_customer` INT(11) unsigned NULL DEFAULT NULL,
    `nbr_products` INT(11) unsigned NOT NULL DEFAULT "12",
    `search_subcategories` TINYINT(1) unsigned NOT NULL,
    `active` TINYINT(1) unsigned NOT NULL,
    PRIMARY KEY (`id_webixa_content_categoryproducts`),
    UNIQUE (`id_webixa_content_block`, `id_customer`),
    UNIQUE (`id_webixa_content_block`, `id_group`),
    INDEX (`id_category`),
    INDEX (`active`),
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_category`)
        REFERENCES `' . _DB_PREFIX_ . 'category`(`id_category`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_group`)
        REFERENCES `' . _DB_PREFIX_ . 'group`(`id_group`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_customer`)
        REFERENCES `' . _DB_PREFIX_ . 'customer`(`id_customer`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_categoryproducts_shop` (
    `id_webixa_content_categoryproducts` INT(11) unsigned NOT NULL,
    `id_shop` INT(11) NOT NULL,
    PRIMARY KEY (`id_webixa_content_categoryproducts`, `id_shop`),
    FOREIGN KEY (`id_webixa_content_categoryproducts`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_categoryproducts`(`id_webixa_content_categoryproducts`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_shop`)
        REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner` (
    `id_webixa_content_banner` INT(11) unsigned NOT NULL AUTO_INCREMENT,
    `id_webixa_content_block` INT(11) unsigned NOT NULL,
    `show_on_home` TINYINT(1) unsigned NOT NULL,
    `show_to_guest` TINYINT(1) unsigned NOT NULL,
    `show_to_logged` TINYINT(1) unsigned NOT NULL,
    `has_group_restrictions` TINYINT(1) unsigned NOT NULL,
    `has_category_restrictions` TINYINT(1) unsigned NOT NULL,
    `position` INT(11) unsigned NOT NULL,
    `active` TINYINT(1) unsigned NOT NULL,
    PRIMARY KEY (`id_webixa_content_banner`),
    INDEX (`id_webixa_content_block`, `position`),
    INDEX (`show_on_home`),
    INDEX (`show_to_guest`),
    INDEX (`show_to_logged`),
    INDEX (`has_group_restrictions`),
    INDEX (`has_category_restrictions`),
    INDEX (`active`),
    FOREIGN KEY (`id_webixa_content_block`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_group_restriction` (
    `id_webixa_content_banner` INT(11) unsigned NOT NULL,
    `id_group` INT(11) unsigned NOT NULL,
    PRIMARY KEY (`id_webixa_content_banner`, `id_group`),
    FOREIGN KEY (`id_webixa_content_banner`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_group`)
        REFERENCES `' . _DB_PREFIX_ . 'group`(`id_group`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_category_restriction` (
    `id_webixa_content_banner` INT(11) unsigned NOT NULL,
    `id_category` INT(11) unsigned NOT NULL,
    PRIMARY KEY (`id_webixa_content_banner`, `id_category`),
    FOREIGN KEY (`id_webixa_content_banner`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_category`)
        REFERENCES `' . _DB_PREFIX_ . 'category`(`id_category`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_shop` (
    `id_webixa_content_banner` INT(11) unsigned NOT NULL,
    `id_shop` INT(11) NOT NULL,
    PRIMARY KEY (`id_webixa_content_banner`, `id_shop`),
    FOREIGN KEY (`id_webixa_content_banner`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_shop`)
        REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_lang` (
    `id_webixa_content_banner` INT(11) unsigned NOT NULL,
    `id_lang` INT(11) NOT NULL,
    `alt` VARCHAR(128) NOT NULL DEFAULT "",
    `link` VARCHAR(512) NOT NULL DEFAULT "",
    PRIMARY KEY (`id_webixa_content_banner`, `id_lang`),
    INDEX (`alt`),
    FOREIGN KEY (`id_webixa_content_banner`)
        REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
        ON DELETE CASCADE,
    FOREIGN KEY (`id_lang`)
        REFERENCES `' . _DB_PREFIX_ . 'lang`(`id_lang`)
        ON DELETE CASCADE
) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        throw new Exception('Install DB failed');
    }
}

return true;
