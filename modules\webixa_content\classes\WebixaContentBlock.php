<?php

use Webixa\Content\Module\WebixaConfig;

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

class WebixaContentBlock extends ObjectModel
{
    public $active = true;
    public $name;
    public $type;
    public $slide_time;
    public $title;
    public $description;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'webixa_content_block',
        'primary' => 'id_webixa_content_block',
        'multilang' => true,
        'fields' => [
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'name' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 128, 'required' => true],
            'type' => ['type' => self::TYPE_STRING, 'validate' => 'isHookName', 'size' => 128, 'required' => true],
            'slide_time' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => false],

            /* Lang fields */
            'title' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml', 'size' => 128],
            'description' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml', 'size' => 3999999999999],
        ],
    ];

    public function delete()
    {
        if (!parent::delete()) {
            return false;
        }

        return WebixaContentHook::cleanPositions(false);
    }

    public static function getAll($idLang, $idShop)
    {
        $sql = '
            SELECT a.`' . static::$definition['primary'] . '`, a.name, al.`title`
            FROM `' . _DB_PREFIX_ . static::$definition['table'] . '` a
            ' .
            (
                !Shop::isFeatureActive() ? '' :
                '
            INNER JOIN `' . _DB_PREFIX_ . static::$definition['table'] . '_shop` sa
                ON (a.' . static::$definition['primary'] . '=sa.' . static::$definition['primary'] . ' AND sa.`id_shop`=' . (int)$idShop . ')
            '
            )
            . '
            LEFT JOIN `' . _DB_PREFIX_ . static::$definition['table'] . '_lang` al
                ON (a.' . static::$definition['primary'] . '=al.' . static::$definition['primary'] . ' AND al.`id_lang`=' . (int)$idLang . ')
            WHERE 1
        ';

        return Db::getInstance()->executeS($sql);
    }

    public static function getSkipAdminViewIds()
    {
        return array_column(
            Db::getInstance()->executeS('
                SELECT ' . static::$definition['primary'] . '
                FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`
                WHERE type IN ("' . WebixaConfig::BLOCK_TYPE_HTML . '")
            '),
            self::$definition['primary']
        );
    }

    public static function getActiveByWebixaContentHookId($idHook, $idLang = null, $idShop = null, $blockId = false)
    {
        $blocks = [];
        $query = new DbQuery();
        $query->select('a.' . static::$definition['primary']);
        $query->select('hb.template');
        $query->from(static::$definition['table'], 'a');
        $query->innerJoin(
            WebixaContentHook::$definition['table'] . '_block',
            'hb',
            'a.' . static::$definition['primary'] . '=' . 'hb.' . static::$definition['primary']
        );
        $query->where('a.active=' . 1);
        $query->where('hb.' . WebixaContentHook::$definition['primary'] . '=' . (int)$idHook);
        if ($blockId) {
            $query->where('a.' . static::$definition['primary'] . '=' . (int)$blockId);
        }
        if (null !== $idShop) {
            $query->innerJoin(
                static::$definition['table'] . '_shop',
                'sa',
                'a.' . static::$definition['primary'] . '=' . 'sa.' . static::$definition['primary'] . ' AND sa.id_shop=' . (int) $idShop
            );
        }
        if (null !== $idLang) {
            $query->innerJoin(
                static::$definition['table'] . '_lang',
                'al',
                'a.' . static::$definition['primary'] . '=' . 'al.' . static::$definition['primary'] . ' AND al.id_lang=' . (int) $idLang
            );
        }

        $query->orderBy('hb.position DESC');
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
        foreach ($result as $row) {
            $webixaContentBlock = new WebixaContentBlock($row[static::$definition['primary']], $idLang, $idShop);
            if (Validate::isLoadedObject($webixaContentBlock)) {
                $blocks[] = [
                    'template' => $row['template'],
                    'webixaContentBlock' => $webixaContentBlock
                ];
            }
        }

        return $blocks;
    }
}
