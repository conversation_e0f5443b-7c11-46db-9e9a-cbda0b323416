.mass-table-string.active
{
    width: 200px;
}

.mass-table-string
{
    width: 100px;
}

.image-show-content-block .mass-table-string.active,
.image-show-content-block .mass-table-string
{
    width: 125px;
}
.mass-multi-select
{
    position: relative;
    display: block;
    height: 200px;
    overflow: scroll;
    width: 300px;
    background-color: #FFF;
    padding: 10px;
}


.fields-mask
{
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    background-color: #000;
    z-index: 400;
    top: 0;
    opacity: 0.5;
}

.fields-loading
{
    display: none;
    position: absolute;
    width: 100%;
    text-align: center;
    height: 20px;
    z-index: 400;
    top: 40%;
    color: #FFF;
    font-size: 80px;
}

.fields-panel
{
    width: 100%;
    position: relative;
    overflow: hidden;
}

.mass-table-text-container{
	padding-right: 30px;
    position: relative;
}
.mass-table-text-container .edit, 
.mass-table-text-container .save {
	width: 20px;
    position: absolute;
    top: 0;
    right: 0;
    height: 20px;
    line-height: 20px;
	padding:0 !important;
	background: #fff;
	border-radius:5px;
} 
.mass-table-text-container .edit i, 
.mass-table-text-container .save i{
	line-height:20px;
}