/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function() {
    var $table = $('#products_list');
    var $fields_panel = $('.fields-panel');
    var $scope = $('#massupdateproducts-fields');
    var $button_on = $scope.find('.show_checkbox');
    var $button_off = $scope.find('.hide_checkbox');
    var $save_fields = $scope.find('.save-fields');
    var $mask = $scope.find('.fields-mask');
    var $fields_loading = $scope.find('.fields-loading');

    $button_on.on('click', function() {
	$button_on.addClass('hidden');
	$button_off.removeClass('hidden');
	$fields_panel.removeClass('hidden');
    });

    $button_off.on('click', function() {
	$button_off.addClass('hidden');
	$button_on.removeClass('hidden');
	$fields_panel.addClass('hidden');
    });

    $table.on('focus', '.mass-table-string', function() {
	var $scope = $(this);
	var $handler = $scope.closest('.field_handler');
	var $flag = $handler.find('.field_handler_flag');
	$scope.data('zindex', $scope.css('z-index')).css({
	    'z-index': 205
	});
	$flag.data('zindex', $flag.css('z-index')).css({
	    'z-index': 206
	});

	$(this).addClass('active');
    });

    $table.on('blur', '.mass-table-string', function() {
	var $scope = $(this);
	var $handler = $scope.closest('.field_handler');
	var $flag = $handler.find('.field_handler_flag');
	$scope.css({
	    'z-index': $scope.data('zindex')
	}).data('zindex', $scope.css('z-index'));
	$flag.css({
	    'z-index': $flag.data('zindex')
	}).data('zindex', $flag.css('z-index'));
	$(this).removeClass('active');
    });

    $fields_panel.sortable({
	stop: function(event, ui) {
	    var $i = 0;
	    $fields_panel.find('.field_position').each(function() {
		$(this).val($i++);
	    });
	}
    });

    $save_fields.on('click', function() {
	if (massProcess())
	    return false;

	$massupdateproductsProcess = true;

	var $fields = {};

	$fields_panel.find('.field').each(function() {
	    var $field = $(this);
	    $fields[$field.attr('name')] = {
		view: $field.prop('checked') ? 1 : 0,
		position: $field.next('.field_position').val()
	    };
	});
	$mask.show();
	$fields_loading.show();
	$.ajax({
	    data: {
		is_ajax: true,
		save_fields: true,
		fields: $fields
	    },
	    type: 'POST',
	    dataType: 'json',
	    success: function($response) {
		$.notify($response.message, (!$response.error ? 'success' : 'error'), {
		    autoHideDelay: 2000
		});

		if (!$response.error)
		    window.location.reload();
	    },
	    complete: function() {
		$massupdateproductsProcess = false;
		$mask.hide();
		$fields_loading.hide();
	    }
	});
    });
});