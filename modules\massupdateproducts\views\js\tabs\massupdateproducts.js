/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function () {
    var $products_list_body = $('#products_list').find('.main_body');
    var $products_list_head = $('#products_list').find('.main_head');

    $products_list_body.on('change', '.to-send[send-name="price"]', function () {
        var $netto_el = $(this);
        var $brutto_el = $netto_el.closest('.element-row').find('.to-send[send-name="price_brutto"]');
        var $p_id = $netto_el.closest('.element-row').attr('p-id');
        //var $tax_el = ;
        var $rate = parseFloat($products_list_body.find('.product-row[p-id="' + $p_id + '"]').data('tax'));//$tax_el.find('option:selected').attr('extra') ? parseFloat($tax_el.find('option:selected').attr('extra')) : 0;
        var $netto = $netto_el.val() ? parseFloat($netto_el.val()) : 0;
        $brutto_el.val(($netto + ($netto * ($rate / 100))).toFixed(2));
    });

    $products_list_body.on('change', '.to-send[send-name="price_brutto"]', function () {
        var $brutto_el = $(this);
        var $netto_el = $brutto_el.closest('.element-row').find('.to-send[send-name="price"]');
        var $p_id = $brutto_el.closest('.element-row').attr('p-id');
        //var $tax_el = ;
        var $rate = parseFloat($products_list_body.find('.product-row[p-id="' + $p_id + '"]').data('tax'));//$tax_el.find('option:selected').attr('extra') ? parseFloat($tax_el.find('option:selected').attr('extra')) : 0;
        var $brutto = $brutto_el.val() ? parseFloat($brutto_el.val()) : 0;
        $netto_el.val((($brutto * 100) / (100 + $rate)).toFixed(6));
    });

    $products_list_body.on('change', '.to-send[send-name="tax"]', function () {
        var $tax_handle = $(this);
        var $p_id = $tax_handle.closest('.product-row').attr('p-id');
        var $extra = $tax_handle.find('option[value="' + $tax_handle.val() + '"]').attr('extra');
        $tax_handle.closest('.product-row').data('tax', $extra ? $extra : 0);
        $products_list_body.find('.product-row[p-id="' + $p_id + '"] [send-name="price"],.combination-row[p-id="' + $p_id + '"] [send-name="price"]').each(function () {
            $(this).trigger('change');
        });
    });

    $products_list_body.on('change', '.to-send[send-name="quantity"]', function () {
        var $scope = $(this);
        var $tr = $scope.closest('tr');
        if ($tr.hasClass('combination-row'))
        {
            var $id = parseInt($tr.attr('p-id'));
            var $sum = 0;
            var $tr_product = $products_list_body.find('.product-row[p-id="' + $id + '"]');
            $products_list_body.find('.combination-row[p-id="' + $id + '"]').each(function () {
                var $quantity_el = $(this).find('.to-send[send-name="quantity"]');
                var $quantity = $quantity_el.val() ? parseInt($quantity_el.val()) : 0;
                $sum += $quantity;
            });

            $tr_product.find('.to-send[send-name="quantity"]').val($sum).trigger('change');
        }
    });
});