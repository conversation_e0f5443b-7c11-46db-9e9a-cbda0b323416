{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}
<div style="display: block;position: relative;overflow: hidden;text-align: left;">
    <select {$attr|strval} style="width: 100px;" class="{$class_mass|strval}" send-name="{$name|strval}">
        {if $select}
            {foreach $select as $id => $option}
                <option {if isset($extra[$id])} extra="{$extra[$id]|strval}" {/if} value="{$id|strval}" {if $value eq $id} selected {/if}>{$option|strval}</option>
            {/foreach}
        {/if}
    </select>
    <div style="display: block; position: relative; overflow: hidden;text-align: left;">
        {l s='New feature' mod='massupdateproducts'}
        {foreach $languages as $language}
            <span style="position: relative;display: block;height: 23px;">
                {include file="./field_0.tpl" select=$select name=$name value='' lang=$language['iso_code'] extra=$extra class_mass=$class_mass validate=$validate attr=$attr}
                <span style="position: absolute;top: 4px;left: 3px;z-index:201;">
                    <img title="{$language['name']|strval}" style="width: 16px; height: 10px;" src="{$img_lang_src|strval}{$language['id_lang']|intval}.jpg" />
                </span>
            </span>
        {/foreach}
    </div>
</div>