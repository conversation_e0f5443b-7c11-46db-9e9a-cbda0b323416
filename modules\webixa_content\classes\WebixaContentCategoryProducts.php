<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

class WebixaContentCategoryProducts extends ObjectModel
{
    public $id_webixa_content_block;
    public $id_category;
    public $id_group;
    public $id_customer;
    public $nbr_products = 12;
    public $search_subcategories = false;
    public $active = true;

    const _TYPE_IMG_DIR_ = 'img/categorys';
    const _IMG_DIR_ = _PS_MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    const _IMG_PATH_ = _MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    const _IMG_DIR_MOBILE_ = _PS_MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/mobile_';
    const _IMG_PATH_MOBILE_ = _MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/mobile_';
    public $image_dir = self::_IMG_DIR_;
    public $image_dir_mobile = self::_IMG_DIR_MOBILE_;

    public $image_format;
    public $image_format_mobile;



    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'webixa_content_categoryproducts',
        'primary' => 'id_webixa_content_categoryproducts',
        'multilang' => false,
        'fields' => [
            'id_webixa_content_block' => ['type' => self::TYPE_INT, 'validate' => 'isInt', 'required' => true],
            'id_category' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true],
            'id_group' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => false, 'allow_null' => true],
            'id_customer' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => false, 'allow_null' => true],
            'nbr_products' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true],
            'search_subcategories' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null, $translator = null)
    {
        if (version_compare(_PS_VERSION_, '1.7.1', '<')) {
            parent::__construct($id, $id_lang, $id_shop);
        } else {
            parent::__construct($id, $id_lang, $id_shop, $translator);
        }

        $this->image_format = $this->getImageExtensionInDir($this->id, self::_IMG_DIR_);

        if (file_exists(self::_IMG_DIR_ . $this->id . '.' . $this->image_format)) {
            $this->image = self::_IMG_PATH_ . $this->id . '.' . $this->image_format;
        }

        $this->image_format_mobile = $this->getImageExtensionInDir($this->id, self::_IMG_DIR_, 'mobile_');

        if (file_exists(self::_IMG_DIR_MOBILE_ . $this->id . '.' . $this->image_format_mobile)) {
            $this->image_mobile = self::_IMG_PATH_MOBILE_ . $this->id . '.' . $this->image_format_mobile;
        }
    }

    public function getImageExtensionInDir($id, $imageDir, $prefix = '')
    {
        $image = false;

        if (is_dir($imageDir)) {
            $filesInFolder = scandir($imageDir);

            foreach ($filesInFolder as $file) {
                $fileNameParts = explode('.', $file);
                $fileName = reset($fileNameParts);

                if ($fileName === ($prefix ?: '') .$id) {
                    $image = $imageDir . $file;
                    break;
                }
            }

            $imageParts = explode('.', $image);
            return end($imageParts);
        }

        return false;
    }

    public function add($auto_date = true, $null_values = false)
    {
        if (empty($this->id_customer)) {
            $this->id_customer = NULL;
        }
        if (empty($this->id_group)) {
            $this->id_group = NULL;
        }
        return parent::add($auto_date, true);
    }

    public function update($null_values = false)
    {
        if (empty($this->id_customer)) {
            $this->id_customer = NULL;
        }
        if (empty($this->id_group)) {
            $this->id_group = NULL;
        }
        return parent::update(true);
    }

    public static function getBlockConfigurationByContext($idBlock, $context)
    {
        $idShop = $context->shop->id;
        $idCustomer = (int) $context->customer->id;
        $idGroup = (int) Group::getCurrent()->id;

        $query = new DbQuery();
        $query->select('a.' . static::$definition['primary']);
        $query->from(static::$definition['table'], 'a');
        $query->where('a.active=' . 1);
        $query->where('a.' . WebixaContentBlock::$definition['primary'] . '=' . (int)$idBlock);
        $query->where('a.id_customer IS NULL OR a.id_customer=' . (int)$idCustomer);
        $query->where('a.id_group IS NULL OR a.id_group=' . (int)$idGroup);

        if (null !== $idShop) {
            $query->innerJoin(
                static::$definition['table'] . '_shop',
                'sa',
                'a.' . static::$definition['primary'] . '=' . 'sa.' . static::$definition['primary'] . ' AND sa.id_shop=' . (int) $idShop
            );
        }

        $query->orderBy('a.id_customer DESC, a.id_group DESC');
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($query);

        if (!$result) {
            return false;
        }

        return new WebixaContentCategoryProducts($result[static::$definition['primary']], $context->language->id, $idShop);
    }

    public function deleteImage($force_delete = false, $type = 'all')
    {
        if ($force_delete || !$this->hasMultishopEntries()) {
            if ('image_mobile' !== $type) {
                foreach (Shop::getShops(false, null, true) as $id_shop) {
                    if (
                        file_exists(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                        && !unlink(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                    ) {
                        return false;
                    }
                }
            }
            if (in_array($type, ['all', 'image_mobile'])) {
                if (
                    file_exists(self::_IMG_DIR_MOBILE_ . $this->id . '.' . $this->image_format_mobile)
                    && !unlink(self::_IMG_DIR_MOBILE_ . $this->id . '.' . $this->image_format_mobile)
                ) {
                    return false;
                }
                if (
                    file_exists(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mobile_' . (int) $this->id . '.' . $this->image_format_mobile)
                    && !unlink(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mobile_' . (int) $this->id . '.' . $this->image_format_mobile)
                ) {
                    return false;
                }
            }
        }
        if ('image_mobile' === $type) {
            return true;
        }
        return parent::deleteImage($force_delete);
    }
}
