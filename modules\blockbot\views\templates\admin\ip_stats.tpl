<div class="panel">
    <div class="panel-heading">
        <i class="icon-shield"></i>
        {l s='Block Bot Management' mod='blockbot'}
    </div>

    <!-- Navigation Tabs -->
    <div class="nav-tabs-container" style="margin-bottom: 20px;">
        <ul class="nav nav-tabs" role="tablist">
            <li class="active" style="margin-right: 5px;">
                <a href="{$admin_url}&tab=ip_stats" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-list"></i>
                    {l s='IP Statistics' mod='blockbot'}
                </a>
            </li>
            <li style="margin-right: 5px;">
                <a href="{$admin_url}&tab=useragent_stats" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-user"></i>
                    {l s='User Agent Statistics' mod='blockbot'}
                </a>
            </li>
            <li>
                <a href="{$admin_url}&tab=settings" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-cogs"></i>
                    {l s='Settings' mod='blockbot'}
                </a>
            </li>
        </ul>
    </div>

    <div class="panel-body">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="icon-list"></i>
                    {l s='IP Address Statistics' mod='blockbot'}
                </h3>
            </div>
            <div class="panel-body">
                <div class="alert alert-info">
                    <p><strong>{l s='Threshold for automatic blocking:' mod='blockbot'}</strong> {$threshold_ip} {l s='visits' mod='blockbot'}</p>
                </div>

                <!-- Clear Log Button -->
                <div class="row" style="margin-bottom: 20px;">
                    <div class="col-md-12">
                        <form method="post" action="{$admin_url}&tab=ip_stats" onsubmit="return confirm('{l s='Are you sure you want to clear all logs?' mod='blockbot'}');" style="display: inline-block;">
                            <button type="submit" name="clearLog" class="btn btn-danger">
                                <i class="icon-trash"></i>
                                {l s='Clear All Logs' mod='blockbot'}
                            </button>
                        </form>
                    </div>
                </div>

        {if $blocked_ips}
        <div class="alert alert-info">
            <h4>{l s='Currently Blocked IPs:' mod='blockbot'}</h4>
            {foreach $blocked_ips as $blocked_ip}
                <span class="label label-danger" style="margin-right: 5px;">
                    {$blocked_ip}
                    <a href="{$admin_url}&tab=ip_stats&unblockIp=1&ip={$blocked_ip}" 
                       onclick="return confirm('{l s='Are you sure you want to unblock this IP?' mod='blockbot'}')"
                       style="color: white; margin-left: 5px;">
                        <i class="icon-remove"></i>
                    </a>
                </span>
            {/foreach}
        </div>
        {/if}

        <form method="post" action="{$admin_url}&tab=ip_stats" id="blockIpsForm">
            <div class="row" style="margin-bottom: 15px;">
                <div class="col-md-12 text-right">
                    <div class="btn-group">
                        <button class="btn btn-outline-secondary dropdown-toggle js-bulk-actions-btn" data-toggle="dropdown" aria-expanded="false" id="bulkActionsBtn" disabled>
                            {l s='Bulk actions' mod='blockbot'}
                            <i class="icon-caret-down"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a href="#" onclick="selectAllIps(); return false;">
                                    <i class="icon-check"></i>
                                    {l s='Select All' mod='blockbot'}
                                </a>
                            </li>
                            <li>
                                <a href="#" onclick="deselectAllIps(); return false;">
                                    <i class="icon-remove"></i>
                                    {l s='Deselect All' mod='blockbot'}
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="#" onclick="submitBulkAction('blockSelectedIps'); return false;">
                                    <i class="icon-ban-circle"></i>
                                    {l s='Block selected IPs' mod='blockbot'}
                                </a>
                            </li>
                            <li>
                                <a href="#" onclick="submitBulkAction('deleteSelectedIpLogs'); return false;">
                                    <i class="icon-trash"></i>
                                    {l s='Delete logs for selected IPs' mod='blockbot'}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAllCheckbox">
                            </th>
                            <th>{l s='IP Address' mod='blockbot'}</th>
                            <th>{l s='Visit Count' mod='blockbot'}</th>
                            <th>{l s='Last Visit' mod='blockbot'}</th>
                            <th>{l s='User Agents' mod='blockbot'}</th>
                            <th>{l s='Actions' mod='blockbot'}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $ip_stats as $stat}
                        <tr {if $stat.count >= $threshold_ip}class="danger"{/if}>
                            <td>
                                <input type="checkbox" name="selected_ips[]" value="{$stat.ip}" class="ip-checkbox">
                            </td>
                            <td>
                                <strong>{$stat.ip}</strong>
                                {if $stat.count >= $threshold_ip}
                                    <span class="label label-danger">{l s='Suspicious' mod='blockbot'}</span>
                                {/if}
                            </td>
                            <td>
                                <span class="badge {if $stat.count >= $threshold_ip}badge-danger{else}badge-info{/if}">
                                    {$stat.count}
                                </span>
                            </td>
                            <td>{$stat.last_visit}</td>
                            <td>
                                <div style="max-height: 100px; overflow-y: hidden; overflow-x: hidden;">
                                    {foreach $stat.user_agents as $agent}
                                        <div style="font-size: 11px; margin-bottom: 2px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                                            {$agent|truncate:60:"..."}
                                        </div>
                                    {/foreach}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group-action">
                                    <div class="btn-group pull-right">
                                        <button class="btn btn-xs btn-default dropdown-toggle" type="button" data-toggle="dropdown" style="padding: 6px 12px;">
                                            {l s='Actions' mod='blockbot'}
                                            <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            {if !in_array($stat.ip, $blocked_ips)}
                                            <li>
                                                <a href="{$admin_url}&tab=ip_stats&action=block&ip={$stat.ip}"
                                                   onclick="return confirm('{l s='Are you sure you want to block this IP?' mod='blockbot'}');">
                                                    <i class="icon-ban-circle"></i>
                                                    {l s='Block IP' mod='blockbot'}
                                                </a>
                                            </li>
                                            {else}
                                            <li class="disabled">
                                                <a href="#"><i class="icon-ban-circle"></i> {l s='Already Blocked' mod='blockbot'}</a>
                                            </li>
                                            {/if}
                                            <li>
                                                <a href="{$admin_url}&tab=ip_stats&action=delete&ip={$stat.ip}"
                                                   onclick="return confirm('{l s='Are you sure you want to delete all logs for this IP address?' mod='blockbot'}');">
                                                    <i class="icon-trash"></i>
                                                    {l s='Delete Logs' mod='blockbot'}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {foreachelse}
                        <tr>
                            <td colspan="6" class="text-center">
                                <em>{l s='No data available' mod='blockbot'}</em>
                            </td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
        </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const ipCheckboxes = document.querySelectorAll('.ip-checkbox');
    const selectAllBtn = document.getElementById('selectAllIps');
    const deselectAllBtn = document.getElementById('deselectAllIps');
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');

    function updateBulkActionsButton() {
        const checkedBoxes = document.querySelectorAll('.ip-checkbox:checked');
        if (bulkActionsBtn) {
            bulkActionsBtn.disabled = checkedBoxes.length === 0;
        }
    }

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            ipCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsButton();
        });
    }

    ipCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionsButton);
    });



    // Initial check
    updateBulkActionsButton();
});

function selectAllIps() {
    const ipCheckboxes = document.querySelectorAll('.ip-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    ipCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    if (selectAllCheckbox) selectAllCheckbox.checked = true;

    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    if (bulkActionsBtn) bulkActionsBtn.disabled = false;
}

function deselectAllIps() {
    const ipCheckboxes = document.querySelectorAll('.ip-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    ipCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    if (selectAllCheckbox) selectAllCheckbox.checked = false;

    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    if (bulkActionsBtn) bulkActionsBtn.disabled = true;
}

function submitBulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.ip-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('{l s='Please select at least one IP address.' mod='blockbot'}');
        return false;
    }

    let confirmMessage = '';
    if (action === 'blockSelectedIps') {
        confirmMessage = '{l s='Are you sure you want to block selected IPs?' mod='blockbot'}';
    } else if (action === 'deleteSelectedIpLogs') {
        confirmMessage = '{l s='Are you sure you want to delete logs for selected IPs?' mod='blockbot'}';
    }

    if (confirm(confirmMessage)) {
        const form = document.getElementById('blockIpsForm');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = action;
        input.value = '1';
        form.appendChild(input);
        form.submit();
    }
}
</script>
