<?php

class AdminWebixaContentLinkController extends ModuleAdminController
{
    public $bootstrap = true;
    protected $position_identifier;
    /** @var WebixaContentBlock */
    protected $webixaContentBlock;

    public function __construct()
    {
        $this->bootstrap = true;
        parent::__construct();

        if (version_compare(_PS_VERSION_, '1.7', '>=')) {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
            $this->tabAccess['view'] = Module::getPermissionStatic($this->module->id, 'view');

            $configAccess = Module::getPermissionStatic($this->module->id, 'configure');
            $this->tabAccess['add'] = $configAccess;
            $this->tabAccess['edit'] = $configAccess;
            $this->tabAccess['delete'] = $configAccess;
        } else {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
        }
    }

    public function initProcess()
    {
        $this->initList();

        parent::initProcess();
    }

    public function initList()
    {
        $this->fieldImageSettings = [
            'name' => 'image',
            'dir' => '../modules/' . $this->module->name . '/' . WebixaContentLink::_TYPE_IMG_DIR_,
        ];

        $this->table = WebixaContentLink::$definition['table'];
        $this->className = 'WebixaContentLink';
        $this->identifier = WebixaContentLink::$definition['primary'];
        $this->position_identifier = $this->identifier;
        $this->position_group_identifier = WebixaContentBlock::$definition['primary'];
        $this->_defaultOrderBy = 'position';
        $this->_defaultOrderWay = 'ASC';

        $this->lang = true;
        if (Shop::isFeatureActive()) {
            Shop::addTableAssociation($this->table, ['type' => 'shop']);
        }
        $this->webixaContentBlock = new WebixaContentBlock((int)Tools::getValue($this->position_group_identifier));

        if (!Validate::isLoadedObject($this->webixaContentBlock)) {
            Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentBlock'));
        }

        self::$currentIndex .= '&' . $this->position_group_identifier . '=' . (int)$this->webixaContentBlock->id;

        $this->_select = 'a.' . $this->identifier . ' as image';
        $this->_where = 'AND ' . $this->position_group_identifier . '=' . (int)$this->webixaContentBlock->id;

        $this->fields_list = [
            $this->identifier => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
            ],
            'image' => [
                'title' => $this->l('Image'),
                'orderby' => false,
                'search' => false,
                'align' => 'center',
                'callback' => 'displayImageDefaultOnList',
            ],
            'title' => [
                'title' => $this->l('Title'),
                'filter_key' => 'b!title',
                'maxlength' => 50,
            ],
            'description' => [
                'title' => $this->l('Description'),
                'filter_key' => 'b!description',
                'maxlength' => 50,
            ],
            'link' => [
                'title' => $this->l('Link'),
                'filter_key' => 'b!link',
                'maxlength' => 50,
            ],
            'position' => [
                'title' => $this->l('Position'),
                'filter_key' => 'a!position',
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'position' => 'position',
            ],
            'active' => [
                'title' => $this->l('Displayed'),
                'align' => 'center',
                'active' => 'status',
                'class' => 'fixed-width-sm',
                'ajax' => true,
                'type' => 'bool',
                'orderby' => false,
            ],
        ];

        $this->addRowAction('edit');
        $this->addRowAction('delete');

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected links?'),
            ],
        ];
    }

    public function renderForm()
    {
        /** @var WebixaContentLink $obj */
        if (!($obj = $this->loadObject(true))) {
            return;
        }
        $image = WebixaContentLink::_IMG_DIR_ . $obj->id . '.' . $this->imageType;

        $image_url = ImageManager::thumbnail(
            $image,
            $this->table . '_' . (int) $obj->id . '.' . $this->imageType,
            350,
            $this->imageType,
            true,
            true
        );
        $image_size = file_exists($image) ? filesize($image) / 1000 : false;
        $image_delete_url = file_exists($image) ? self::$currentIndex . '&' . $this->identifier . '=' . (int) $this->object->id . '&action=deleteImage&token=' . $this->token : false;

        $this->show_form_cancel_button = false;
        $this->fields_form = [
            'tinymce' => true,
            'legend' => [
                'title' => Validate::isLoadedObject($obj) ? $this->l('Update content Link') : $this->l('Add content Link'),
                'icon' => 'icon-cogs',
            ],
            'input' => [
                [
                    'type' => 'file',
                    'label' => $this->l('Image'),
                    'name' => 'image',
                    'image' => $image_url ? $image_url : false,
                    'size' => $image_size,
                    'delete_url' => $image_delete_url,
                    'display_image' => true,
                    'col' => 6,
                    'hint' => $this->l('Upload image from your computer.'),
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Title'),
                    'name' => 'title',
                    'lang' => true,
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'autoload_rte' => true,
                    'lang' => true,
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Link'),
                    'name' => 'link',
                    'lang' => true,
                ],
                [
                    'type' => 'hidden',
                    'name' => 'id_webixa_content_block',
                    'value' => $this->webixaContentBlock->id,
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Displayed'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
            ],
            'buttons' => [
                [
                    'title' => $this->l('Cancel'),
                    'id' => $this->table . '_form_cancel_btn',
                    'href' => self::$currentIndex . '&token=' . $this->token,
                    'icon' => 'process-icon-cancel',
                ],
            ],
        ];
        if (Shop::isFeatureActive()) {
            $this->fields_form['input'][] = [
                'type' => 'shop',
                'label' => $this->l('Shop association:'),
                'name' => 'checkBoxShopAsso',
            ];
        }

        return parent::renderForm();
    }

    public function renderList()
    {
        return $this->module->prepareAdminInfoBanner() . parent::renderList();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Links in') . ' ' . $this->webixaContentBlock->title[$this->context->language->id];
    }

    public function initToolbar()
    {
        parent::initToolbar();

        if ('edit' == $this->display || 'add' == $this->display) {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => self::$currentIndex . '&token=' . $this->token,
                'desc' => $this->l('Back'),
                'icon' => 'process-icon-back',
            ];
        } else {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => $this->context->link->getAdminLink('AdminWebixaContentBlock'),
                'desc' => $this->l('Back to Blocks'),
                'icon' => 'process-icon-back',
            ];
        }
    }

    public function setMedia($isNewTheme = false)
    {
        if (!$this->module->active && version_compare(_PS_VERSION_, '8.0.2', '>=')) {
            $this->module->hookDisplayBackOfficeHeader([]);
        }
        parent::setMedia($isNewTheme);
        $this->addJqueryUI([
            'ui.core',
        ]);
        $this->addJqueryPlugin('tablednd');
        if ($isNewTheme) {
            $this->registerJavascript('dnd', _PS_JS_DIR_ . 'admin/dnd.js', ['position' => 'bottom', 'priority' => 150]);
        } else {
            $this->addJS(_PS_JS_DIR_ . 'admin/dnd.js');
        }
    }

    public function postProcess()
    {
        $ret = parent::postProcess();
        if (!$this->ajax) {
            if (!empty($this->action) && method_exists($this, 'process' . ucfirst(Tools::toCamelCase($this->action)))) {
                $this->module->clearModuleCache('*');
            }
        }

        return $ret;
    }

    protected function postImage($id)
    {
        parent::postImage($id);

        return !count($this->errors) ? true : false;
    }

    protected function uploadImage($id, $name, $dir, $ext = false, $width = null, $height = null)
    {
        if (isset($_FILES[$name]['error']) && !in_array($_FILES[$name]['error'], [UPLOAD_ERR_OK, UPLOAD_ERR_NO_FILE])) {
            $this->errors[] = $this->module->codeToMessage($_FILES[$name]['error']);
            return false;
        }

        if (isset($_FILES[$name]['tmp_name']) && !empty($_FILES[$name]['tmp_name'])) {
            // Delete old image
            if (Validate::isLoadedObject($object = $this->loadObject())) {
                $object->deleteImage();
            } else {
                return false;
            }

            // Check image validity
            $max_size = isset($this->max_image_size) ? $this->max_image_size : 0;
            if ($error = ImageManager::validateUpload($_FILES[$name], Tools::getMaxUploadSize($max_size))) {
                $this->errors[] = $error;
            }

            if (!move_uploaded_file($_FILES[$name]['tmp_name'], _PS_IMG_DIR_ . $dir . $id . '.' . $this->imageType)) {
                $this->errors[] = $this->trans('An error occurred while uploading the image.', [], 'Admin.Notifications.Error');
            }


            if (count($this->errors)) {
                return false;
            }

            return $this->afterImageUpload();
        }

        return true;
    }

    public function processDeleteImage()
    {
        $field = Tools::getValue('field');
        $object = $this->loadObject();
        if (!Validate::isLoadedObject($object) || !$object->deleteImage(true, $field)) {
            $this->errors[] = $this->l('Unable to delete image');
        } else {
            $this->confirmations[] = $this->l('Image successfully deleted');
        }

        $this->module->clearModuleCache('*');

        Tools::redirectAdmin(
            self::$currentIndex.'&'.$this->identifier.'='.$object->id.'&update'.$this->table.'&token='.$this->token
        );
    }

    public function ajaxProcessStatusWebixaContentLink()
    {
        $id_object = (int) Tools::getValue($this->identifier);

        $sql = 'UPDATE ' . _DB_PREFIX_ . $this->table . ' SET `active`= NOT `active` WHERE ' . $this->identifier . '=' . $id_object;
        $result = Db::getInstance()->execute($sql);

        if ($result) {
            $this->module->clearModuleCache('*');
            $response = json_encode(['success' => 1, 'text' => $this->l('The status has been updated successfully.')]);
        } else {
            $response = json_encode(['success' => 0, 'text' => $this->l('An error occurred while updating the status.')]);
        }
        $this->ajaxDie($response);
    }

    public function ajaxProcessUpdatePositions()
    {
        if (
            !isset($this->position_identifier) ||
            !($positions = Tools::getValue($this->table, false)) ||
            !WebixaContentLink::updatePositions($positions)
        ) {
            $this->ajaxDie(
                json_encode(
                    [
                        'hasError' => true,
                        'errors' => $this->l('Update position failed'),
                    ]
                )
            );
        }
        $this->module->clearModuleCache('*');
        $this->ajaxDie(
            json_encode(
                [
                    'success' => true,
                ]
            )
        );
    }

    protected function ajaxDie($value = null, $controller = null, $method = null)
    {
        if (ob_get_length() > 0) {
            ob_end_clean();
        }
        header('Content-Type: application/json');

        if (version_compare(_PS_VERSION_, '1.6.1', '>=')) {
            return parent::ajaxDie($value, $controller, $method);
        }

        header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        echo $value;
        exit;
    }

    public static function displayImageDefaultOnList($id, $row = null)
    {
        $img_path = WebixaContentLink::_IMG_DIR_ . $id . '.jpg';

        $path_to_image = false;
        if (file_exists($img_path)) {
            $path_to_image = $img_path;
        }

        $context = Context::getContext();
        return ImageManager::thumbnail($path_to_image, WebixaContentLink::$definition['table'] . '_mini_' . $id . '_' . $context->shop->id . '.jpg', 45, 'jpg');
    }
}
