<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitwebixa_content
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'Webixa\\Content\\' => 15,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Webixa\\Content\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'WebixaContentBanner' => __DIR__ . '/../..' . '/classes/WebixaContentBanner.php',
        'WebixaContentBlock' => __DIR__ . '/../..' . '/classes/WebixaContentBlock.php',
        'WebixaContentCategoryProducts' => __DIR__ . '/../..' . '/classes/WebixaContentCategoryProducts.php',
        'WebixaContentHook' => __DIR__ . '/../..' . '/classes/WebixaContentHook.php',
        'WebixaContentItem' => __DIR__ . '/../..' . '/classes/WebixaContentItem.php',
        'WebixaContentLink' => __DIR__ . '/../..' . '/classes/WebixaContentLink.php',
        'WebixaContentSlide' => __DIR__ . '/../..' . '/classes/WebixaContentSlide.php',
        'Webixa\\Content\\Module\\WebixaConfig' => __DIR__ . '/../..' . '/src/Module/WebixaConfig.php',
        'Webixa\\Content\\Module\\WidgetInterface' => __DIR__ . '/../..' . '/src/Module/WidgetInterface.php',
        'Webixa\\Content\\Search\\CategoryProductQuery' => __DIR__ . '/../..' . '/src/Search/CategoryProductQuery.php',
        'Webixa\\Content\\Search\\CategoryProductSearchProvider' => __DIR__ . '/../..' . '/src/Search/CategoryProductSearchProvider.php',
        'Webixa_Content' => __DIR__ . '/../..' . '/webixa_content.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitwebixa_content::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitwebixa_content::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitwebixa_content::$classMap;

        }, null, ClassLoader::class);
    }
}
