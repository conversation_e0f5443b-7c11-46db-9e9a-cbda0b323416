/**
 * Inspiration admin CSS
 */

/* Product list */
#product-list {
    margin-top: 15px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f8f8f8;
}

.product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    margin-bottom: 5px;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 3px;
}

.product-item:hover {
    background: #f5f5f5;
}

.product-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.product-name {
    flex: 1;
    margin-right: 10px;
}

.remove-product {
    margin-left: 5px;
}

/* Image management */
.image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.image-item {
    position: relative;
    width: 150px;
    border: 1px solid #ddd;
    padding: 5px;
    background: #fff;
    border-radius: 3px;
}

.image-item.main-image {
    border: 2px solid #25b9d7;
}

.image-item img {
    width: 100%;
    height: auto;
    display: block;
}

.image-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}

.main-image-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #25b9d7;
    color: #fff;
    padding: 2px 5px;
    font-size: 10px;
    border-radius: 3px;
}

/* Positioning modal */
.image-container {
    position: relative;
    margin-bottom: 15px;
    cursor: crosshair;
}

.product-marker {
    position: absolute;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transform: translate(-50%, -50%);
    cursor: move;
}

/* Form improvements */
.inspiration-form .panel {
    padding: 20px;
}

.inspiration-form .form-group {
    margin-bottom: 20px;
}

.inspiration-form .form-control {
    max-width: 100%;
}

.inspiration-form .help-block {
    margin-top: 5px;
    color: #999;
}

/* Category tree */
.category-tree {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f8f8f8;
}

.category-item {
    margin-bottom: 5px;
}

.subcategory-list {
    margin-left: 20px;
    margin-top: 5px;
}

/* Image upload */
.image-upload-container {
    margin-top: 15px;
}

.image-upload-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.preview-item {
    position: relative;
    width: 100px;
    height: 100px;
    border: 1px solid #ddd;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.remove-preview {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .image-item {
        width: calc(50% - 10px);
    }
}

@media (max-width: 480px) {
    .image-item {
        width: 100%;
    }
}
