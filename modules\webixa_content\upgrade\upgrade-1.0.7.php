<?php

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_1_0_7($module)
{
    $sql = [];

    $sql[] = 'ALTER TABLE ' . _DB_PREFIX_ . 'webixa_content_slide ADD `is_gradient` TINYINT(1) unsigned NOT NULL DEFAULT 0 AFTER `position`';
    $sql[] = 'ALTER TABLE ' . _DB_PREFIX_ . 'webixa_content_block ADD `slide_time` SMALLINT(5) unsigned NOT NULL DEFAULT 0';

    foreach ($sql as $query) {
        if (Db::getInstance()->execute($query) == false) {
            throw new Exception('Install DB failed');
        }
    }

    return true;
}
