<?php

if (!defined('_PS_VERSION_')) {
    exit;
}

class Webixa_comparer extends Module
{
	const MAX_PRODUCTS = 4;
	
    public function __construct()
    {
        $this->name = 'webixa_comparer';
        $this->tab = 'front_office_features';
        $this->version = '1.1.0';
        $this->author = 'Webixa';
        $this->need_instance = 0;

        parent::__construct();

        $this->displayName = $this->l('Product Comparer');
        $this->description = $this->l('Product comparison tool for customers');
        $this->ps_versions_compliancy = ['min' => '8.0.0', 'max' => _PS_VERSION_];
    }

    public function install(): bool
    {
        return parent::install() &&
            $this->installDB() &&
            $this->registerHook('header') &&
            $this->registerHook('displayProductActions') &&
            $this->registerHook('actionCustomerAccountAdd') &&
            $this->registerHook('actionCronJob') &&
            $this->registerHook('displayTop') &&
            $this->registerHook('displayComparisonPage');
    }

    private function installDB(): bool
    {
        $sql = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'product_comparison` (
            `id_product_comparison` INT UNSIGNED NOT NULL AUTO_INCREMENT,
            `id_customer` INT(10) UNSIGNED NOT NULL,
            `id_guest` INT(10) UNSIGNED NOT NULL,
            `product_ids` TEXT NOT NULL,
            `date_add` DATETIME NOT NULL,
            PRIMARY KEY (`id_product_comparison`)
        ) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8;';

        return Db::getInstance()->execute($sql);
    }

    public function hookHeader(): void
    {
		Media::addJsDef([
            'webixaComparer' => [
                'ajaxUrl' => $this->context->link->getModuleLink('webixa_comparer', 'compare', ['ajax' => 1]),
				'comparedProducts' => $this->getProductsForComparison(
					(int)$this->context->customer->id,
					(int)$this->context->cookie->id_guest
				),
                'maxItems' => self::MAX_PRODUCTS,
                'translations' => [
                    'add' => $this->l('Add to Compare'),
                    'remove' => $this->l('Remove from Compare'),
                    'maxItems' => sprintf($this->l('Maximum %d products can be compared'), self::MAX_PRODUCTS),
                    'error' => $this->l('Error')
                ]
            ]
        ]);


        $this->context->controller->registerStylesheet(
            'webixa-comparer-tailwind',
            'modules/'.$this->name.'/views/css/tailwind.css',
            ['media' => 'all', 'priority' => 150]
        );

        $this->context->controller->registerJavascript(
            'webixa-comparer-js',
            'modules/'.$this->name.'/views/js/comparer.js',
            ['position' => 'bottom', 'priority' => 150]
        );
    }

    public function hookDisplayTop(array $params): string
    {
        $comparisonCount = count($this->getProductsForComparison(
            (int)$this->context->customer->id,
            (int)$this->context->cookie->id_guest
        ));

        $this->context->smarty->assign([
            'comparisonCount' => $comparisonCount,
            'comparisonLink' => $this->context->link->getModuleLink('webixa_comparer', 'compare')
        ]);

        return $this->display(__FILE__, 'views/templates/hooks/top.tpl');
    }

    public function hookActionCustomerAccountAdd(array $params): void
    {
        $guestId = (int)$this->context->cookie->id_guest;
        $customerId = (int)$params['newCustomer']->id;

        Db::getInstance()->update('product_comparison', [
            'id_customer' => $customerId,
            'id_guest' => 0
        ], 'id_guest = '.(int)$guestId);
    }

    public function hookActionCronJob(array $params): string|false
    {
        if ($params['controller']->process_name === 'webixa_comparer_clean') {
            $days = 30;
            $result = Db::getInstance()->delete('product_comparison',
                'date_add < DATE_SUB(NOW(), INTERVAL '.(int)$days.' DAY) AND id_customer = 0'
            );
            return sprintf($this->l('Cleaned %d old entries'), $result);
        }
        return false;
    }

    public function hookDisplayComparisonPage(array $params): string
    {
        $products = $this->getProductsForComparison(
            (int)$this->context->customer->id,
            (int)$this->context->cookie->id_guest
        );

        if (empty($products)) {
            return '<p class="text-center text-gray-500">'.$this->l('No products in comparison.').'</p>';
        }

        $this->context->smarty->assign([
            'products' => $this->getProductDetails($products),
            'features' => $this->getComparableFeatures($products)
        ]);

        return $this->display(__FILE__, 'views/templates/front/comparison_page.tpl');
    }

    public function hookDisplayProductActions(array $params): string
    {
        // Różne sposoby pobrania ID produktu w zależności od kontekstu
        $productId = 0;

        // Sprawdź różne możliwe struktury parametrów
        if (isset($params['product']['id_product'])) {
            $productId = (int)$params['product']['id_product'];
        } elseif (isset($params['product']['id'])) {
            $productId = (int)$params['product']['id'];
        } elseif (isset($params['id_product'])) {
            $productId = (int)$params['id_product'];
        } elseif (isset($params['product']) && is_object($params['product'])) {
            // Jeśli product to obiekt Product
            if (property_exists($params['product'], 'id')) {
                $productId = (int)$params['product']->id;
            } elseif (property_exists($params['product'], 'id_product')) {
                $productId = (int)$params['product']->id_product;
            }
        } elseif (isset($this->context->controller) &&
                  property_exists($this->context->controller, 'php_self') &&
                  $this->context->controller->php_self === 'product') {
            // Jeśli jesteśmy na stronie produktu, pobierz ID z URL
            $productId = (int)Tools::getValue('id_product');
        }

        // Jeśli nadal nie mamy ID produktu, spróbuj pobrać z kontekstu
        if (!$productId && isset($this->context->smarty)) {
            $smartyVars = $this->context->smarty->getTemplateVars();
            if (isset($smartyVars['product']['id_product'])) {
                $productId = (int)$smartyVars['product']['id_product'];
            } elseif (isset($smartyVars['product']['id'])) {
                $productId = (int)$smartyVars['product']['id'];
            }
        }

        // Jeśli nadal nie mamy prawidłowego ID produktu, nie wyświetlaj przycisku
        if (!$productId || !Validate::isUnsignedInt($productId)) {
            return '';
        }

        $isCompared = $this->isProductInComparison($productId);

        $this->context->smarty->assign([
            'productId' => $productId,
            'isCompared' => $isCompared
        ]);

        return $this->display(__FILE__, 'views/templates/hooks/button.tpl');
    }

    private function isProductInComparison(int $productId): bool
    {
        $customerId = (int)$this->context->customer->id;
        $guestId = (int)$this->context->cookie->id_guest;

        $query = new DbQuery();
        $query->select('product_ids');
        $query->from('product_comparison');
        $query->where($customerId ?
            "id_customer = $customerId" :
            "id_guest = $guestId");

        $result = Db::getInstance()->getValue($query);
        $products = $result ? json_decode($result, true) : [];

        return in_array($productId, $products);
    }

    public function addProduct(int $productId): array
    {
        $customerId = (int)$this->context->customer->id;
        $guestId = (int)$this->context->cookie->id_guest;

        $query = new DbQuery();
        $query->select('*');
        $query->from('product_comparison');
        $query->where($customerId ?
            "id_customer = $customerId" :
            "id_guest = $guestId");

        $row = Db::getInstance()->getRow($query);
        $products = $row ? json_decode($row['product_ids'], true) : [];

		if (count($products) > Webixa_Comparer::MAX_PRODUCTS) {
            return ['success' => false, 'message' => sprintf($this->l('Maximum %d products can be compared'), Webixa_Comparer::MAX_PRODUCTS)];
        }

        if (!in_array($productId, $products)) {
            $products[] = $productId;
        }

        if ($row) {
            Db::getInstance()->update('product_comparison', [
                'product_ids' => json_encode($products),
                'date_add' => date('Y-m-d H:i:s')
            ], 'id_product_comparison = '.$row['id_product_comparison']);
        } else {
            Db::getInstance()->insert('product_comparison', [
                'id_customer' => $customerId,
                'id_guest' => $guestId,
                'product_ids' => json_encode($products),
                'date_add' => date('Y-m-d H:i:s')
            ]);
        }

        return ['success' => true, 'count' => count($products)];
    }
	
	public function removeProduct(int $productId): array
	{
		$customerId = (int)$this->context->customer->id;
		$guestId = (int)$this->context->cookie->id_guest;

		$row = Db::getInstance()->getRow('
			SELECT * FROM '._DB_PREFIX_.'product_comparison
			WHERE '.( $customerId ? "id_customer = $customerId" : "id_guest = $guestId" ).'
		');

		$products = [];
		if ($row) {
			$products = json_decode($row['product_ids'], true);
			$key = array_search($productId, $products);

			if ($key !== false) {
				unset($products[$key]);
				Db::getInstance()->update('product_comparison', [
					'product_ids' => json_encode(array_values($products))
				], 'id_product_comparison = '.(int)$row['id_product_comparison']);
			}
		}

		return ['success' => true, 'count' => count($products)];
	}

	public function clearComparison(): array
	{
		$customerId = (int)$this->context->customer->id;
		$guestId = (int)$this->context->cookie->id_guest;

		Db::getInstance()->delete('product_comparison',
			$customerId ? "id_customer = $customerId" : "id_guest = $guestId"
		);

		return ['success' => true];
	}
		
	public function getProductsForComparison(int $customerId, int $guestId): array
	{
		$query = new DbQuery();
		$query->select('product_ids');
		$query->from('product_comparison');

		// Warunek dla zalogowanych i niezalogowanych
		$conditions = [];
		if ($customerId) {
			$conditions[] = "id_customer = " . (int)$customerId;
		}
		if ($guestId) {
			$conditions[] = "id_guest = " . (int)$guestId;
		}

		if (!empty($conditions)) {
			$query->where(implode(' OR ', $conditions));
		}

		$result = Db::getInstance()->getValue($query);

		return $result ? json_decode($result, true) : [];
	}

}
