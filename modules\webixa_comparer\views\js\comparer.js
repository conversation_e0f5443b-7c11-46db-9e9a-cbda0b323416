document.addEventListener('DOMContentLoaded', () => {

	// Sprawdź czy webixaComparer istnieje i ma poprawną strukturę
	if (typeof webixaComparer === 'undefined') {
		console.warn('webixaComparer not defined');
		return;
	}

	// Upewnij się, że comparedProducts jest tablicą
	if (!Array.isArray(webixaComparer.comparedProducts)) {
		webixaComparer.comparedProducts = [];
	}

	document.querySelectorAll('.product-miniature.js-product-miniature.tw-relative').forEach(flagsContainer => {
        const productCard = flagsContainer.closest('[data-id-product]');
        if (!productCard) return;

        const productId = parseInt(productCard.dataset.idProduct);
        const isCompared = webixaComparer.comparedProducts.includes(productId);
               
        const button = document.createElement('button');
        button.className = `compare-button ${isCompared ? 'compared' : ''}`;
        button.dataset.productId = productId;
        button.innerHTML = `
            <i class="icon">${isCompared ? '✓' : '↔'}</i>
            <span>${isCompared ? webixaComparer.translations.remove : webixaComparer.translations.add}</span>
        `;
        
        flagsContainer.insertAdjacentElement('afterend', button);
        
        button.addEventListener('click', async () => {
            const action = isCompared ? 'remove' : 'add';
            
            addToCompare(productId, action, button);
        });
    });
	
	
	document.querySelectorAll('.compare-button').forEach(button => {
		button.addEventListener('click', async () => {
			const productId = button.dataset.productId;
			const action = button.dataset.action;
			
			addToCompare(productId, action, button);
		});
	});
});

async function addToCompare(productId, action, button) {
	try {
		const response = await fetch(webixaComparer.ajaxUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				action: action,
				productId: productId
			})
		});

		const result = await response.json();
		
		if (result.success) {
			
			button.dataset.action = action === 'add' ? 'remove' : 'add'
			button.classList.toggle('bg-blue-100', action === 'remove')
			button.classList.toggle('text-blue-700', action === 'remove')
			button.classList.toggle('bg-red-100', action === 'add')
			button.classList.toggle('text-red-700', action === 'add')
							
			const textSpan = button.querySelector('span')
			textSpan.textContent = action === 'add' 
				? webixaComparer.translations.remove 
				: webixaComparer.translations.add
			
			showNotification(result.message);
			updateComparisonCount(result.data.count);			
		}
		else if (!result.success) {
			showNotification(result.message);
		}
	} catch (error) {
		console.error('Error:', error);
		showNotification(webixaComparer.translations.error, 'error')
	}
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;
    
    notification.style.position = 'fixed';
    notification.style.top = '50px';
    notification.style.right = '20px';
    notification.style.color = 'white';
    notification.style.padding = '16px';
    notification.style.borderRadius = '8px';
    notification.style.zIndex = 9999;
    notification.style.background = type === 'success' ? 'green' : 'red';

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function updateComparisonCount(count) {
    const countElements = document.querySelectorAll('.comparison-count');
    countElements.forEach(element => {
        element.textContent = count;
        element.style.display = count > 0 ? 'grid' : 'none';
    });
}
