<nav class="pagination">
  <div class="tw-relative tw-w-full">
    {block name='pagination_page_list'}
      {if $pagination.should_be_displayed}
        <ul class="page-list clearfix tw-flex tw-gap-5 tw-justify-center tw-w-full tw-my-4">
          {foreach from=$pagination.pages item="page"}
            <li {if $page.current} class="current" {/if} {if $page.type=='next'} class="tw-absolute tw-right-0" {/if}
              {if $page.type=='previous'} class="tw-absolute tw-left-0" {/if}>
              {if $page.type === 'spacer'}
                <span class="spacer">&hellip;</span>
              {else}
                {if $page.current}
                  <p>{$page.page}</p>
                {else}
                  <a rel="{if $page.type === 'previous'}prev{elseif $page.type === 'next'}next{/if}" href="{$page.url}"
                    class="{if $page.type === 'previous'}previous {elseif $page.type === 'next'}next {/if}{['disabled' => !$page.clickable, '' => true]|classnames}">
                  {/if}
                  {if $page.type === 'previous'}
                    <svg width="6" height="12" viewBox="0 0 6 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5.25 1.5L0.75 6L5.25 10.5" stroke="#0F6A6A" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>

                  {elseif $page.type === 'next'}
                    <svg width="6" height="12" viewBox="0 0 6 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0.75 10.5L5.25 6L0.75 1.5" stroke="#0F6A6A" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>

                  {elseif $page.type!=$page.current}
                    {$page.page}
                  {/if}
                </a>
              {/if}
            </li>
          {/foreach}
        </ul>
      {/if}
    {/block}
  </div>
</nav>