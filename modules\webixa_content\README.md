# Webixa Content

Prestashop 1.7.6+
Prestashop 8.1+

1. After adding Blocks with Items
2. Assign them to Hooks it will automatically call `$this->registerHook(WebixaContentHook->hook)`
3. It can also be used directly in `.tpl` as `widget` or `hook` make sure to provide `hook`

```php
// Mandatory parameter
(string) $hook; // ex. `displayHome`

// Optional
(int) $id; // WebixaContentBlock->id - it will display only specified Block
```

Simple example to call widget

```smarty
{widget name='webixa_content' hook='displayWebixaContent'}
```

Simple example to call hook with `id` parameter

```smarty
{hook h='displayWebixaContent' id='1'}
```
