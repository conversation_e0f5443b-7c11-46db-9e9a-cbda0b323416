BlockBot Security Alert - {shop_name}

Dear Administrator,

BlockBot has detected suspicious traffic on your website: {shop_url}

ALERT SUMMARY:
- Suspicious IP addresses: {suspicious_ips}
- Suspicious User Agents: {suspicious_agents}
- IP threshold: {ip_threshold} visits
- User Agent threshold: {agent_threshold} visits

{message}

You can review and manage these entries in your admin panel:
Go to: Modules > Module Manager > BlockBot > Configure

RECOMMENDED ACTIONS:
1. Review the suspicious entries in your BlockBot admin panel
2. Block confirmed malicious IPs and User Agents
3. Consider adjusting thresholds if you receive too many false positives
4. Monitor your website traffic regularly

This is an automated security alert from the BlockBot module.
Do not reply to this email.

Best regards,
BlockBot Security Module
