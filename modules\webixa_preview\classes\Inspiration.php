<?php

class Inspiration extends ObjectModel
{
    public $id;
    public $id_inspiration;
    public $active = 1;
    public $position;
    public $date_add;
    public $date_upd;
    public $rgb_color; // Added RGB color field
    public $id_shop;
    public $display_on_homepage = 0; // Flag to display on homepage

    // Lang fields
    public $title;
    public $link_rewrite;
    public $description;
    public $short_description;

    // Associations (not part of the definition, loaded separately)
    public $categories;
    public $products; // Products with x, y coordinates

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'inspiration',
        'primary' => 'id_inspiration',
        'multilang' => true,
        'multishop' => true,
        //'multishop_required' => true,
        'fields' => array(
            'active' => array('type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => true),
            'position' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'),
            'date_add' => array('type' => self::TYPE_DATE, 'validate' => 'isDate', 'copy_post' => false),
            'date_upd' => array('type' => self::TYPE_DATE, 'validate' => 'isDate', 'copy_post' => false),
            'id_shop' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true, 'shop' => true),
            'rgb_color' => array('type' => self::TYPE_STRING, 'validate' => 'isColor', 'size' => 7), // RGB color in format #RRGGBB
            'display_on_homepage' => array('type' => self::TYPE_BOOL, 'validate' => 'isBool'), // Checkbox to display on homepage

            // Lang fields
            'title' => array('type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'required' => true, 'size' => 255),
            'link_rewrite' => array('type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isLinkRewrite', 'required' => true, 'size' => 255),
            'description' => array('type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml'), // Allow HTML
            'short_description' => array('type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml', 'size' => 512), // Short description for homepage
        ),
        'associations' => array(
            'shops' => array('type' => self::HAS_MANY, 'field' => 'id_shop', 'object' => 'Shop')
        ),
    );

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        Shop::addTableAssociation('inspiration', array('type' => 'shop'));
        parent::__construct($id, $id_lang, $id_shop);
		if ($id_shop) {
			$this->id_shop = $id_shop;
		} elseif (Shop::isFeatureActive()) {
            $this->id_shop = Context::getContext()->shop->id;
        } else {
            $this->id_shop = 1;
        }

        // Automatically generate link_rewrite if empty
        if ($this->id && !$this->link_rewrite && $this->title) {
             if ($id_lang === null) {
                 // If no specific lang, try context lang or default lang
                 $id_lang_current = Context::getContext()->language->id;
                 if (isset($this->title[$id_lang_current]) && !empty($this->title[$id_lang_current])) {
                     $this->link_rewrite = Tools::link_rewrite($this->title[$id_lang_current]);
                 } else {
                     // Fallback to any available title
                     foreach ($this->title as $lang_id => $title) {
                         if (!empty($title)) {
                             $this->link_rewrite = Tools::link_rewrite($title);
                             break;
                         }
                     }
                 }
             } elseif (isset($this->title[$id_lang])) {
                 $this->link_rewrite = Tools::link_rewrite($this->title[$id_lang]);
             }
            // Consider checking for uniqueness and appending numbers if needed
        }
    }

    public function add($auto_date = true, $null_values = false)
    {
        if ($this->position <= 0) {
            $this->position = self::getHighestPosition() + 1;
        }
        if ($auto_date && !$this->date_add) {
            $this->date_add = date('Y-m-d H:i:s');
        }
        if ($auto_date && !$this->date_upd) {
            $this->date_upd = date('Y-m-d H:i:s');
        }

        if (Shop::isFeatureActive()) {
            $this->id_shop = Context::getContext()->shop->id;
        } else {
            $this->id_shop = 1;
        }

        // Generate link_rewrite before adding
        foreach (Language::getIDs(false) as $id_lang) {
            if (!isset($this->link_rewrite[$id_lang]) || empty($this->link_rewrite[$id_lang])) {
                 if (isset($this->title[$id_lang]) && !empty($this->title[$id_lang])) {
                     $this->link_rewrite[$id_lang] = Tools::link_rewrite($this->title[$id_lang]);
                     // Add uniqueness check here if necessary
                 }
            }
        }

        $return = parent::add($auto_date, $null_values);

        // Save categories and products after adding
        if ($return) {
            $this->setCategories($this->categories); // Assumes $this->categories is populated before add()
            $this->setProducts($this->products);     // Assumes $this->products is populated before add()
        }
        return $return;
    }

    public function update($null_values = false)
    {
        $this->date_upd = date('Y-m-d H:i:s');

        // Generate link_rewrite before updating if needed
        foreach (Language::getIDs(false) as $id_lang) {
            if (!isset($this->link_rewrite[$id_lang]) || empty($this->link_rewrite[$id_lang])) {
                 if (isset($this->title[$id_lang]) && !empty($this->title[$id_lang])) {
                     $this->link_rewrite[$id_lang] = Tools::link_rewrite($this->title[$id_lang]);
                     // Add uniqueness check here if necessary
                 }
            }
        }

        $return = parent::update($null_values);

        // Update categories and products
        if ($return) {
            $this->setCategories($this->categories); // Assumes $this->categories is populated before update()
            $this->setProducts($this->products);     // Assumes $this->products is populated before update()
        }
        return $return;
    }

    public function delete()
    {
        if (!parent::delete()) {
            return false;
        }
        self::cleanPositions();
        Db::getInstance()->delete('inspiration_to_category', 'id_inspiration = ' . (int)$this->id);
        Db::getInstance()->delete('inspiration_product', 'id_inspiration = ' . (int)$this->id);
        Db::getInstance()->delete('inspiration_related', 'id_inspiration = ' . (int)$this->id);
        Db::getInstance()->delete('inspiration_related', 'id_related_inspiration = ' . (int)$this->id);

        $sql = 'SELECT `image` FROM `' . _DB_PREFIX_ . 'inspiration_image` WHERE `id_inspiration` = ' . (int)$this->id;
        $images = Db::getInstance()->executeS($sql);
        if ($images) {
            foreach ($images as $image) {
                if (file_exists(_PS_MODULE_DIR_ . 'webixa_preview/views/img/' . $image['image'])) {
                    @unlink(_PS_MODULE_DIR_ . 'webixa_preview/views/img/' . $image['image']);
                }
            }
        }

        Db::getInstance()->delete('inspiration_image', 'id_inspiration = ' . (int)$this->id);

        $sql = 'DELETE ip.* FROM `' . _DB_PREFIX_ . 'inspiration_image_product` ip
                INNER JOIN `' . _DB_PREFIX_ . 'inspiration_image` ii ON ip.id_inspiration_image = ii.id_inspiration_image
                WHERE ii.id_inspiration = ' . (int)$this->id;
        Db::getInstance()->execute($sql);
        return true;
    }

    /**
     * Gets the highest position.
     * @return int Highest position
     */
    public static function getHighestPosition()
    {
        $sql = 'SELECT MAX(`position`) FROM `' . _DB_PREFIX_ . 'inspiration`';
        $position = Db::getInstance()->getValue($sql);
        return (is_numeric($position)) ? $position : -1;
    }

    /**
     * Reorders positions.
     * @return bool Success
     */
    public static function cleanPositions()
    {
        $sql = 'SELECT `id_inspiration` FROM `' . _DB_PREFIX_ . 'inspiration` ORDER BY `position` ASC';
        $result = Db::getInstance()->executeS($sql);

        if (!$result) {
            return true; // No items to reorder
        }

        $i = 0;
        foreach ($result as $row) {
            $updateSql = 'UPDATE `' . _DB_PREFIX_ . 'inspiration` SET `position` = ' . (int)$i++ . ' WHERE `id_inspiration` = ' . (int)$row['id_inspiration'];
            Db::getInstance()->execute($updateSql);
        }
        return true;
    }

    /**
     * Get inspiration ID by link_rewrite
     * @param string $rewrite
     * @param int $id_lang
     * @return int|false
     */
    public static function getIdByRewrite($rewrite, $id_lang)
    {
        if (empty($rewrite) || !Validate::isLinkRewrite($rewrite)) {
            return false;
        }

        $sql = new DbQuery();
        $sql->select('i.id_inspiration');
        $sql->from('inspiration', 'i');
        $sql->innerJoin('inspiration_lang', 'il', 'i.id_inspiration = il.id_inspiration');
        $sql->where('il.link_rewrite = \'' . pSQL($rewrite) . '\'');
        $sql->where('il.id_lang = ' . (int)$id_lang);
        $sql->where('i.active = 1');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get inspirations for the front office list
     * @param int $id_lang
     * @param int $limit
     * @param int $offset
     * @param int|null $id_inspiration_category
     * @return array|false Formatted inspirations
     */
    public static function getInspirations($id_lang, $limit, $offset, $id_inspiration_category = null, $exclude_id = null)
    {
        $sql = new DbQuery();
        $sql->select('i.id_inspiration'); // Select only ID first
        $sql->from('inspiration', 'i');
        $sql->where('i.active = 1');

        if ($id_inspiration_category !== null) {
            $sql->innerJoin('inspiration_to_category', 'itc', 'i.id_inspiration = itc.id_inspiration');
            $sql->where('itc.id_inspiration_category = ' . (int)$id_inspiration_category);
        }
        if ($exclude_id !== null) {
            $sql->where('i.id_inspiration != ' . (int)$exclude_id);
        }

        // Join lang table to ensure translation exists for the current language
        $sql->innerJoin('inspiration_lang', 'il_check', 'i.id_inspiration = il_check.id_inspiration AND il_check.id_lang = ' . (int)$id_lang);

        $sql->orderBy('i.position ASC, i.date_add DESC');
        $sql->limit((int)$limit, (int)$offset);

        $ids = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if (!$ids) {
            return [];
        }

        $inspiration_list = [];
        foreach ($ids as $id_row) {
            $inspiration = new Inspiration((int)$id_row['id_inspiration'], $id_lang);
            if (Validate::isLoadedObject($inspiration)) {
                // You might want a simplified representation for the list view
                // Get main image URL
                $mainImageUrl = $inspiration->getMainImageUrl();
                $mainImage = null;

                // If we have a main image, extract just the filename
                if ($mainImageUrl) {
                    $pathParts = pathinfo($mainImageUrl);
                    $mainImage = $pathParts['basename'];
                }

                $inspiration_list[] = [
                    'id_inspiration' => $inspiration->id,
                    'title' => $inspiration->title,
                    'link_rewrite' => $inspiration->link_rewrite,
                    'image' => $mainImage,
                    // Add link later in the controller using context->link
                ];
            }
        }
        return $inspiration_list;
    }

    /**
     * Get count of inspirations for pagination
     * @param int $id_lang
     * @param int|null $id_inspiration_category
     * @return int
     */
    public static function getInspirationsCount($id_lang, $id_inspiration_category = null)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(DISTINCT i.id_inspiration)'); // Use DISTINCT if joining multiple tables
        $sql->from('inspiration', 'i');
        $sql->where('i.active = 1');

        if ($id_inspiration_category !== null) {
            $sql->innerJoin('inspiration_to_category', 'itc', 'i.id_inspiration = itc.id_inspiration');
            $sql->where('itc.id_inspiration_category = ' . (int)$id_inspiration_category);
        }

        // Join lang table to ensure translation exists for the current language
        $sql->innerJoin('inspiration_lang', 'il_check', 'i.id_inspiration = il_check.id_inspiration AND il_check.id_lang = ' . (int)$id_lang);


        return (int)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get associated categories for this inspiration
     * @param int $id_lang
     * @return array
     */
    public function getCategories($id_lang)
    {
        if ($this->categories !== null && isset($this->categories[$id_lang])) {
             return $this->categories[$id_lang]; // Return cached if available
        }

        $sql = new DbQuery();
        $sql->select('ic.*, icl.*');
        $sql->from('inspiration_to_category', 'itc');
        $sql->innerJoin('inspiration_category', 'ic', 'itc.id_inspiration_category = ic.id_inspiration_category');
        $sql->leftJoin('inspiration_category_lang', 'icl', 'ic.id_inspiration_category = icl.id_inspiration_category AND icl.id_lang = ' . (int)$id_lang);
        $sql->where('itc.id_inspiration = ' . (int)$this->id);
        $sql->where('ic.active = 1'); // Only show active categories
        $sql->orderBy('ic.position ASC');

        $this->categories[$id_lang] = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        return $this->categories[$id_lang];
    }

    /**
     * Set associated categories for this inspiration
     * @param array|null $category_ids Array of category IDs
     * @return bool
     */
    public function setCategories($category_ids)
    {
        if ($this->id <= 0) return false; // Need an ID to set associations

        // Clear existing associations
        Db::getInstance()->delete('inspiration_to_category', 'id_inspiration = ' . (int)$this->id);

        if (is_array($category_ids) && !empty($category_ids)) {
            $insert_data = [];
            foreach ($category_ids as $id_category) {
                if ((int)$id_category > 0) { // Basic validation
                    $insert_data[] = array(
                        'id_inspiration' => (int)$this->id,
                        'id_inspiration_category' => (int)$id_category,
                    );
                }
            }
            if (!empty($insert_data)) {
                return Db::getInstance()->insert('inspiration_to_category', $insert_data, false, true, Db::INSERT_IGNORE);
            }
        }
        return true; // Return true even if no categories were set (clearing is success)
    }


    /**
     * Get associated products with positions and formatted details
     * @param int $id_lang
     * @param Context|null $context
     * @return array
     */
    public function getProducts($id_lang, Context $context = null)
    {
        if ($this->products !== null && isset($this->products[$id_lang])) {
             return $this->products[$id_lang]; // Return cached if available
        }

        if ($context === null) {
            $context = Context::getContext();
        }

        $sql = new DbQuery();
        // Select necessary fields for Product::assembleProduct and our positions
        $sql->select('p.*, product_shop.*, stock.out_of_stock, IFNULL(stock.quantity, 0) as quantity, pl.`description`, pl.`description_short`, pl.`link_rewrite`, pl.`meta_description`, pl.`meta_keywords`, pl.`meta_title`, pl.`name`, image_shop.`id_image` id_image, il.`legend` as legend, ip.position_x, ip.position_y');
        $sql->from('inspiration_product', 'ip');
        $sql->innerJoin('product', 'p', 'p.id_product = ip.id_product');
        $sql->innerJoin('product_shop', 'product_shop', 'product_shop.id_product = p.id_product AND product_shop.id_shop = ' . (int)$context->shop->id);
        $sql->leftJoin('product_lang', 'pl', 'p.id_product = pl.id_product AND pl.id_lang = ' . (int)$id_lang . Shop::addSqlRestrictionOnLang('pl'));
        $sql->leftJoin('image_shop', 'image_shop', 'image_shop.id_product = p.id_product AND image_shop.cover = 1 AND image_shop.id_shop = ' . (int)$context->shop->id);
        $sql->leftJoin('image_lang', 'il', 'image_shop.id_image = il.id_image AND il.id_lang = ' . (int)$id_lang);
        $sql->join(Product::sqlStock('p', 0, $context->shop)); // Add stock information

        $sql->where('ip.id_inspiration = ' . (int)$this->id);
        $sql->where('product_shop.active = 1');
        $sql->where('product_shop.visibility IN ("both", "catalog")'); // Ensure product is visible

        $sql->groupBy('p.id_product'); // Group by product ID

        $productsRaw = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if (!$productsRaw) {
             $this->products[$id_lang] = [];
            return [];
        }

        // Use Product::assembleProduct to format product data correctly for templates
        $assembler = new ProductAssembler($context);
        $presenterFactory = new ProductPresenterFactory($context);
        $presentationSettings = $presenterFactory->getPresentationSettings();
        $presenter = $presenterFactory->getPresenter();

        $formattedProducts = [];
        foreach ($productsRaw as $rawProduct) {
            // Need to add position_x and position_y back after assembly
            $posX = $rawProduct['position_x'];
            $posY = $rawProduct['position_y'];
            $assembledProduct = $assembler->assembleProduct($rawProduct);
            $presentedProduct = $presenter->present(
                $presentationSettings,
                $assembledProduct,
                $context->language
            );
            // Add custom position data
            $presentedProduct['inspiration_position_x'] = $posX;
            $presentedProduct['inspiration_position_y'] = $posY;
            $formattedProducts[] = $presentedProduct;
        }

        return $formattedProducts;
    }

     /**
      * Set associated products for this inspiration (primarily for admin)
      * @param array|null $products Array of arrays, each containing 'id_product', 'position_x', 'position_y'
      * @return bool
      */
     public function setProducts($products)
     {
         if ($this->id <= 0) return false;

         // Clear existing associations
         Db::getInstance()->delete('inspiration_product', 'id_inspiration = ' . (int)$this->id);

         if (is_array($products) && !empty($products)) {
             $values = array();
             foreach ($products as $product_data) {
                 if (isset($product_data['id_product']) && (int)$product_data['id_product'] > 0) {
                     $values[] = array(
                         'id_inspiration' => (int)$this->id,
                         'id_product' => (int)$product_data['id_product'],
                         'position_x' => isset($product_data['position_x']) ? (int)$product_data['position_x'] : 0,
                         'position_y' => isset($product_data['position_y']) ? (int)$product_data['position_y'] : 0
                     );
                 }
             }

             if (!empty($values)) {
                 return Db::getInstance()->insert('inspiration_product', $values);
             }
         }

         return true;
     }

     /**
      * Get the main image URL for this inspiration
      * @return string|null
      */
     public function getMainImageUrl()
     {
         require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/InspirationImage.php';

         $mainImage = InspirationImage::getMainImage($this->id);
         if ($mainImage) {
             return $mainImage->getImageUrl();
         }
         return null;
     }

     public function getRelatedInspirations($id_lang, $limit = 4, Context $context = null)
     {
         if ($context === null) {
             $context = Context::getContext();
         }

         $categories = $this->getCategories($id_lang);
         if (empty($categories)) {
             return [];
         }

         $category_ids = [];
         foreach ($categories as $category) {
             $category_ids[] = (int)$category['id_inspiration_category'];
         }

         $sql = 'SELECT DISTINCT i.id_inspiration
                FROM `' . _DB_PREFIX_ . 'inspiration` i
                INNER JOIN `' . _DB_PREFIX_ . 'inspiration_to_category` itc ON i.id_inspiration = itc.id_inspiration
                INNER JOIN `' . _DB_PREFIX_ . 'inspiration_lang` il ON i.id_inspiration = il.id_inspiration AND il.id_lang = ' . (int)$id_lang . '
                WHERE itc.id_inspiration_category IN (' . implode(',', $category_ids) . ')
                AND i.id_inspiration != ' . (int)$this->id . '
                AND i.active = 1
                ORDER BY i.date_add DESC
                LIMIT ' . (int)$limit;

         $ids = Db::getInstance()->executeS($sql);

         if (!$ids) {
             return [];
         }

         $related_inspirations = [];
         foreach ($ids as $id_row) {
             $inspiration = new Inspiration((int)$id_row['id_inspiration'], $id_lang);
             if (Validate::isLoadedObject($inspiration)) {
                 $mainImageUrl = $inspiration->getMainImageUrl();
                 $related_inspirations[] = [
                     'id_inspiration' => $inspiration->id,
                     'title' => $inspiration->title,
                     'link_rewrite' => $inspiration->link_rewrite,
                     'image_url' => $mainImageUrl,
                     'url' => $context->link->getModuleLink('webixa_preview', 'inspiration', ['inspiration_rewrite' => $inspiration->link_rewrite])
                 ];
             }
         }

         return $related_inspirations;
     }

     public function setRelatedInspirations($related_ids)
     {
         if ($this->id <= 0) return false;

         Db::getInstance()->delete('inspiration_related', 'id_inspiration = ' . (int)$this->id);

         if (is_array($related_ids) && !empty($related_ids)) {
             $values = [];
             $position = 0;
             foreach ($related_ids as $id_related) {
                 if ((int)$id_related > 0 && (int)$id_related != (int)$this->id) {
                     $values[] = [
                         'id_inspiration' => (int)$this->id,
                         'id_related_inspiration' => (int)$id_related,
                         'position' => $position++
                     ];
                 }
             }

             if (!empty($values)) {
                 return Db::getInstance()->insert('inspiration_related', $values);
             }
         }

         return true;
     }

     public function getRelatedInspirationIds()
     {
         $sql = 'SELECT id_related_inspiration
                FROM `' . _DB_PREFIX_ . 'inspiration_related`
                WHERE id_inspiration = ' . (int)$this->id . '
                ORDER BY position ASC';

         $rows = Db::getInstance()->executeS($sql);

         if (!$rows) {
             return [];
         }

         $ids = [];
         foreach ($rows as $row) {
             $ids[] = (int)$row['id_related_inspiration'];
         }

         return $ids;
     }

     /**
      * Get all images for this inspiration
      *
      * @param int $id_lang Language ID
      * @param Context $context Shop context
      * @return array Array of images with products
      */
     public function getAllImages($id_lang, Context $context = null)
     {
         if ($context === null) {
             $context = Context::getContext();
         }

         // First, check if we have a main image in the inspiration_image table
         require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/InspirationImage.php';

         $images = [];

         // Get all images from the inspiration_image table
         $sql = new DbQuery();
         $sql->select('id_inspiration_image, image, position, main');
         $sql->from('inspiration_image');
         $sql->where('id_inspiration = '.(int)$this->id);
         $sql->orderBy('position ASC');

         $imageRows = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

         if ($imageRows) {
             foreach ($imageRows as $row) {
                 $imageObj = new InspirationImage((int)$row['id_inspiration_image']);
                 if (Validate::isLoadedObject($imageObj)) {
                     // Get products specifically associated with this image
                     $imageProducts = $imageObj->getProducts($id_lang, $context);

                     $images[] = [
                         'id_inspiration_image' => $imageObj->id,
                         'image' => $imageObj->image,
                         'url' => $imageObj->getImageUrl(),
                         'position' => $imageObj->position,
                         'main' => $imageObj->main,
                         'products' => $imageProducts
                     ];
                 }
             }
         }

         return $images;
     }

     /**
      * Add a new image to this inspiration
      *
      * @param string $image Image filename
      * @param bool $main Whether this is the main image
      * @return InspirationImage|false New image object or false on failure
      */
     public function addImage($image, $main = false)
     {
         require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/InspirationImage.php';

         // Get highest position
         $sql = new DbQuery();
         $sql->select('MAX(position)');
         $sql->from('inspiration_image');
         $sql->where('id_inspiration = '.(int)$this->id);

         $position = (int)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

         // Create new image
         $inspirationImage = new InspirationImage();
         $inspirationImage->id_inspiration = (int)$this->id;
         $inspirationImage->image = pSQL($image);
         $inspirationImage->position = $position + 1;
         $inspirationImage->main = (bool)$main;

         if ($main) {
             // Unset main flag for all other images
             Db::getInstance()->update(
                 'inspiration_image',
                 ['main' => 0],
                 'id_inspiration = '.(int)$this->id
             );
         }

         if ($inspirationImage->add()) {
             return $inspirationImage;
         }

         return false;
     }


}
