<?php

if (!defined('_PS_VERSION_')) {
    exit;
}


require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/Inspiration.php';
require_once _PS_MODULE_DIR_ . 'webixa_preview/classes/InspirationCategory.php';

class webixa_previewInspirationModuleFrontController extends ModuleFrontController
//class Webixa_price_negotiationListsModuleFrontController extends ModuleFrontController
{
    public $ssl = true;
    public $module;

    public function __construct()
    {
        parent::__construct();
        $this->context = Context::getContext();
        $this->module = Module::getInstanceByName('webixa_preview');
    }


    public function initContent()
    {
        parent::initContent();

        $inspiration_rewrite = Tools::getValue('inspiration_rewrite');
        $category_rewrite = Tools::getValue('category_rewrite');



        if (!empty($inspiration_rewrite)) {

            $this->displayInspirationDetails($inspiration_rewrite, $category_rewrite);
        } elseif (!empty($category_rewrite)) {

            $this->displayInspirationList($category_rewrite);
        } else {
            $this->displayInspirationList();
        }
    }


    protected function displayInspirationList($category_rewrite = null)
    {
        $id_lang = $this->context->language->id;
        $page = (int)Tools::getValue('page', 1);
        if ($page < 1) $page = 1;
        $limit = (int)Configuration::get('PS_PRODUCTS_PER_PAGE');
        $offset = ($page - 1) * $limit;

        $currentCategory = null;
        $id_inspiration_category = null;


        $categories = InspirationCategory::getActiveCategories($id_lang);


        if ($category_rewrite !== null) {
            $id_inspiration_category = InspirationCategory::getIdByRewrite($category_rewrite, $id_lang);
            if (!$id_inspiration_category) {
                Tools::redirect('index.php?controller=404');
                return;
            }

            $currentCategory = new InspirationCategory($id_inspiration_category, $id_lang);
            if (!Validate::isLoadedObject($currentCategory)) {
                 Tools::redirect('index.php?controller=404');
                 return;
            }
        }


        $inspirationsData = Inspiration::getInspirations($id_lang, $limit, $offset, $id_inspiration_category);
        $totalInspirations = Inspiration::getInspirationsCount($id_lang, $id_inspiration_category);


        $inspirations = [];
        foreach ($inspirationsData as $insp) {

             $inspiration_obj = new Inspiration($insp['id_inspiration'], $id_lang);
             if (Validate::isLoadedObject($inspiration_obj)) {
                 $insp['products'] = $inspiration_obj->getProductsWithCoordinates($id_lang, $this->context);


                 $categories = $inspiration_obj->getCategories($id_lang);


                 if (!empty($categories)) {

                     $firstCategory = $categories[0];
                     $insp['url'] = $this->context->link->getModuleLink(
                         'webixa_preview',
                         'inspiration',
                         [
                             'category_rewrite' => $firstCategory['link_rewrite'],
                             'inspiration_rewrite' => $insp['link_rewrite']
                         ]
                     );
                 } else {

                     $insp['url'] = $this->context->link->getModuleLink(
                         'webixa_preview',
                         'inspiration',
                         ['inspiration_rewrite' => $insp['link_rewrite']]
                     );
                 }
             } else {
                 $insp['products'] = [];
                 $insp['url'] = $this->context->link->getModuleLink(
                     'webixa_preview',
                     'inspiration',
                     ['inspiration_rewrite' => $insp['link_rewrite']]
                 );
             }

             $insp['image_url'] = $insp['image'] ? $this->module->getPathUri() . 'views/img/' . $insp['image'] : null;
             $inspirations[] = $insp;
        }


        foreach ($inspirations as &$insp) {
            if (empty($insp['link_rewrite'])) {
                $insp['link_rewrite'] = Tools::link_rewrite($insp['title']);
            }
        }
        unset($insp);


        $pagination = $this->getTemplateVarPagination($totalInspirations, $limit, $page);
        $breadcrumb = $this->getBreadcrumbLinks();
        $this->context->smarty->assign([
            'inspirations' => $inspirations,
            'categories' => $categories,
            'currentCategory' => Validate::isLoadedObject($currentCategory) ? $currentCategory : null,
            'pagination' => $pagination,
            'breadcrumb' => $breadcrumb,
            'page_title' => Validate::isLoadedObject($currentCategory) ? $currentCategory->name : $this->module->l('Inspirations', 'inspirationcontroller'),
            'inspiration_img_dir_path' => $this->module->getLocalPath().'views/img/',
            'inspiration_img_dir_uri' => $this->module->getPathUri().'views/img/'
        ]);

        $this->setTemplate('module:webixa_preview/views/templates/front/inspiration_list.tpl');
    }


    protected function displayInspirationDetails($inspiration_rewrite, $category_rewrite = null)
    {
        $id_lang = $this->context->language->id;
        $id_inspiration = Inspiration::getIdByRewrite($inspiration_rewrite, $id_lang);

        if (!$id_inspiration) {
            Tools::redirect('index.php?controller=404');
            return;
        }

        $inspiration = new Inspiration($id_inspiration, $id_lang);
        if (!Validate::isLoadedObject($inspiration)) {
            Tools::redirect('index.php?controller=404');
            return;
        }


        if (!empty($category_rewrite)) {
            $inspirationCategories = $inspiration->getCategories($id_lang);
            $categoryValid = false;

            foreach ($inspirationCategories as $category) {
                if ($category['link_rewrite'] == $category_rewrite) {
                    $categoryValid = true;
                    break;
                }
            }


            if (!$categoryValid) {

                if (!empty($inspirationCategories)) {
                    $firstCategory = $inspirationCategories[0];
                    $correctUrl = $this->context->link->getModuleLink(
                        'webixa_preview',
                        'inspiration',
                        [
                            'category_rewrite' => $firstCategory['link_rewrite'],
                            'inspiration_rewrite' => $inspiration->link_rewrite
                        ]
                    );
                    Tools::redirect($correctUrl);
                    return;
                } else {

                    $fallbackUrl = $this->context->link->getModuleLink(
                        'webixa_preview',
                        'inspiration',
                        ['inspiration_rewrite' => $inspiration->link_rewrite]
                    );
                    Tools::redirect($fallbackUrl);
                    return;
                }
            }
        }


        $inspiration->link_rewrite_original = $inspiration->link_rewrite;


        $languages = Language::getLanguages(true);
        $default_lang = (int)Configuration::get('PS_LANG_DEFAULT');
        $default_title = '';


        if (isset($inspiration->title[$default_lang]) && !empty($inspiration->title[$default_lang])) {
            $default_title = $inspiration->title[$default_lang];
        } else {

            foreach ($inspiration->title as $lang_id => $title) {
                if (!empty($title)) {
                    $default_title = $title;
                    break;
                }
            }
        }


        foreach ($languages as $language) {
            $id_lang = (int)$language['id_lang'];
            if (empty($inspiration->link_rewrite[$id_lang])) {

                $title_to_use = (isset($inspiration->title[$id_lang]) && !empty($inspiration->title[$id_lang]))
                    ? $inspiration->title[$id_lang]
                    : $default_title;

                if (!empty($title_to_use)) {
                    $inspiration->link_rewrite[$id_lang] = Tools::link_rewrite($title_to_use);
                } else {

                    $inspiration->link_rewrite[$id_lang] = 'inspiration-' . $inspiration->id;
                }
            }
        }


        $needsUpdate = false;
        foreach ($languages as $language) {
            $lang_id = (int)$language['id_lang'];
            if (isset($inspiration->link_rewrite[$lang_id]) &&
                $inspiration->link_rewrite[$lang_id] != $inspiration->link_rewrite_original[$lang_id]) {
                $needsUpdate = true;
                break;
            }
        }

        if ($needsUpdate) {
            $inspiration->update();
        }

        $inspirationCategories = $inspiration->getCategories($id_lang);
        $inspirationProducts = $inspiration->getProducts($id_lang, $this->context);
        $related_inspirations = $inspiration->getRelatedInspirations($id_lang, 4, $this->context);




        $inspiration_images = $inspiration->getAllImages($id_lang, $this->context);        
        $breadcrumb = $this->getBreadcrumbLinks();

        $this->context->smarty->assign([
            'inspiration' => $inspiration,
            'inspiration_categories' => $inspirationCategories,
            'inspiration_products' => $inspirationProducts,
            'inspiration_image_url' => $inspiration->getMainImageUrl(),
            'inspiration_images' => $inspiration_images,
            'related_inspirations' => $related_inspirations,
            'breadcrumb' => $breadcrumb,
            'page_title' => $inspiration->title,
            'allow_add_to_cart' => (int)Configuration::get('PS_ORDERING_PROCESS_TYPE') === 0,
            'inspiration_img_dir_uri' => $this->module->getPathUri().'views/img/',
        ]);

        $this->setTemplate('module:webixa_preview/views/templates/front/inspiration_details.tpl');
    }

     protected function getTemplateVarPagination($totalItems, $itemsPerPage, $currentPage)
     {

         $totalPages = ceil($totalItems / $itemsPerPage);


         if ($currentPage < 1) {
             $currentPage = 1;
         } elseif ($currentPage > $totalPages) {
             $currentPage = $totalPages;
         }


         $pages = [];
         $displayedPages = 5;

         if ($totalPages <= $displayedPages) {

             for ($i = 1; $i <= $totalPages; $i++) {
                 $pages[] = $i;
             }
         } else {

             $pages[] = 1;


             $startPage = max(2, $currentPage - floor(($displayedPages - 3) / 2));
             $endPage = min($totalPages - 1, $currentPage + ceil(($displayedPages - 3) / 2));


             if ($startPage > 2) {
                 $pages[] = '...';
             }


             for ($i = $startPage; $i <= $endPage; $i++) {
                 $pages[] = $i;
             }


             if ($endPage < $totalPages - 1) {
                 $pages[] = '...';
             }


             if ($totalPages > 1) {
                 $pages[] = $totalPages;
             }
         }


         $category_rewrite = Tools::getValue('category_rewrite');
         $params = [];

         if (!empty($category_rewrite)) {
             $params['category_rewrite'] = $category_rewrite;
             $baseUrl = $this->context->link->getModuleLink('webixa_preview', 'inspiration', $params);
         } else {

             $baseUrl = $this->context->link->getModuleLink('webixa_preview', 'inspiration');
         }


         $link = new Link();
         $baseUrl = $link->getModuleLink('webixa_preview', 'inspiration', $params, true, $this->context->language->id, $this->context->shop->id);


         $separator = (strpos($baseUrl, '?') === false) ? '?' : '&';


         $urlTemplate = $baseUrl . $separator . 'page=';


         $paginationLinks = [];
         foreach ($pages as $page) {
             if ($page === '...') {
                 $paginationLinks[] = [
                     'type' => 'spacer',
                     'label' => '...',
                     'url' => null,
                     'clickable' => false
                 ];
             } else {
                 $paginationLinks[] = [
                     'type' => 'page',
                     'page' => $page,
                     'label' => $page,
                     'url' => $urlTemplate . $page,
                     'current' => ($page == $currentPage),
                     'clickable' => ($page != $currentPage)
                 ];
             }
         }


         $previousPage = ($currentPage > 1) ? $currentPage - 1 : null;
         $nextPage = ($currentPage < $totalPages) ? $currentPage + 1 : null;

         return [
             'total_items' => $totalItems,
             'items_shown_from' => (($currentPage - 1) * $itemsPerPage) + 1,
             'items_shown_to' => min($currentPage * $itemsPerPage, $totalItems),
             'current_page' => $currentPage,
             'pages_count' => $totalPages,
             'pages' => $paginationLinks,
             'should_be_displayed' => ($totalPages > 1),
             'previous' => [
                 'url' => $previousPage ? $urlTemplate . $previousPage : null,
                 'clickable' => ($previousPage !== null)
             ],
             'next' => [
                 'url' => $nextPage ? $urlTemplate . $nextPage : null,
                 'clickable' => ($nextPage !== null)
             ]
         ];
     }



    public function getBreadcrumbLinks()
    {

        $breadcrumb = [
            'links' => [
                [
                    'title' => $this->trans('Home', [], 'Shop.Theme.Global'),
                    'url' => $this->context->link->getPageLink('index')
                ],
                [
                    'title' => $this->module->l('Inspirations', 'inspiration'),
                    'url' => $this->context->link->getModuleLink('webixa_preview', 'inspiration')
                ]
            ],
            'count' => 2
        ];


        $category_rewrite = Tools::getValue('category_rewrite');
        $inspiration_rewrite = Tools::getValue('inspiration_rewrite');


        if (!empty($inspiration_rewrite)) {
            $id_lang = $this->context->language->id;
            $id_inspiration = Inspiration::getIdByRewrite($inspiration_rewrite, $id_lang);

            if ($id_inspiration) {
                $inspiration = new Inspiration($id_inspiration, $id_lang);

                if (Validate::isLoadedObject($inspiration)) {

                    $categories = $inspiration->getCategories($id_lang);


                    if (!empty($categories)) {
                        $category = $categories[0];


                        if (isset($category['id_parent']) && $category['id_parent'] > 0) {
                            $parentCategory = new InspirationCategory($category['id_parent'], $id_lang);
                            if (Validate::isLoadedObject($parentCategory)) {
                                $breadcrumb['links'][] = [
                                    'title' => $parentCategory->name,
                                    'url' => $this->context->link->getModuleLink('webixa_preview', 'inspiration', ['category_rewrite' => $parentCategory->link_rewrite])
                                ];
                                $breadcrumb['count']++;
                            }
                        }


                        $breadcrumb['links'][] = [
                            'title' => $category['name'],
                            'url' => $this->context->link->getModuleLink('webixa_preview', 'inspiration', ['category_rewrite' => $category['link_rewrite']])
                        ];
                        $breadcrumb['count']++;
                    }


                    $breadcrumb['links'][] = [
                        'title' => $inspiration->title,
                        'url' => $this->context->link->getModuleLink('webixa_preview', 'inspiration', ['inspiration_rewrite' => $inspiration_rewrite])
                    ];
                    $breadcrumb['count']++;
                }
            }
        }

        elseif (!empty($category_rewrite)) {
            $id_lang = $this->context->language->id;
            $id_category = InspirationCategory::getIdByRewrite($category_rewrite, $id_lang);

            if ($id_category) {
                $category = new InspirationCategory($id_category, $id_lang);

                if (Validate::isLoadedObject($category)) {

                    if ($category->id_parent > 0) {
                        $parentCategory = new InspirationCategory($category->id_parent, $id_lang);
                        if (Validate::isLoadedObject($parentCategory)) {
                            $breadcrumb['links'][] = [
                                'title' => $parentCategory->name,
                                'url' => $this->context->link->getModuleLink('webixa_preview', 'inspiration', ['category_rewrite' => $parentCategory->link_rewrite])
                            ];
                            $breadcrumb['count']++;
                        }
                    }


                    $breadcrumb['links'][] = [
                        'title' => $category->name,
                        'url' => $this->context->link->getModuleLink('webixa_preview', 'inspiration', ['category_rewrite' => $category_rewrite])
                    ];
                    $breadcrumb['count']++;
                }
            }
        }

        return $breadcrumb;
    }


    public function getCanonicalURL()
    {
        $params = [];
        $category_rewrite = Tools::getValue('category_rewrite');
        $inspiration_rewrite = Tools::getValue('inspiration_rewrite');

        if (!empty($inspiration_rewrite)) {
            $params['inspiration_rewrite'] = $inspiration_rewrite;
        } elseif (!empty($category_rewrite)) {
            $params['category_rewrite'] = $category_rewrite;
        }



        return $this->context->link->getModuleLink(
            'webixa_preview',
            'inspiration',
            $params,
            true, // SSL
            $this->context->language->id,
            $this->context->shop->id
        );
    }

}
