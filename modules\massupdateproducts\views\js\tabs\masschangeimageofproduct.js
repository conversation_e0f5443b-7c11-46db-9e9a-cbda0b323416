/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function () {
    var $products_list_body = $('#products_list').find('.main_body');
    var $products_list_head = $('#products_list').find('.main_head');


    var mouseenter = function () {
        $products_list_body.on('mouseenter', '.image-show-contents', function () {
            var $scope = $(this);
            $scope.removeClass('inactive').addClass('active').css({height: parseInt($scope.find('.image-show-content').css('height'))});
            console.log(parseInt($scope.find('.image-show-content').css('height')));

        });

        $products_list_body.on('mouseleave', '.image-show-contents', function () {
            var $scope = $(this);
            $scope.removeClass('active').addClass('inactive').css({height: parseInt($scope.find('.image-show-content').css('height'))});
        });

        $products_list_body.on('mouseenter', '.file-upload-block', function () {
            var $scope = $(this);
            $scope.removeClass('inactive').addClass('active');

        });

        $products_list_body.on('mouseleave', '.file-upload-block', function () {
            var $scope = $(this);
            $scope.removeClass('active').addClass('inactive');
        });
    };

    var mouseover = function () {
        $products_list_body.off('mouseenter', '.image-show-contents');
        $products_list_body.off('mouseleave', '.image-show-contents');
        $products_list_body.off('mouseenter', '.file-upload-block');
        $products_list_body.off('mouseleave', '.file-upload-block');
    };


    $products_list_body.on('click', '.cover-inactive', function () {
        var $scope = $(this);
        var $image = $scope.closest('.image-show-content-block');
        var $tr = $scope.closest('.product-row');
        $tr.find('.cover-active').removeClass('cover-active').addClass('cover-inactive');
        $tr.find('.to-send[send-name="cover"]').val($image.attr('pic-id'));
        console.log($tr.find('.to-send[send-name="cover"]'));
        $scope.removeClass('cover-inactive').addClass('cover-active');
        $tr.find('img:first').attr('src', $image.find('img').attr('src'));
        if ($image.attr('pic-id') !== $tr.find('.to-send[send-name="cover"]').attr('pic-id'))
        {
            $image.find('.to-send[send-name="picture_belong"]').val($image.attr('pic-id'));
            $image.find('.picture_belong').removeClass('rem-inactive').addClass('rem-active');
        }
    });

    $products_list_body.on('click', '.add-from-main-inactive', function () {
        var $scope = $(this);
        var $image = $scope.closest('.image-show-content-block');
        $image.find('.to-send[send-name="picture_belong"]').val($image.attr('pic-id'));
        $scope.removeClass('add-from-main-inactive').addClass('add-from-main-active');
        $.notify(picture_added, 'success', {
            autoHideDelay: 2000
        });
    });

    $products_list_body.on('click', '.add-from-main-active', function () {
        var $scope = $(this);
        var $image = $scope.closest('.image-show-content-block');
        $image.find('.to-send[send-name="picture_belong"]').val('0');
        $scope.removeClass('add-from-main-active').addClass('add-from-main-inactive');
    });

    $products_list_body.on('click', '.rem-inactive', function () {
        var $scope = $(this);
        var $image = $scope.closest('.image-show-content-block');
        $image.find('.to-send[send-name="picture_belong"]').val($image.attr('pic-id'));
        $scope.removeClass('rem-inactive').addClass('rem-active');
    });

    $products_list_body.on('click', '.rem-active', function () {
        var $scope = $(this);
        var $image = $scope.closest('.image-show-content-block');
        var $tr = $scope.closest('.product-row');
        if (parseInt($image.attr('pic-id')) !== parseInt($tr.find('.to-send[send-name="cover"]').val()))
        {
            $image.find('.to-send[send-name="picture_belong"]').val('0');
            $scope.removeClass('rem-active').addClass('rem-inactive');
        }
    });

    $products_list_head.find('.file-upload-block').fileupload2(['image/jpeg', 'image/gif', 'image/png']);

    $products_list_head.on('click', '.button-upload', function () {
        $(this).prev('.file-upload').trigger('click');
    });

    /*var drop = function () {
     $products_list_body.find('.combination-row').find('.image-content-droppable').droppable({
     drop: function (event, ui) {
     var $scope = $(this);
     var $target = $scope.closest('tr').find('.image-show-content');
     var $pic_id = ui.draggable.attr('pic-id');
     if ($target.find('.image-show-content-block[pic-id="' + $pic_id + '"]').length === 0) {
     $target.append('<div class="image-show-content-block" pic-id="' + $pic_id + '">' + ui.draggable.html() + '</div>');
     var $added = $target.find('.image-show-content-block[pic-id="' + $pic_id + '"]');
     $.notify(picture_added, 'success', {
     autoHideDelay: 2000
     });
     $added.find('.image-show-content-picture-legend').remove();
     $added.find('.cover-inactive, .cover-active').remove();
     }
     else
     {
     $.notify(picture_not_added, 'error', {
     autoHideDelay: 2000
     });
     }
     }
     });
     };*/



    var trigger = function () {
        mouseenter();
        $products_list_body.find('.image-show-contents').each(function () {
            var $this = $(this);
            $this.removeClass('active').addClass('inactive').css({height: parseInt($this.find('.image-show-content').css('height'))});
        });
        $products_list_body.find('.file-upload-block').fileupload(['image/jpeg', 'image/gif', 'image/png']);
        /*$products_list_body.find('.product-row').find('.image-show-content-block').draggable({revert: true, helper: "clone",
         start: function() {
         mouseover();
         
         },
         stop: function() {
         mouseenter();
         }
         });*/
        //drop();
    };

    $products_list_body.on('refresh', function () {
        trigger();
    });

    $products_list_body.on('filter', function () {
        trigger();
    });

    $products_list_body.on('saved', function (event, $response) {
        trigger();
    });

    $products_list_body.on('addnewfile', function (event, tr) {
        $('#products_list .main_body').find('.product-row').each(function () {
            var $prod_row = $(this);
            var $tbody = $prod_row.find('.file-upload-block .upload tbody');
            if ($prod_row.find('.check_single').prop('checked'))
                $tbody.append(tr);
        });
    });

    $products_list_body.on('click', '.button-upload', function () {
        $(this).prev('.file-upload').trigger('click');
    });
});