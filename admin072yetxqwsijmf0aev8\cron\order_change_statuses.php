<?php

require dirname(__FILE__) . '/../../config/config.inc.php';

$orderStateToSet = '3';
$orderStateToCheck = [
    '2',
    '11',
    '13',
];

$orders = Db::getInstance()->executeS(
    'SELECT * FROM ' . _DB_PREFIX_ . 'orders WHERE current_state IN (' . implode(',', $orderStateToCheck) . ')'
);

foreach ($orders as $order) {
    $order = new Order($order['id_order']);
    $order->setCurrentState($orderStateToSet);
    $order->save();
}