{**
 * Image list template for inspiration module
 * Used for AJAX refresh of images list
 *}

{if isset($inspiration_images) && $inspiration_images}
    {foreach from=$inspiration_images item=image}
        <div class="col-xs-6 col-md-3 image-item" data-id="{$image.id_inspiration_image}">
            <div class="panel {if $image.main}panel-success{else}panel-default{/if}">
                <div class="panel-heading">
                    {if $image.main}
                        <span class="label label-success">{l s='Main' d='Modules.Webixapreview.Admin'}</span>
                    {/if}
                    {l s='Image' d='Modules.Webixapreview.Admin'} #{$image.id_inspiration_image}
                </div>
                <div class="panel-body">
                    <img src="{$image.url}" class="img-responsive" alt="">
                </div>
                <div class="panel-footer">
                    <div class="btn-group">
                        <button type="button" class="btn btn-default btn-sm set-main-image" data-id="{$image.id_inspiration_image}">
                            <i class="icon-star"></i> {l s='Set as Main' d='Modules.Webixapreview.Admin'}
                        </button>
                        <button type="button" class="btn btn-danger btn-sm delete-image" data-id="{$image.id_inspiration_image}">
                            <i class="icon-trash"></i> {l s='Delete' d='Modules.Webixapreview.Admin'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    {/foreach}
{else}
    <div class="alert alert-info">
        {l s='No images uploaded yet. Use the image upload field to add images.' d='Modules.Webixapreview.Admin'}
    </div>
{/if}
