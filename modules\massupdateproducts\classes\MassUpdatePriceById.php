<?php

class MassUpdatePriceById extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdatePriceById');
        parent::__construct($module, $object, $context);
    }

    public function extra()
    {
        $ssl = isset($_SERVER['HTTPS']) ? 'https://' : 'http://';
        $last = Db::getInstance()->getValue('SELECT id_product FROM `'._DB_PREFIX_.'product` ORDER BY `id_product` DESC');
        $ajaxGetProductInfo = $ssl.$this->context->shop->domain.$this->context->shop->physical_uri.'module/massupdateproducts/PriceById';
        $this->context->smarty->assign(array(
            'max_id' => $last,
            'ajaxGetProductInfo' => $ajaxGetProductInfo
        ));
    }

}