<?php
/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */

class MassUpdateCategoriesProductsModel extends MassUpdateProductsAbstract
{
	public function __construct(&$module, &$object, &$context)
	{
		$this->settings_name = Tools::strtoupper('MassUpdateCategoriesProductsModel');
		parent::__construct($module, $object, $context);

		//category_default
		$this->fields['category_def'] = array(
			'display_name' => $this->module->l('Category default', $this->languages_name),
			'name' => 'category_def',
			'active' => true,
			'type' => self::TYPE_CAT_REC
		);

		//categories
		$this->fields['categories'] = array(
			'display_name' => $this->module->l('Categories', $this->languages_name),
			'name' => 'categories',
			'active' => true,
			'type' => self::TYPE_MUL_SEL,
                        'extra' => 'unselect'
		);

		$categories = $this->object->getCategoriesRecursive(Configuration::get('PS_HOME_CATEGORY'));

		$categories_selected = array(
			0 => array(
				'level_depth' => 1,
				'display_name' => $this->module->l('none', $this->languages_name)
		));
		if ($categories)
			foreach ($categories as $category)
				$categories_selected[$category['id_category']] = array(
					'level_depth' => $category['level_depth'],
					'display_name' => $category['name']
				);

		$this->fields['category_def']['select'] = $categories_selected;
		unset($categories_selected[0]);
		$this->fields['categories']['select'] = $categories_selected;
	}
	public function save(array $data)
	{
		$result = array();
		$end = false;
		$context = Context::getContext();
		if ($data) {
            foreach ($data as $id_product => $params) {
                $result[$id_product] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );
                $product_params = array_key_exists('data', $params) ? $params['data'] : null;
                $product = new Product($id_product, false, $context->cookie->id_lang, $context->shop->id);
                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }
                if ($product_params) {
                    $product->id_category_default = $product_params['category_def'];
                    $categories = isset($product_params['categories']) && is_array($product_params['categories']) ? $product_params['categories'] : array();
                    $categories_add = array();
                    $categorysAdd = array();
                    if ($categories) {
                        foreach ($categories as $category) {
                            if ($category) {
                                $categories_add[] = array(
                                    'id' => $category
                                );
                                $categorysAdd[] = $category;
                            }
                        }
                    }
//                    print_r($categories_add);exit();
                    $errors = $product->validateFields(false, true);
                    if ($errors !== true) {
                        $result[$id_product]['message'] = '<p style="color: #FFF;">' . (is_bool($errors) ?
                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)) . '</p>';
                        continue;
                    } else {
                        if ($product->update() && $product->updateCategories($categorysAdd)) {//setWsCategories($categories_add)) {
                            $result[$id_product]['error'] = false;
                            $result[$id_product]['message'] = $this->displayProduct($product);
                        } else {
                            $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                            continue;
						}
                    }
                } else {
                    $result[$id_product]['error'] = false;
                    $result[$id_product]['message'] = $this->displayProduct($product);
                }
            }
        } else {
            $end = true;
        }
		return array(
			'raport' => $result,
			'end' => $end
		);
	}
	public function display($result)
	{
		$ids_product = $result['result'];
		$result['result'] = '';
		$result['table'] = true;

		if ($ids_product)
			foreach ($ids_product as $product_arr)
			{
				$product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
				if (!Validate::isLoadedObject($product))
				{
					$result['dates']['products_count'] --;
					continue;
				}

				$result['result'] .= $this->displayProduct($product);
			}

		return $result;
	}
	public function displayProduct(&$product)
	{
		parent::displayProduct($product);
		$product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
			$this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
		$product->quantity = $this->object->shop_group->share_stock ? $product->quantity : StockAvailable::getQuantityAvailableByProduct($product->id);
		$product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
		$product->full_name = $product->name[$this->context->language->id];

		$this->fields['category_def']['value'] = $product->id_category_default;

		$categories = $product->getCategories();

		$this->fields['categories']['value'] = $categories;

		$this->context->smarty->assign(array(
			'product' => $product,
			'fields' => $this->getFields(),
			'languages' => $this->object->languages,
            'iso' => file_exists(_PS_ROOT_DIR_.'/js/tinymce/langs/'.$this->context->language->iso_code.'.js') ? $this->context->language->iso_code : 'en',
            'path_css' => _THEME_CSS_DIR_,
            'ad' => dirname($_SERVER['PHP_SELF'])
		));

		return $this->object->createTemplate('tr-product.tpl')->fetch();
	}
	public function displayCombination(&$product, &$combination)
	{
		if (!parent::displayCombination($product, $combination))
			return '';
	}
}