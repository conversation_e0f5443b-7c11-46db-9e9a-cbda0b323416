# BlockBot Module for PrestaShop

## Opis

Moduł BlockBot to zaawansowane narzędzie do monitorowania i blokowania botów w sklepie PrestaShop. Moduł automatycznie loguje wszystkie wizyty w sklepie i umożliwia blokowanie podejrzanego ruchu na podstawie adresów IP i User Agents.

## Funkcjonalności

### 1. Logowanie ruchu
- Automatyczne logowanie wszystkich wizyt w sklepie
- Rejestrowanie: IP, User Agent, Referer, znacznik czasu
- Zapis do tabeli bazy danych `ps_blockbot_logs`

### 2. Analiza statystyk
- **Zakładka IP Statistics**: Grupowanie wizyt według adresów IP
- **Zakładka User Agent Statistics**: Grupowanie wizyt według User Agents
- Sortowanie według liczby wizyt (od największej)
- Oznaczanie podejrzanych wpisów przekraczających progi

### 3. Blokowanie ruchu
- Blokowanie pojedynczych adresów IP
- Blokowanie pojedynczych User Agents
- Blokowanie grupowe (zaznaczenie wielu elementów)
- Automatyczne blokowanie przy przekroczeniu progów

### 4. Mechanizm blokowania
- Dodawanie reguł do pliku `.htaccess` (blokowanie na poziomie serwera)
- Dodawanie reguł do pliku `robots.txt` (wskazówki dla botów)
- Automatyczne tworzenie kopii zapasowych przed modyfikacją

### 5. Zarządzanie blokadami
- Lista aktualnie zablokowanych IP i User Agents
- Możliwość odblokowywania (usuwanie z .htaccess i robots.txt)
- Przegląd wszystkich aktywnych blokad

### 6. Ustawienia i progi
- **Dwupoziomowy system automatycznego blokowania:**
  - **Rapid-Fire**: Natychmiastowe blokowanie przy agresywnych atakach
    - IP: domyślnie 30 żądań/minutę (ochrona przed DDoS)
    - User Agent: domyślnie 30 żądań/minutę (ochrona przed agresywnymi botami)
  - **High-Volume**: Blokowanie przy długotrwałej podejrzanej aktywności
    - IP: domyślnie 200 wizyt/24h (każde odświeżenie strony = 1 wizyta)
    - User Agent: domyślnie 200 wizyt/24h (ten sam próg dla wszystkich)
- **Wyjaśnienie progów:**
  - Każde załadowanie strony = 1 wizyta (nie unikalne sesje)
  - IP i User Agent mają te same progi (sprawiedliwe traktowanie)
  - 200 wizyt/24h = ~8 wizyt/godzinę (normalny użytkownik: 10-50 wizyt/sesję)
- Włączanie/wyłączanie automatycznego blokowania
- Włączanie/wyłączanie blokowania pustych User Agents
- Konfiguracja okresu automatycznego czyszczenia logów (domyślnie: 3 miesiące)
- Włączanie/wyłączanie całego modułu

### 7. Automatyczne czyszczenie logów
- Cron job do automatycznego usuwania starych logów
- Konfigurowalny okres przechowywania (domyślnie: 3 miesiące)
- Możliwość ręcznego czyszczenia przez panel admin
- Generowanie klucza bezpieczeństwa dla cron job

### 8. Powiadomienia email
- Automatyczne powiadomienia o podejrzanym ruchu
- Konfigurowalny próg powiadomień
- Domyślnie wyłączone (można włączyć w ustawieniach)
- Wysyłka na adresy administratorów sklepu
- Ograniczenie do jednego powiadomienia na godzinę

### 9. Ochrona przed blokowaniem dobrych botów
- Lista znanych dobrych botów (Google, Bing, Facebook, etc.)
- Automatyczne ostrzeżenia przy próbie blokowania legalnych botów
- Ochrona SEO przed przypadkowym zablokowaniem botów wyszukiwarek
- Pomijanie dobrych botów przy blokowania grupowym

### 10. Predefiniowane listy botów
- Lista znanych złośliwych botów automatycznie dodawana przy instalacji
- Osobne pliki z listami dobrych i złych botów
- Łatwe aktualizowanie list bez modyfikacji kodu
- Ponad 200 znanych złośliwych botów w bazie

## Instalacja

1. Skopiuj folder `blockbot` do katalogu `modules/` w Twojej instalacji PrestaShop
2. Przejdź do panelu administracyjnego PrestaShop
3. Idź do **Moduły** > **Menedżer modułów**
4. Znajdź moduł "Block Bot" i kliknij **Instaluj**
5. Po instalacji moduł będzie dostępny w menu **Narzędzia** > **Block Bot**

## Konfiguracja

### Podstawowe ustawienia
1. Przejdź do **Narzędzia** > **Block Bot** > **Ustawienia**
2. Włącz moduł przełącznikiem "Enable BlockBot"
3. Ustaw progi blokowania:
   - **IP Blocking Threshold**: liczba wizyt z tego samego IP w 24h (domyślnie: 200)
   - **User Agent Blocking Threshold**: liczba wizyt z tym samego User Agent w 24h (domyślnie: 200)
   - **Rapid-Fire IP Threshold**: liczba żądań z tego samego IP w 1 min (domyślnie: 30)
   - **Rapid-Fire User Agent Threshold**: liczba żądań z tego samego User Agent w 1 min (domyślnie: 30)
4. Skonfiguruj dodatkowe opcje:
   - **Automatic Blocking**: automatyczne blokowanie (domyślnie: włączone)
   - **Block Empty User Agents**: blokowanie pustych User Agents (domyślnie: włączone)
   - **Email Notifications**: powiadomienia email (domyślnie: wyłączone)
   - **Auto-cleanup Period**: okres automatycznego czyszczenia (domyślnie: 3 miesiące)
5. Kliknij **Zapisz ustawienia**

### Monitorowanie ruchu
1. **Zakładka IP Statistics**: 
   - Przegląd wszystkich adresów IP i liczby ich wizyt
   - Podejrzane IP (przekraczające próg) są oznaczone na czerwono
   - Możliwość blokowania pojedynczych IP lub grupowego

2. **Zakładka User Agent Statistics**:
   - Przegląd wszystkich User Agents i liczby ich wizyt
   - Podejrzane User Agents są oznaczone na czerwono
   - Możliwość blokowania pojedynczych User Agents lub grupowego

### Blokowanie
- **Pojedyncze blokowanie**: Kliknij przycisk "Zablokuj" przy wybranym elemencie
- **Grupowe blokowanie**: Zaznacz checkboxy przy wybranych elementach i kliknij "Zablokuj wybrane"
- **Automatyczne blokowanie**: Włącz w ustawieniach, aby automatycznie blokować elementy przekraczające progi

### Odblokowywanie
- Zablokowane elementy są wyświetlane na górze każdej zakładki
- Kliknij ikonę "X" przy zablokowanym elemencie, aby go odblokować
- Odblokowanie usuwa wpisy z bazy danych oraz reguły z plików .htaccess i robots.txt

### Automatyczne czyszczenie logów
1. Przejdź do **Narzędzia** > **Block Bot** > **Ustawienia**
2. Skonfiguruj okres przechowywania logów (domyślnie: 3 miesiące)
3. Wygeneruj klucz cron używając przycisku "Generate New Cron Key"
4. Skonfiguruj cron job na serwerze:

**Metoda 1 - Linia komend (zalecana):**
```bash
0 2 * * * /usr/bin/php /ścieżka/do/prestashop/modules/blockbot/cron/cleanup.php
```

**Metoda 2 - Przez HTTP:**
```bash
0 2 * * * /usr/bin/wget -q -O - "http://twoja-domena.pl/modules/blockbot/cron/cleanup.php?key=TWÓJ_KLUCZ_CRON"
```

5. Opcjonalnie możesz ręcznie wyczyścić stare logi przyciskiem "Clean Old Logs Now"

### Konfiguracja powiadomień email
1. Przejdź do **Narzędzia** > **Block Bot** > **Ustawienia**
2. Włącz "Email Notifications"
3. Wprowadź adresy email (domyślnie: email administratora sklepu)
4. Zapisz ustawienia
5. Powiadomienia będą wysyłane maksymalnie raz na godzinę przy wykryciu podejrzanego ruchu

## Baza danych i bezpieczeństwo

### Tabele bazy danych
Moduł tworzy następujące tabele:
- `ps_blockbot_logs` - przechowuje wszystkie logi wizyt
- `ps_blockbot_blocked` - przechowuje zablokowane IP i User Agents

### Kopie zapasowe
Moduł automatycznie tworzy kopie zapasowe przed modyfikacją plików:
- `.htaccess.blockbot.backup.YYYY-MM-DD-HH-MM-SS`
- `robots.txt.blockbot.backup.YYYY-MM-DD-HH-MM-SS`

### Zarządzanie logami
- Logi przechowywane w tabeli `ps_blockbot_logs`
- Automatyczne czyszczenie przez cron job
- Możliwość ręcznego czyszczenia przez panel admin
- Konfigurowalny okres przechowywania logów

### Bezpieczeństwo
- Walidacja wszystkich danych wejściowych
- Zabezpieczenie przed atakami XSS i SQL injection
- Sprawdzanie uprawnień administratora
- Bezpieczne operacje na plikach

## Ostrzeżenia

⚠️ **Ważne uwagi:**
- Blokowanie botów wyszukiwarek (Google, Bing) może negatywnie wpłynąć na SEO
- Zawsze sprawdź User Agent przed zablokowaniem
- Regularnie czyść plik logów, aby nie zajmował zbyt dużo miejsca
- Kopie zapasowe plików są tworzone automatycznie

## Rozwiązywanie problemów

### Moduł nie loguje wizyt
1. Sprawdź czy moduł jest włączony w ustawieniach
2. Sprawdź połączenie z bazą danych
3. Sprawdź czy tabele `ps_blockbot_logs` i `ps_blockbot_blocked` istnieją

### Blokowanie nie działa
1. Sprawdź uprawnienia do zapisu plików `.htaccess` i `robots.txt`
2. Sprawdź czy serwer obsługuje reguły .htaccess
3. Sprawdź logi błędów serwera

### Problemy z wydajnością
1. Skonfiguruj cron job do automatycznego czyszczenia logów
2. Rozważ zmniejszenie okresu przechowywania logów
3. Rozważ zwiększenie progów blokowania
4. Wyłącz automatyczne blokowanie jeśli nie jest potrzebne

### Problemy z cron job
1. Sprawdź czy ścieżka do PHP jest poprawna
2. Sprawdź uprawnienia do pliku `cleanup.php`
3. Sprawdź logi cron w pliku `modules/blockbot/cron/cleanup.log`
4. Sprawdź czy klucz cron jest poprawny (dla metody HTTP)

## Wsparcie

W przypadku problemów:
1. Sprawdź logi błędów PrestaShop
2. Sprawdź uprawnienia plików
3. Sprawdź konfigurację serwera
4. Skontaktuj się z administratorem systemu

## Wersja
1.0.0 - Pierwsza wersja modułu

## Licencja
Komercyjna - jeden sklep na licencję
