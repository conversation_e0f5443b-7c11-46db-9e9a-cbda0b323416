<?php
if (!defined('_PS_VERSION_')) {
    exit;
}

class InspirationImage extends ObjectModel
{
    public $id_inspiration_image;
    public $id_inspiration;
    public $image;
    public $position;
    public $main;
    public static $definition = array(
        'table' => 'inspiration_image',
        'primary' => 'id_inspiration_image',
        'fields' => array(
            'id_inspiration' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true),
            'image' => array('type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 255, 'required' => true),
            'position' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'),
            'main' => array('type' => self::TYPE_BOOL, 'validate' => 'isBool'),
        ),
    );

    /**
     * Get image URL
     *
     * @return string Image URL
     */
    public function getImageUrl()
    {
        return _MODULE_DIR_ . 'webixa_preview/views/img/' . $this->image;
    }

    /**
     * Get products associated with this image
     *
     * @param int $id_lang Language ID
     * @param Context $context Shop context
     * @return array Array of products with coordinates
     */
    public function getProducts($id_lang, $context)
    {
        $sql = new DbQuery();
        $sql->select('ip.id_product, ip.position_x, ip.position_y');
        $sql->from('inspiration_image_product', 'ip');
        $sql->where('ip.id_inspiration_image = '.(int)$this->id);

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $products = [];
        if ($results) {
            foreach ($results as $row) {
                $product = new Product((int)$row['id_product'], true, $id_lang, $context->shop->id);
                if (Validate::isLoadedObject($product)) {
                    $products[] = [
                        'id_product' => $product->id,
                        'name' => $product->name,
                        'price' => $context->getCurrentLocale()->formatPrice(
                            Product::getPriceStatic($product->id),
                            $context->currency->iso_code
                        ),
                        'image_url' => $context->link->getImageLink($product->link_rewrite, $product->getCoverWs(), 'home_default'),
                        'position_x' => $row['position_x'],
                        'position_y' => $row['position_y']
                    ];
                }
            }
        }

        return $products;
    }

    /**
     * Add a product to this image
     *
     * @param int $id_product Product ID
     * @param float $position_x X position (percentage)
     * @param float $position_y Y position (percentage)
     * @return bool Success
     */
    public function addProduct($id_product, $position_x, $position_y)
    {
        // Validate inputs
        if (!Validate::isUnsignedInt($id_product) || !Validate::isFloat($position_x) || !Validate::isFloat($position_y)) {
            return false;
        }

        // Ensure position values are within 0-100 range
        $position_x = max(0, min(100, $position_x));
        $position_y = max(0, min(100, $position_y));

        // Check if product already exists for this image
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('inspiration_image_product');
        $sql->where('id_inspiration_image = '.(int)$this->id);
        $sql->where('id_product = '.(int)$id_product);

        $exists = (bool)Db::getInstance()->getValue($sql);

        if ($exists) {
            // Update existing product position
            return Db::getInstance()->update(
                'inspiration_image_product',
                [
                    'position_x' => (float)$position_x,
                    'position_y' => (float)$position_y
                ],
                'id_inspiration_image = '.(int)$this->id.' AND id_product = '.(int)$id_product
            );
        } else {
            // Insert new product
            return Db::getInstance()->insert(
                'inspiration_image_product',
                [
                    'id_inspiration_image' => (int)$this->id,
                    'id_product' => (int)$id_product,
                    'position_x' => (float)$position_x,
                    'position_y' => (float)$position_y
                ]
            );
        }
    }

    /**
     * Remove a product from this image
     *
     * @param int $id_product Product ID
     * @return bool Success
     */
    public function removeProduct($id_product)
    {
        if (!Validate::isUnsignedInt($id_product)) {
            return false;
        }

        return Db::getInstance()->delete(
            'inspiration_image_product',
            'id_inspiration_image = '.(int)$this->id.' AND id_product = '.(int)$id_product
        );
    }

    /**
     * Get all images for an inspiration
     *
     * @param int $id_inspiration Inspiration ID
     * @return array Array of InspirationImage objects
     */
    public static function getByInspiration($id_inspiration)
    {
        if (!Validate::isUnsignedInt($id_inspiration)) {
            return [];
        }

        $sql = new DbQuery();
        $sql->select('id_inspiration_image');
        $sql->from('inspiration_image');
        $sql->where('id_inspiration = '.(int)$id_inspiration);
        $sql->orderBy('position ASC');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $images = [];
        if ($results) {
            foreach ($results as $row) {
                $image = new InspirationImage((int)$row['id_inspiration_image']);
                if (Validate::isLoadedObject($image)) {
                    $images[] = $image;
                }
            }
        }

        return $images;
    }

    /**
     * Get main image for an inspiration
     *
     * @param int $id_inspiration Inspiration ID
     * @return InspirationImage|false Main image or false if not found
     */
    public static function getMainImage($id_inspiration)
    {
        if (!Validate::isUnsignedInt($id_inspiration)) {
            return false;
        }

        $sql = new DbQuery();
        $sql->select('id_inspiration_image');
        $sql->from('inspiration_image');
        $sql->where('id_inspiration = '.(int)$id_inspiration);
        $sql->where('main = 1');
        $sql->orderBy('position ASC');
        //$sql->limit(1);

        $id_inspiration_image = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        if ($id_inspiration_image) {
            $image = new InspirationImage((int)$id_inspiration_image);
            if (Validate::isLoadedObject($image)) {
                return $image;
            }
        }

        return false;
    }

    /**
     * Set this image as the main image for its inspiration
     *
     * @return bool Success
     */
    public function setAsMain()
    {
        // First, unset main flag for all images of this inspiration
        Db::getInstance()->update(
            'inspiration_image',
            ['main' => 0],
            'id_inspiration = '.(int)$this->id_inspiration
        );

        // Then set this image as main
        $this->main = 1;
        return $this->save();
    }
}
