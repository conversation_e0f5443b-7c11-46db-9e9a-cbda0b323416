{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<div {$attr|strval} class="mass-multi-select" send-name="{$name|strval}">
    {if $select}
        {if isset($extra) && $extra eq 'unselect'}
            <div class="mass-multi-select-el" style="text-align: left; padding-left: 15px;">
                <span class="mass-multi-select-el-ch">
                    <input class="uncheck" type="checkbox" />{l s='Uncheck changed default category' mod='massupdateproducts'}
                </span>
            </div>
        {/if}
        {foreach $select as $id => $option}
            <div {if isset($extra[$id])} extra="{$extra[$id]|strval}" {/if} class="mass-multi-select-el" style="text-align: left; padding-left: {(15*$option['level_depth'])|strval}px;">
                <span class="mass-multi-select-el-ch">
                    <input send-name="{$name|strval}" {if is_array($value) && in_array($id, $value)}checked{/if} type="checkbox" value="{$id|strval}" class="{$class_mass|strval} no-checkbox" />{$option['display_name']|strval}
                </span>
            </div>
        {/foreach}
    {/if}
</div>