# ~~start~~ Do not remove this comment, Prestashop will keep automatically the code outside this comment when .htaccess will be generated again
# .htaccess automaticaly generated by PrestaShop e-commerce open-source solution
# https://www.prestashop.com - https://www.prestashop.com/forums

<IfModule mod_rewrite.c>
<IfModule mod_env.c>
SetEnv HTTP_MOD_REWRITE On
</IfModule>

RewriteEngine on


#Domain: localhost
RewriteRule . - [E=REWRITEBASE:/luxury-love/]
RewriteRule ^api(?:/(.*))?$ %{ENV:REWRITEBASE}webservice/dispatcher.php?url=$1 [QSA,L]
RewriteRule ^upload/.+$ %{ENV:REWRITEBASE}index.php [QSA,L]

# Images
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^(([\d])(?:\-[\w-]*)?)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/p/$2/$1$3 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^(([\d])([\d])(?:\-[\w-]*)?)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/p/$2/$3/$1$4 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^(([\d])([\d])([\d])(?:\-[\w-]*)?)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/p/$2/$3/$4/$1$5 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^(([\d])([\d])([\d])([\d])(?:\-[\w-]*)?)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/p/$2/$3/$4/$5/$1$6 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^(([\d])([\d])([\d])([\d])([\d])(?:\-[\w-]*)?)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/p/$2/$3/$4/$5/$6/$1$7 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^(([\d])([\d])([\d])([\d])([\d])([\d])(?:\-[\w-]*)?)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/p/$2/$3/$4/$5/$6/$7/$1$8 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^(([\d])([\d])([\d])([\d])([\d])([\d])([\d])(?:\-[\w-]*)?)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/p/$2/$3/$4/$5/$6/$7/$8/$1$9 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^c/([\d]+)(\-[\.*\w-]*)/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/c/$1$2$3 [L]
RewriteCond %{HTTP_HOST} ^localhost$
RewriteRule ^c/([a-zA-Z_-]+)(-[\d]+)?/.+(\.(?:jpe?g|webp|png|avif))$ %{ENV:REWRITEBASE}img/c/$1$2$3 [L]
# AlphaImageLoader for IE and fancybox
RewriteRule ^images_ie/?([^/]+)\.(jpe?g|png|gif)$ %{ENV:REWRITEBASE}js/jquery/plugins/fancybox/images/$1.$2 [L]

# Dispatcher
RewriteCond %{REQUEST_FILENAME} -s [OR]
RewriteCond %{REQUEST_FILENAME} -l [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^.*$ - [NC,L]
RewriteRule ^.*$ %{ENV:REWRITEBASE}index.php [NC,L]
</IfModule>

AddType application/vnd.ms-fontobject .eot
AddType font/ttf .ttf
AddType font/otf .otf
AddType application/font-woff .woff
AddType font/woff2 .woff2
<IfModule mod_headers.c>
	<FilesMatch "\.(ttf|ttc|otf|eot|woff|woff2|svg)$">
		Header set Access-Control-Allow-Origin "*"
	</FilesMatch>
</IfModule>

<Files composer.lock>
    # Apache 2.2
    <IfModule !mod_authz_core.c>
        Order deny,allow
        Deny from all
    </IfModule>

    # Apache 2.4
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
</Files>
#If rewrite mod isn't enabled
ErrorDocument 404 /luxury-love/index.php?controller=404

# ~~end~~ Do not remove this comment, Prestashop will keep automatically the code outside this comment when .htaccess will be generated again
# BlockBot Rule
SetEnvIfNoCase User-Agent "MJ12bot/v1.4.8" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "^SemrushBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Twiceler" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Mail.ru" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "VoilaBot BETA 1.2" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "libwww-perl/5.805" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Java/1.5.0_11" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Sogou web spider/3.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "psbot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Exabot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Charlotte/1.0b" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "boitho.com-dc" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "ajSitemap" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "bot/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "panscient.com" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Java/1.6.0_11" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebDataCentreBot/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Java" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SapphireWebCrawler" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Yandex" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Baiduspider" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Rankivabot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "DBLBot/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Black Hole" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Titan" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebStripper" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "NetMechanic" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CherryPicker" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "EmailCollector" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "EmailSiphon" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebBandit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "EmailWolf" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "ExtractorPro" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CopyRightCheck" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Crescent" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Wget" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SiteSnagger" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "ProWebWalker" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CheeseBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Teleport" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "TeleportPro" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "MIIxpc" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Telesoft" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Website Quester" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebZip" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "moget/2.1" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebZip/4.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebSauger" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebCopier" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "NetAnts" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Mister PiX" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebAuto" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "TheNomad" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WWW-Collector-E" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "RMA" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "libWeb/clsHTTP" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "asterias" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "httplib" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "turingos" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "spanner" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "InfoNaviRobot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Harvest/1.5" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Bullseye/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Mozilla/4.0 (compatible; BullsEye; Windows 95)" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Crescent Internet ToolPak HTTP OLE Control v.1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CherryPickerSE/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CherryPicker /1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebBandit/3.50" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "NICErsPRO" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Microsoft URL Control - 5.01.4511" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "DittoSpyder" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Foobot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebmasterWorldForumBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SpankBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BotALot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "lwp-trivial/1.34" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "lwp-trivial" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Wget/1.6" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BunnySlippers" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Microsoft URL Control - 6.00.8169" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "URLy Warning" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Wget/1.5.3" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "LinkWalker" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "cosmos" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "moget" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "hloader" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "humanlinks" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "LinkextractorPro" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Offline Explorer" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Mata Hari" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "LexiBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Web Image Collector" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "The Intraformant" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "True_Robot/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "True_Robot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BlowFish/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "JennyBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "MIIxpc/4.2" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BuiltBotTough" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "ProPowerBot/2.14" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BackDoorBot/1.0" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "toCrawl/UrlDispatcher" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WebEnhancer" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "TightTwatBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "suzuran" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "VCI WebViewer VCI WebViewer Win32" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "VCI" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Szukacz/1.4" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "QueryN Metasearch" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Openfind data gathere" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Openfind" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Xenu's Link Sleuth 1.1c" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Xenu's" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Zeus" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "RepoMonkey Bait & Tackle/v1.01" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "RepoMonkey" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Zeus 32297 Webster Pro V2.9 Win32" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Webster Pro" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "EroCrawler" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "LinkScan/8.1a Unix" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Keyword Density/0.9" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Kenjin Spider" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Cegbfeieh" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "360Spider" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BUbiNG" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "YandexBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CCBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "2ip bot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "GPTBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "nekst" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "AhrefsBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "MegaIndex.ru" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "DotBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SeznamBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "PetalBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "DataForSeoBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "serpstatbot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "MJ12bot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "DomainStatsBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "LinkpadBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Go-http-client" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "python-requests" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "curl" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "libcurl" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "scrapy" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "HeadlessChrome" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "PhantomJS" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SiteAuditBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Screaming Frog" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Nutch" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Apache-HttpClient" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "okhttp" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Riddler" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "ZoominfoBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Clickagy" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "MauiBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SurdotlyBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Cliqzbot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SemrushBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BLEXBot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "MegaIndex" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Screaming Frog SEO Spider" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Netcraft Web Server Survey" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Wappalyzer" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "BuiltWith" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WhatCMS" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CMSmap" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Nikto" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "sqlmap" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "w3af" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "OWASP ZAP" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Burp Suite" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Acunetix" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Nessus" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "OpenVAS" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Qualys" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Rapid7" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Tenable" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Veracode" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WhiteHat Security" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "IBM Security AppScan" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "HP WebInspect" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Checkmarx" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Fortify" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SonarQube" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "RIPS" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Bandit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Brakeman" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "ESLint" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "JSHint" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "TSLint" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Pylint" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Flake8" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "mypy" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "black" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "isort" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "autopep8" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "yapf" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "bandit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "safety" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "pipenv check" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "npm audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "yarn audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "bundle audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "composer audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "cargo audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "go mod audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "dotnet list package --vulnerable" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "nuget audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "pip-audit" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "safety check" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "snyk test" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "OWASP Dependency Check" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "RetireJS" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "NSP" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Node Security Platform" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Gemnasium" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "VersionEye" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Libraries.io" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "David-dm" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Greenkeeper" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Renovate" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Dependabot" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "WhiteSource" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Black Duck" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "FOSSA" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Sonatype Nexus" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "JFrog Xray" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Twistlock" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Aqua Security" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Sysdig" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Falco" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Cilium" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Istio" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Linkerd" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Consul Connect" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Vault" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Terraform" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Ansible" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Chef" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Puppet" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "SaltStack" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Kubernetes" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Docker" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Podman" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "containerd" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "CRI-O" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "rkt" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "LXC" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "LXD" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "systemd-nspawn" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "OpenVZ" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Proxmox" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "VMware" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "VirtualBox" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "QEMU" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "KVM" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Xen" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Hyper-V" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Parallels" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Vagrant" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Packer" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Nomad" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Consul" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Boundary" bad_bot

# BlockBot Rule
SetEnvIfNoCase User-Agent "Waypoint" bad_bot
