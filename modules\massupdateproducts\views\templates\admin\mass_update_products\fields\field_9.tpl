{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<select {$attr|strval} style="width: 300px;" class="{$class_mass|strval}" send-name="{$name|strval}" data-default="{$value|intval}">
    {if $select}
	{foreach $select as $id => $option}
	    <option send-name="{$name|strval}" {if isset($extra[$id])} extra="{$extra[$id]|strval}" {/if} value="{$id|strval}" {if $value eq $id} selected {/if}>{(str_repeat('&nbsp;', 2*$option['level_depth']))|strval}{$option['display_name']|strval}</option>
	{/foreach}
    {/if}
</select>