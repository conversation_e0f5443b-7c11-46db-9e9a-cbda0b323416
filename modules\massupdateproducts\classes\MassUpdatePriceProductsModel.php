<?php

/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdatePriceProductsModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdatePriceProductsModel');
        parent::__construct($module, $object, $context);
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product) {
                $result[$id_product] = array(
                    'error' => true,
                    'message' => ''
                );
                $product = new Product($id_product);
				$product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;

                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                $type_change = (int)Tools::getValue('type_change', 0);
                $value_to_change = (float)Tools::getValue('value_to_change', 0);
                $method = (int)Tools::getValue('method', 0);
                $options = (int)Tools::getValue('options', 0);
				$options_round = (int)Tools::getValue('options_round', 0);

                if (!$method) {
                    $new_price = ($product->price * ($value_to_change / 100));
                } else {
                    $new_price = $value_to_change;
                }

                if ($options == 0 || $options == 2) {
                    if ($type_change)
                        $product->price += $new_price;
                    else
                        $product->price -= $new_price;
					
					if ($options_round == 1)
					{						
						$rate = ($product->rate / 100) + 1;
						$price_brutto = round($product->price * $rate, 0);
						$product->price = round($price_brutto / $rate, 4);
					}
                }

                if ($options == 1 || $options == 2) {
                    $combinations = $product->getAttributeCombinations(Context::getContext()->language->id);

                    if (!empty($combinations)) {
                        foreach ($combinations as $comb) {
                            $c = new Combination($comb['id_product_attribute']);

                            if (!$method) {
                                $new_price = ($c->price * ($value_to_change / 100));
                            } else {
                                $new_price = $value_to_change;
                            }

                            if($type_change){
                                $c->price += $new_price;
                            } else {
                                $c->price -= $new_price;
                            }
							if ($options_round == 1)
							{
								$rate = ($product->rate / 100) + 1;
								$price_brutto = round($c->price * $rate, 0);
								$c->price = round($price_brutto / $rate, 4);
							}
                            $c->update();
                        }
                    }
                }

                $errors = $product->validateFields(false, true);
                $errors2 = $product->validateFieldsLang(false, true);
                if ($errors !== true || $errors2 !== true) {
                    if ($errors !== true)
                        $result[$id_product]['message'] = '<p style="color: #FFF;">'.(is_bool($errors) ?
                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)).'</p>';
                    if ($errors2 !== true)
                        $result[$id_product]['message'] = '<p style="color: #FFF;">'.(is_bool($errors2) ?
                                $this->module->l('Validate error', $this->languages_name) : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)).'</p>';
                    continue;
                }
                else {
                    if ($product->update()) {
                        $result[$id_product]['message'] = $this->module->l('Product saved', $this->languages_name).': '.$product->name[$this->context->language->id];
                        $result[$id_product]['error'] = false;
                    } else {
                        $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                        continue;
                    }
                }
            } else
            $end = true;

        return array(
            'result' => $result,
            'end' => $end
        );
    }

    public function displayCombination(&$product, &$combination)
    {
        if (!parent::displayCombination($product, $combination))
            return '';
    }

    public function display($result)
    {
        $return = array();
        $return['table'] = false;
        $return['product_count'] = $result['datas']['product_count'];
        return $result;
    }

    public function extra()
    {
        return Context::getContext()->smarty->createTemplate($this->module->getLocalPath().'views/templates/admin/mass_update_products/fields/change_price.tpl')->fetch();
    }
}
