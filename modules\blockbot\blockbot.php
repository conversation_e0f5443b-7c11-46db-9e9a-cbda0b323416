<?php
/**
 * BlockBot Module
 * 
 * <AUTHOR> IT
 * @copyright 2025
 * @license   Commercial
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class BlockBot extends Module
{
    public function __construct()
    {
        $this->name = 'blockbot';
        $this->tab = 'administration';
        $this->version = '1.0.0';
        $this->author = 'Kardo IT';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '*******',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('BlockBot - Ochrona przed Botami');
        $this->description = $this->l('Zaawansowana ochrona przed botami, scraperami i podejrzanym ruchem z automatycznym blokowaniem i monitoringiem.');
        $this->confirmUninstall = $this->l('Czy na pewno chcesz odinstalować ten moduł?');

        if (!Configuration::get('BLOCKBOT_THRESHOLD_IP')) {
            $this->warning = $this->l('Nie skonfigurowano progów');
        }
    }

    public function install()
    {
        if (Shop::isFeatureActive()) {
            Shop::setContext(Shop::CONTEXT_ALL);
        }

        return parent::install() &&
            $this->installTab() &&
            Configuration::updateValue('BLOCKBOT_THRESHOLD_IP', 200) &&
            Configuration::updateValue('BLOCKBOT_THRESHOLD_USERAGENT', 200) &&
            Configuration::updateValue('BLOCKBOT_RAPID_FIRE_IP', 30) &&
            Configuration::updateValue('BLOCKBOT_RAPID_FIRE_AGENT', 30) &&
            Configuration::updateValue('BLOCKBOT_ENABLED', 1) &&
            Configuration::updateValue('BLOCKBOT_AUTO_BLOCK', 1) &&
            Configuration::updateValue('BLOCKBOT_BLOCK_EMPTY_AGENT', 1) &&
            Configuration::updateValue('BLOCKBOT_CLEANUP_MONTHS', 3) &&
            Configuration::updateValue('BLOCKBOT_EMAIL_NOTIFICATIONS', 0) &&
            Configuration::updateValue('BLOCKBOT_EMAIL_ADDRESSES', Configuration::get('PS_SHOP_EMAIL')) &&
            $this->createTables() &&
            $this->addKnownBadBots();
    }

    public function uninstall()
    {
        return parent::uninstall() &&
            $this->uninstallTab() &&
            $this->dropTables() &&
            Configuration::deleteByName('BLOCKBOT_THRESHOLD_IP') &&
            Configuration::deleteByName('BLOCKBOT_THRESHOLD_USERAGENT') &&
            Configuration::deleteByName('BLOCKBOT_ENABLED') &&
            Configuration::deleteByName('BLOCKBOT_AUTO_BLOCK') &&
            Configuration::deleteByName('BLOCKBOT_BLOCK_EMPTY_AGENT') &&
            Configuration::deleteByName('BLOCKBOT_CLEANUP_MONTHS') &&
            Configuration::deleteByName('BLOCKBOT_CRON_KEY') &&
            Configuration::deleteByName('BLOCKBOT_LAST_CLEANUP') &&
            Configuration::deleteByName('BLOCKBOT_EMAIL_NOTIFICATIONS') &&
            Configuration::deleteByName('BLOCKBOT_EMAIL_ADDRESSES') &&
            Configuration::deleteByName('BLOCKBOT_LAST_ALERT') &&
            Configuration::deleteByName('BLOCKBOT_RAPID_FIRE_IP') &&
            Configuration::deleteByName('BLOCKBOT_RAPID_FIRE_AGENT');
    }

    private function installTab()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminBlockBot';
        $tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'Block Bot';
        }
        $tab->id_parent = (int)Tab::getIdFromClassName('AdminTools');
        $tab->module = $this->name;
        return $tab->add();
    }

    private function uninstallTab()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminBlockBot');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            return $tab->delete();
        }
        return true;
    }

    private function createTables()
    {
        $sql = array();

        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'blockbot_logs` (
            `id_log` int(11) NOT NULL AUTO_INCREMENT,
            `ip_address` varchar(45) NOT NULL,
            `user_agent` text,
            `referer` text,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_log`),
            KEY `ip_address` (`ip_address`),
            KEY `date_add` (`date_add`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'blockbot_blocked` (
            `id_blocked` int(11) NOT NULL AUTO_INCREMENT,
            `type` enum("ip","user_agent") NOT NULL,
            `value` text NOT NULL,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_blocked`),
            KEY `type` (`type`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        foreach ($sql as $query) {
            if (Db::getInstance()->execute($query) == false) {
                return false;
            }
        }

        return true;
    }

    private function dropTables()
    {
        $sql = array();
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'blockbot_logs`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'blockbot_blocked`';

        foreach ($sql as $query) {
            if (Db::getInstance()->execute($query) == false) {
                return false;
            }
        }

        return true;
    }



    private function addKnownBadBots()
    {
        $knownBadBots = $this->loadBadBots();

        foreach ($knownBadBots as $userAgent) {
            // Check if already exists in .htaccess
            $htaccessFile = _PS_ROOT_DIR_ . '/.htaccess';
            if (file_exists($htaccessFile)) {
                $content = file_get_contents($htaccessFile);
                if (strpos($content, 'SetEnvIfNoCase User-Agent "' . $userAgent . '"') !== false) {
                    continue; // Already exists
                }
            }

            // Add to .htaccess
            $this->addToHtaccess('SetEnvIfNoCase User-Agent "' . $userAgent . '" bad_bot');

            // Add to database
            $sql = 'INSERT IGNORE INTO `' . _DB_PREFIX_ . 'blockbot_blocked`
                    (type, value, date_add)
                    VALUES ("user_agent", "' . pSQL($userAgent) . '", NOW())';
            Db::getInstance()->execute($sql);
        }

        // Add the deny rule for bad_bot
        $this->addToHtaccess('Order Allow,Deny' . "\n" . 'Allow from all' . "\n" . 'Deny from env=bad_bot');

        return true;
    }

    private function loadBadBots()
    {
        $badBotsFile = $this->getLocalPath() . 'data/bad_bots.php';
        if (file_exists($badBotsFile)) {
            return include $badBotsFile;
        }
        return [];
    }

    public function isGoodBot($userAgent)
    {
        $goodBots = $this->loadGoodBots();

        foreach ($goodBots as $goodBot) {
            if (stripos($userAgent, $goodBot) !== false) {
                return true;
            }
        }

        return false;
    }

    private function loadGoodBots()
    {
        $goodBotsFile = $this->getLocalPath() . 'data/good_bots.php';
        if (file_exists($goodBotsFile)) {
            return include $goodBotsFile;
        }
        return [];
    }

    public function getGoodBotWarning($userAgent)
    {
        if ($this->isGoodBot($userAgent)) {
            $botType = 'unknown';

            if (stripos($userAgent, 'Google') !== false) {
                $botType = 'Google';
            } elseif (stripos($userAgent, 'bing') !== false || stripos($userAgent, 'msn') !== false) {
                $botType = 'Bing/Microsoft';
            } elseif (stripos($userAgent, 'Yahoo') !== false || stripos($userAgent, 'Slurp') !== false) {
                $botType = 'Yahoo';
            } elseif (stripos($userAgent, 'facebook') !== false) {
                $botType = 'Facebook';
            } elseif (stripos($userAgent, 'Twitter') !== false) {
                $botType = 'Twitter';
            } elseif (stripos($userAgent, 'LinkedIn') !== false) {
                $botType = 'LinkedIn';
            } elseif (stripos($userAgent, 'Apple') !== false) {
                $botType = 'Apple';
            } elseif (stripos($userAgent, 'DuckDuck') !== false) {
                $botType = 'DuckDuckGo';
            } elseif (stripos($userAgent, 'Uptime') !== false || stripos($userAgent, 'Ping') !== false) {
                $botType = 'monitoring service';
            }

            return [
                'is_good_bot' => true,
                'bot_type' => $botType,
                'warning' => sprintf(
                    'WARNING: This appears to be a legitimate %s bot. Blocking it may negatively impact your SEO and search engine visibility!',
                    $botType
                )
            ];
        }

        return ['is_good_bot' => false];
    }

    public function isStandardBrowser($userAgent)
    {
        $standardBrowsers = [
            'Chrome/',
            'Firefox/',
            'Safari/',
            'Edge/',
            'Opera/',
            'Internet Explorer',
            'MSIE'
        ];

        foreach ($standardBrowsers as $browser) {
            if (stripos($userAgent, $browser) !== false) {
                return true;
            }
        }

        return false;
    }





    public function getContent()
    {
        // Redirect to the BlockBot admin controller
        $adminUrl = $this->context->link->getAdminLink('AdminBlockBot');
        Tools::redirectAdmin($adminUrl);
    }

    public function getClientIp()
    {
        $ipaddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        } else {
            $ipaddress = 'UNKNOWN';
        }
   
        return $ipaddress;
    }

    public function logVisit($ip, $userAgent, $referer)
    {
        // Always log the visit - we need all visits for proper bot detection
        $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'blockbot_logs`
                (ip_address, user_agent, referer, date_add)
                VALUES ("' . pSQL($ip) . '", "' . pSQL($userAgent) . '", "' . pSQL($referer) . '", NOW())';

        return Db::getInstance()->execute($sql);
    }

    public function getLogData($limit = 1000, $offset = 0)
    {
        $sql = 'SELECT ip_address as ip, user_agent, referer, date_add as time
                FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                ORDER BY date_add DESC
                LIMIT ' . (int)$limit . ' OFFSET ' . (int)$offset;

        return Db::getInstance()->executeS($sql);
    }

    public function getLogCount()
    {
        $sql = 'SELECT COUNT(*) as total FROM `' . _DB_PREFIX_ . 'blockbot_logs`';
        $result = Db::getInstance()->getRow($sql);
        return $result ? (int)$result['total'] : 0;
    }

    public function getIpStats()
    {
        $sql = 'SELECT
                    ip_address as ip,
                    COUNT(*) as count,
                    MAX(date_add) as last_visit,
                    GROUP_CONCAT(DISTINCT user_agent SEPARATOR "|||") as user_agents
                FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                GROUP BY ip_address
                ORDER BY count DESC';

        $results = Db::getInstance()->executeS($sql);
        $ipStats = [];

        foreach ($results as $row) {
            $ipStats[] = [
                'ip' => $row['ip'],
                'count' => (int)$row['count'],
                'last_visit' => $row['last_visit'],
                'user_agents' => $row['user_agents'] ? explode('|||', $row['user_agents']) : []
            ];
        }

        return $ipStats;
    }

    public function getUserAgentStats()
    {
        $sql = 'SELECT
                    user_agent,
                    COUNT(*) as count,
                    MAX(date_add) as last_visit,
                    GROUP_CONCAT(DISTINCT ip_address SEPARATOR "|||") as ips
                FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                GROUP BY user_agent
                ORDER BY count DESC';

        $results = Db::getInstance()->executeS($sql);
        $agentStats = [];

        foreach ($results as $row) {
            $agentStats[] = [
                'user_agent' => $row['user_agent'],
                'count' => (int)$row['count'],
                'last_visit' => $row['last_visit'],
                'ips' => $row['ips'] ? explode('|||', $row['ips']) : []
            ];
        }

        return $agentStats;
    }

    public function clearLogs()
    {
        $sql = 'TRUNCATE TABLE `' . _DB_PREFIX_ . 'blockbot_logs`';
        return Db::getInstance()->execute($sql);
    }

    public function deleteLogEntry($id)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'blockbot_logs` WHERE id_log = ' . (int)$id;
        return Db::getInstance()->execute($sql);
    }

    public function deleteLogsByIp($ip)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'blockbot_logs` WHERE ip_address = "' . pSQL($ip) . '"';
        return Db::getInstance()->execute($sql);
    }

    public function deleteLogsByUserAgent($userAgent)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'blockbot_logs` WHERE user_agent = "' . pSQL($userAgent) . '"';
        return Db::getInstance()->execute($sql);
    }

    public function cleanOldLogs($months = 3)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                WHERE date_add < DATE_SUB(NOW(), INTERVAL ' . (int)$months . ' MONTH)';
        return Db::getInstance()->execute($sql);
    }

    public function blockIp($ip)
    {
        // Check if trying to block own IP
        $currentIp = $this->getClientIp();
        if ($ip === $currentIp) {
            return false; // Don't allow blocking own IP
        }

        // Add to database
        $sql = 'INSERT IGNORE INTO `' . _DB_PREFIX_ . 'blockbot_blocked`
                (type, value, date_add)
                VALUES ("ip", "' . pSQL($ip) . '", NOW())';

        $dbResult = Db::getInstance()->execute($sql);

        // Add to .htaccess and robots.txt
        return $dbResult &&
               $this->addToHtaccess("deny from $ip") &&
               $this->addToRobots("User-agent: *\nDisallow: / # Blocked IP: $ip");
    }

    public function blockUserAgent($userAgent)
    {
        // Add to database
        $sql = 'INSERT IGNORE INTO `' . _DB_PREFIX_ . 'blockbot_blocked`
                (type, value, date_add)
                VALUES ("user_agent", "' . pSQL($userAgent) . '", NOW())';

        $dbResult = Db::getInstance()->execute($sql);

        // Add to .htaccess and robots.txt
        $escapedAgent = str_replace('"', '\"', $userAgent);
        return $dbResult &&
               $this->addToHtaccess("RewriteCond %{HTTP_USER_AGENT} \"$escapedAgent\" [NC]\nRewriteRule .* - [F,L]") &&
               $this->addToRobots("User-agent: $userAgent\nDisallow: /");
    }

    private function addToHtaccess($rule)
    {
        $htaccessFile = _PS_ROOT_DIR_ . '/.htaccess';

        // Create backup
        if (file_exists($htaccessFile)) {
            copy($htaccessFile, $htaccessFile . '.blockbot.backup.' . date('Y-m-d-H-i-s'));
        }

        $content = file_exists($htaccessFile) ? file_get_contents($htaccessFile) : '';
        $content .= "\n$rule";

        return file_put_contents($htaccessFile, $content) !== false;
    }

    private function addToRobots($rule)
    {
        $robotsFile = _PS_ROOT_DIR_ . '/robots.txt';

        // Create backup
        if (file_exists($robotsFile)) {
            copy($robotsFile, $robotsFile . '.blockbot.backup.' . date('Y-m-d-H-i-s'));
        }

        $content = file_exists($robotsFile) ? file_get_contents($robotsFile) : '';
        $content .= "\n$rule";

        return file_put_contents($robotsFile, $content) !== false;
    }

    public function getBlockedIps()
    {
        $sql = 'SELECT value FROM `' . _DB_PREFIX_ . 'blockbot_blocked`
                WHERE type = "ip" ORDER BY date_add DESC';

        $results = Db::getInstance()->executeS($sql);
        $blocked = [];

        foreach ($results as $row) {
            $blocked[] = $row['value'];
        }

        return $blocked;
    }

    public function getBlockedUserAgents()
    {
        $sql = 'SELECT value FROM `' . _DB_PREFIX_ . 'blockbot_blocked`
                WHERE type = "user_agent" ORDER BY date_add DESC';

        $results = Db::getInstance()->executeS($sql);
        $blocked = [];

        foreach ($results as $row) {
            $blocked[] = $row['value'];
        }

        return $blocked;
    }

    public function unblockIp($ip)
    {
        // Remove from database
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'blockbot_blocked`
                WHERE type = "ip" AND value = "' . pSQL($ip) . '"';

        $dbResult = Db::getInstance()->execute($sql);

        // Remove from .htaccess
        $htaccessFile = _PS_ROOT_DIR_ . '/.htaccess';
        if (file_exists($htaccessFile)) {
            $content = file_get_contents($htaccessFile);
            $pattern = "/\ndeny from " . preg_quote($ip, '/') . "/";
            $newContent = preg_replace($pattern, '', $content);
            file_put_contents($htaccessFile, $newContent);
        }

        // Remove from robots.txt
        $robotsFile = _PS_ROOT_DIR_ . '/robots.txt';
        if (file_exists($robotsFile)) {
            $robotsContent = file_get_contents($robotsFile);
            $robotsPattern = "/\nUser-agent: \*\nDisallow: \/ # Blocked IP: " . preg_quote($ip, '/') . "/";
            $newRobotsContent = preg_replace($robotsPattern, '', $robotsContent);
            file_put_contents($robotsFile, $newRobotsContent);
        }

        return $dbResult;
    }

    public function unblockUserAgent($userAgent)
    {
        // Remove from database
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'blockbot_blocked`
                WHERE type = "user_agent" AND value = "' . pSQL($userAgent) . '"';

        $dbResult = Db::getInstance()->execute($sql);

        // Remove from .htaccess
        $htaccessFile = _PS_ROOT_DIR_ . '/.htaccess';
        if (file_exists($htaccessFile)) {
            $content = file_get_contents($htaccessFile);
            $escapedAgent = preg_quote(str_replace('"', '\"', $userAgent), '/');
            $pattern = "/\nRewriteCond %\{HTTP_USER_AGENT\} \"" . $escapedAgent . "\" \[NC\]\nRewriteRule \.\* - \[F,L\]/";
            $newContent = preg_replace($pattern, '', $content);
            file_put_contents($htaccessFile, $newContent);
        }

        // Remove from robots.txt
        $robotsFile = _PS_ROOT_DIR_ . '/robots.txt';
        if (file_exists($robotsFile)) {
            $robotsContent = file_get_contents($robotsFile);
            $robotsPattern = "/\nUser-agent: " . preg_quote($userAgent, '/') . "\nDisallow: \//";
            $newRobotsContent = preg_replace($robotsPattern, '', $robotsContent);
            file_put_contents($robotsFile, $newRobotsContent);
        }

        return $dbResult;
    }

    public function autoBlockSuspiciousTraffic()
    {
        $blocked = 0;
        $currentIp = $this->getClientIp();

        // Check for rapid-fire attacks (many requests in short time)
        $rapidFireBlocked = $this->checkRapidFireAttacks($currentIp);
        $blocked += $rapidFireBlocked;

        // Check for high-volume attacks (many requests over longer period)
        $highVolumeBlocked = $this->checkHighVolumeAttacks($currentIp);
        $blocked += $highVolumeBlocked;

        return $blocked;
    }

    private function checkRapidFireAttacks($currentIp)
    {
        $blocked = 0;
        $blockedIps = $this->getBlockedIps();
        $blockedAgents = $this->getBlockedUserAgents();

        // Check for IPs with rapid-fire requests in last 1 minute
        $rapidFireIpThreshold = (int)Configuration::get('BLOCKBOT_RAPID_FIRE_IP');
        $rapidFireIpSql = 'SELECT ip_address, COUNT(*) as count
                           FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                           WHERE date_add > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                           GROUP BY ip_address
                           HAVING count >= ' . $rapidFireIpThreshold;

        $rapidFireIps = Db::getInstance()->executeS($rapidFireIpSql);

        foreach ($rapidFireIps as $stat) {
            if (!in_array($stat['ip_address'], $blockedIps) &&
                $stat['ip_address'] !== $currentIp) {
                if ($this->blockIp($stat['ip_address'])) {
                    $blocked++;
                }
            }
        }

        // Check for User Agents with rapid-fire requests in last 1 minute
        $rapidFireAgentThreshold = (int)Configuration::get('BLOCKBOT_RAPID_FIRE_AGENT');
        $rapidFireAgentSql = 'SELECT user_agent, COUNT(*) as count
                              FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                              WHERE date_add > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                              GROUP BY user_agent
                              HAVING count >= ' . $rapidFireAgentThreshold;

        $rapidFireAgents = Db::getInstance()->executeS($rapidFireAgentSql);

        foreach ($rapidFireAgents as $stat) {
            if (!in_array($stat['user_agent'], $blockedAgents) &&
                !$this->isGoodBot($stat['user_agent'])) {
                if ($this->blockUserAgent($stat['user_agent'])) {
                    $blocked++;
                }
            }
        }

        return $blocked;
    }

    private function checkHighVolumeAttacks($currentIp)
    {
        $ipThreshold = (int)Configuration::get('BLOCKBOT_THRESHOLD_IP');
        $agentThreshold = (int)Configuration::get('BLOCKBOT_THRESHOLD_USERAGENT');

        $blocked = 0;
        $blockedIps = $this->getBlockedIps();
        $blockedAgents = $this->getBlockedUserAgents();

        // Check for IPs exceeding threshold in last 24 hours
        $highVolumeIpSql = 'SELECT ip_address, COUNT(*) as count
                            FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                            WHERE date_add > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                            GROUP BY ip_address
                            HAVING count >= ' . (int)$ipThreshold;

        $highVolumeIps = Db::getInstance()->executeS($highVolumeIpSql);

        foreach ($highVolumeIps as $stat) {
            if (!in_array($stat['ip_address'], $blockedIps) &&
                $stat['ip_address'] !== $currentIp) {
                if ($this->blockIp($stat['ip_address'])) {
                    $blocked++;
                }
            }
        }

        // Check for User Agents exceeding threshold in last 24 hours
        $highVolumeAgentSql = 'SELECT user_agent, COUNT(*) as count
                               FROM `' . _DB_PREFIX_ . 'blockbot_logs`
                               WHERE date_add > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                               GROUP BY user_agent
                               HAVING count >= ' . (int)$agentThreshold;

        $highVolumeAgents = Db::getInstance()->executeS($highVolumeAgentSql);

        foreach ($highVolumeAgents as $stat) {
            if (!in_array($stat['user_agent'], $blockedAgents) &&
                !$this->isGoodBot($stat['user_agent'])) {
                if ($this->blockUserAgent($stat['user_agent'])) {
                    $blocked++;
                }
            }
        }

        return $blocked;
    }

    public function getLogStats()
    {
        $sql = 'SELECT
                    COUNT(*) as total_logs,
                    COUNT(DISTINCT ip_address) as unique_ips,
                    COUNT(DISTINCT user_agent) as unique_agents,
                    MIN(date_add) as first_log,
                    MAX(date_add) as last_log
                FROM `' . _DB_PREFIX_ . 'blockbot_logs`';

        $result = Db::getInstance()->getRow($sql);
        return $result ? $result : [
            'total_logs' => 0,
            'unique_ips' => 0,
            'unique_agents' => 0,
            'first_log' => null,
            'last_log' => null
        ];
    }

    public function getSuspiciousActivityAlert()
    {
        $ipThreshold = (int)Configuration::get('BLOCKBOT_THRESHOLD_IP');
        $agentThreshold = (int)Configuration::get('BLOCKBOT_THRESHOLD_USERAGENT');

        $alerts = [];

        // Check for suspicious IPs
        $ipStats = $this->getIpStats();
        $blockedIps = $this->getBlockedIps();

        foreach ($ipStats as $stat) {
            if ($stat['count'] >= $ipThreshold && !in_array($stat['ip'], $blockedIps)) {
                $alerts[] = [
                    'type' => 'ip',
                    'value' => $stat['ip'],
                    'count' => $stat['count'],
                    'threshold' => $ipThreshold
                ];
            }
        }

        // Check for suspicious User Agents
        $agentStats = $this->getUserAgentStats();
        $blockedAgents = $this->getBlockedUserAgents();

        foreach ($agentStats as $stat) {
            if ($stat['count'] >= $agentThreshold && !in_array($stat['user_agent'], $blockedAgents)) {
                $alerts[] = [
                    'type' => 'user_agent',
                    'value' => $stat['user_agent'],
                    'count' => $stat['count'],
                    'threshold' => $agentThreshold
                ];
            }
        }

        return $alerts;
    }

    public function exportLogData($format = 'csv', $limit = 10000)
    {
        $data = $this->getLogData($limit);

        if ($format === 'csv') {
            $output = "Time,IP Address,Referer,User Agent\n";
            foreach ($data as $entry) {
                $output .= sprintf(
                    '"%s","%s","%s","%s"' . "\n",
                    $entry['time'],
                    $entry['ip'],
                    str_replace('"', '""', $entry['referer']),
                    str_replace('"', '""', $entry['user_agent'])
                );
            }
            return $output;
        }

        if ($format === 'json') {
            return json_encode($data, JSON_PRETTY_PRINT);
        }

        return false;
    }

    public function sendSuspiciousTrafficAlert($ipStats, $agentStats)
    {
        if (!Configuration::get('BLOCKBOT_EMAIL_NOTIFICATIONS')) {
            return false;
        }

        $emailAddresses = Configuration::get('BLOCKBOT_EMAIL_ADDRESSES');
        if (!$emailAddresses) {
            return false;
        }

        $addresses = array_map('trim', explode(',', $emailAddresses));
        $ipThreshold = (int)Configuration::get('BLOCKBOT_THRESHOLD_IP');
        $agentThreshold = (int)Configuration::get('BLOCKBOT_THRESHOLD_USERAGENT');

        $suspiciousIps = [];
        $suspiciousAgents = [];

        // Find suspicious IPs
        foreach ($ipStats as $stat) {
            if ($stat['count'] >= $ipThreshold) {
                $suspiciousIps[] = $stat;
            }
        }

        // Find suspicious User Agents
        foreach ($agentStats as $stat) {
            if ($stat['count'] >= $agentThreshold) {
                $suspiciousAgents[] = $stat;
            }
        }

        if (empty($suspiciousIps) && empty($suspiciousAgents)) {
            return false;
        }

        $shopName = Configuration::get('PS_SHOP_NAME');
        $shopUrl = Tools::getShopDomainSsl(true);

        $subject = sprintf('[%s] Suspicious Bot Traffic Alert', $shopName);

        $message = "Dear Administrator,\n\n";
        $message .= "BlockBot has detected suspicious traffic on your website: {$shopUrl}\n\n";

        if (!empty($suspiciousIps)) {
            $message .= "SUSPICIOUS IP ADDRESSES (threshold: {$ipThreshold} visits):\n";
            $message .= str_repeat("-", 50) . "\n";
            foreach ($suspiciousIps as $ip) {
                $message .= "IP: {$ip['ip']} - {$ip['count']} visits (last: {$ip['last_visit']})\n";
            }
            $message .= "\n";
        }

        if (!empty($suspiciousAgents)) {
            $message .= "SUSPICIOUS USER AGENTS (threshold: {$agentThreshold} visits):\n";
            $message .= str_repeat("-", 50) . "\n";
            foreach ($suspiciousAgents as $agent) {
                $userAgent = strlen($agent['user_agent']) > 80 ?
                    substr($agent['user_agent'], 0, 80) . '...' :
                    $agent['user_agent'];
                $message .= "Agent: {$userAgent} - {$agent['count']} visits (last: {$agent['last_visit']})\n";
            }
            $message .= "\n";
        }

        $message .= "You can review and block these entries in your admin panel:\n";
        $message .= "Go to: Modules > Module Manager > BlockBot > Configure\n\n";
        $message .= "This is an automated message from BlockBot module.\n";
        $message .= "Time: " . date('Y-m-d H:i:s') . "\n";

        $sent = false;
        foreach ($addresses as $email) {
            if (Validate::isEmail($email)) {
                if (Mail::Send(
                    (int)Configuration::get('PS_LANG_DEFAULT'),
                    'blockbot_alert',
                    $subject,
                    [
                        '{shop_name}' => $shopName,
                        '{shop_url}' => $shopUrl,
                        '{message}' => $message,
                        '{suspicious_ips}' => count($suspiciousIps),
                        '{suspicious_agents}' => count($suspiciousAgents),
                        '{ip_threshold}' => $ipThreshold,
                        '{agent_threshold}' => $agentThreshold
                    ],
                    $email,
                    null,
                    Configuration::get('PS_SHOP_EMAIL'),
                    $shopName,
                    null,
                    null,
                    _PS_MODULE_DIR_ . $this->name . '/mails/'
                )) {
                    $sent = true;
                }
            }
        }

        return $sent;
    }

    public function checkAndSendAlerts()
    {
        // Check if we should send alerts (not more than once per hour)
        $lastAlert = Configuration::get('BLOCKBOT_LAST_ALERT');
        if ($lastAlert && (time() - strtotime($lastAlert)) < 3600) {
            return false;
        }

        $ipStats = $this->getIpStats();
        $agentStats = $this->getUserAgentStats();

        if ($this->sendSuspiciousTrafficAlert($ipStats, $agentStats)) {
            Configuration::updateValue('BLOCKBOT_LAST_ALERT', date('Y-m-d H:i:s'));
            return true;
        }

        return false;
    }
}
