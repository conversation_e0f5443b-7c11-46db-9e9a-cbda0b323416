{**
 * Simplified image management template for inspiration module
 *}

<div class="panel">
    <div class="panel-heading">
        {l s='Images and Products (upload after add)' d='Modules.Webixapreview.Admin'}
    </div>

    <div class="form-wrapper">
        <!-- Simple file upload -->
        <div class="form-group">
            <label class="control-label col-lg-3">
                {l s='Add images' d='Modules.Webixapreview.Admin'}
            </label>
            <div class="col-lg-9">
                <input id="inspiration-image-input" type="file" name="images[]" multiple class="form-control">
                <p class="help-block">{l s='Select one or more images to upload' d='Modules.Webixapreview.Admin'}</p>
            </div>
        </div>

        <!-- Images list with product association -->
        <div class="form-group">
            <label class="control-label col-lg-3">
                {l s='Images and associated products' d='Modules.Webixapreview.Admin'}
            </label>
            <div class="col-lg-9">
                <div id="inspiration-images-container">
                    {if isset($inspiration_images) && count($inspiration_images) > 0}
                        {foreach from=$inspiration_images item=image}
                            <div class="row">
                                <div class="col-xs-12 image-container" data-id="{$image.id_inspiration_image}">
                                    <div class="panel {if $image.main}panel-success{else}panel-default{/if}">
                                        <div class="panel-heading">
                                            <div class="btn-group pull-right">
                                                <button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown">
                                                    <i class="icon-cog"></i> {l s='Actions' d='Modules.Webixapreview.Admin'} <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a href="#" class="set-main-image" data-id="{$image.id_inspiration_image}">
                                                            <i class="icon-star"></i> {l s='Set as main image' d='Modules.Webixapreview.Admin'}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="delete-image" data-id="{$image.id_inspiration_image}">
                                                            <i class="icon-trash"></i> {l s='Delete image' d='Modules.Webixapreview.Admin'}
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                            {if $image.main}
                                                <span class="label label-success"><i class="icon-check"></i> {l s='Main image' d='Modules.Webixapreview.Admin'}</span>
                                            {else}
                                                <span class="label label-default">{l s='Image' d='Modules.Webixapreview.Admin'} #{$image.id_inspiration_image}</span>
                                            {/if}
                                        </div>
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-md-12 text-center">
                                                    <div class="image-preview-container" style="position: relative; display: inline-block;">
                                                        <img src="{$image.url}" class="img-responsive center-block" style="max-height: 300px;">
                                                        {if isset($image.products) && $image.products}
                                                            {foreach from=$image.products item=product}
                                                                <div class="product-marker draggable"
                                                                     data-product-id="{$product.id_product}"
                                                                     data-image-id="{$image.id_inspiration_image}"
                                                                     style="position: absolute;
                                                                            left: {$product.position_x}%;
                                                                            top: {$product.position_y}%;
                                                                            width: 20px;
                                                                            height: 20px;
                                                                            background-color: rgba(255, 0, 0, 0.7);
                                                                            border-radius: 50%;
                                                                            transform: translate(-50%, -50%);
                                                                            cursor: move;
                                                                            z-index: 10;"
                                                                     title="{$product.name|escape:'html':'UTF-8'} - Drag to reposition">
                                                                </div>
                                                            {/foreach}
                                                        {/if}
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="product-association">
                                                        <h4>{l s='Associated products:' d='Modules.Webixapreview.Admin'}</h4>
                                                        <div class="associated-products-list" data-image-id="{$image.id_inspiration_image}">
                                                            {if isset($image.products) && $image.products}
                                                                {foreach from=$image.products item=product}
                                                                    <div class="product-item" data-product-id="{$product.id_product}">
                                                                        <div class="row">
                                                                            <div class="col-xs-6">
                                                                                {$product.name|escape:'html':'UTF-8'}
                                                                            </div>
                                                                            <div class="col-xs-6 text-right">
                                                                                <div class="btn-group">
                                                                                    <button type="button" class="btn btn-default btn-xs edit-product-position"
                                                                                            data-product-id="{$product.id_product}"
                                                                                            data-image-id="{$image.id_inspiration_image}"
                                                                                            data-position-x="{$product.position_x}"
                                                                                            data-position-y="{$product.position_y}">
                                                                                        <i class="icon-pencil"></i> {l s='Position' d='Modules.Webixapreview.Admin'} ({$product.position_x}%, {$product.position_y}%)
                                                                                    </button>
                                                                                    <button type="button" class="btn btn-danger btn-xs remove-product"
                                                                                            data-product-id="{$product.id_product}"
                                                                                            data-image-id="{$image.id_inspiration_image}">
                                                                                        <i class="icon-trash"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                {/foreach}
                                                            {else}
                                                                <p class="text-muted">{l s='No products associated with this image' d='Modules.Webixapreview.Admin'}</p>
                                                            {/if}
                                                        </div>
                                                        <div class="add-product-form">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control product-search" placeholder="{l s='Search for a product' d='Modules.Webixapreview.Admin'}" data-image-id="{$image.id_inspiration_image}">
                                                                <span class="input-group-btn">
                                                                    <button class="btn btn-default add-product-btn" type="button" data-image-id="{$image.id_inspiration_image}">
                                                                        <i class="icon-plus"></i> {l s='Add' d='Modules.Webixapreview.Admin'}
                                                                    </button>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/foreach}
                    {else}
                        <div class="alert alert-info">
                            {l s='No images uploaded yet. Use the file input above to add images.' d='Modules.Webixapreview.Admin'}
                        </div>
                    {/if}
                </div>
                <input type="hidden" name="main_image_id" id="main_image_id" value="{if isset($main_image_id)}{$main_image_id}{/if}">
                <input type="hidden" name="product_associations" id="product_associations" value="">
                <input type="hidden" name="current_inspiration_id" id="current_inspiration_id" value="{if isset($id_inspiration)}{$id_inspiration|intval}{else}0{/if}">
            </div>
        </div>
    </div>
</div>

<!-- Position modal -->
<div class="modal fade" id="position-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                <h4 class="modal-title">{l s='Set product position' d='Modules.Webixapreview.Admin'}</h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="control-label col-sm-4">{l s='Position X (%)' d='Modules.Webixapreview.Admin'}</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input type="number" class="form-control" id="position-x" min="0" max="100" value="50">
                                <div class="input-group-addon">%</div>
                            </div>
                            <p class="help-block">{l s='Horizontal position (0% = left, 100% = right)' d='Modules.Webixapreview.Admin'}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-4">{l s='Position Y (%)' d='Modules.Webixapreview.Admin'}</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input type="number" class="form-control" id="position-y" min="0" max="100" value="50">
                                <div class="input-group-addon">%</div>
                            </div>
                            <p class="help-block">{l s='Vertical position (0% = top, 100% = bottom)' d='Modules.Webixapreview.Admin'}</p>
                        </div>
                    </div>
                </div>
                <input type="hidden" id="modal-product-id">
                <input type="hidden" id="modal-image-id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{l s='Cancel' d='Modules.Webixapreview.Admin'}</button>
                <button type="button" class="btn btn-primary" id="save-position">{l s='Save' d='Modules.Webixapreview.Admin'}</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    var productAssociations = {};
    $('.associated-products-list').each(function() {
        var imageId = $(this).data('image-id');
        productAssociations[imageId] = [];

        $(this).find('.product-item').each(function() {
            var productId = $(this).data('product-id');
            var positionX = $(this).find('.edit-product-position').data('position-x') || 50;
            var positionY = $(this).find('.edit-product-position').data('position-y') || 50;
            var productName = $(this).find('.col-xs-8').text().trim();

            productAssociations[imageId].push({
                id_product: productId,
                position_x: positionX,
                position_y: positionY,
                name: productName
            });
        });
    });

    // Update hidden field with product associations
    function updateProductAssociationsField() {
        $('#product_associations').val(JSON.stringify(productAssociations));
    }

    // Function to clear all markers from an image
    function clearImageMarkers(imageId) {
        $('.image-container[data-id="' + imageId + '"] .product-marker').remove();
    }

    // Function to refresh product list for an image based on productAssociations data
    function refreshImageProductList(imageId) {
        var productsList = $('.associated-products-list[data-image-id="' + imageId + '"]');



        productsList.empty();

        if (productAssociations[imageId] && productAssociations[imageId].length > 0) {
            // Remove duplicates from productAssociations first
            var uniqueProducts = [];
            var seenProducts = {};

            productAssociations[imageId].forEach(function(product) {
                if (!seenProducts[product.id_product]) {
                    seenProducts[product.id_product] = true;
                    uniqueProducts.push(product);
                } else {
                    console.log('Removing duplicate product:', product.id_product);
                }
            });

            // Update productAssociations with unique products
            productAssociations[imageId] = uniqueProducts;

            uniqueProducts.forEach(function(product) {
                // Get product name from stored data, existing item, or use fallback
                var productName = product.name || 'Product #' + product.id_product;
                if (!product.name) {
                    var existingItem = $('.product-item[data-product-id="' + product.id_product + '"]');
                    if (existingItem.length) {
                        productName = existingItem.find('.col-xs-8').text().trim();
                        // Store the name for future use
                        product.name = productName;
                    }
                }

                var productHtml = '<div class="product-item" data-product-id="' + product.id_product + '">' +
                    '<div class="row">' +
                    '<div class="col-xs-8">' + productName + '</div>' +
                    '<div class="col-xs-4 text-right">' +
                    '<div class="btn-group">' +
                    '<button type="button" class="btn btn-default btn-xs edit-product-position" ' +
                    'data-product-id="' + product.id_product + '" ' +
                    'data-image-id="' + imageId + '" ' +
                    'data-position-x="' + product.position_x + '" ' +
                    'data-position-y="' + product.position_y + '">' +
                    '<i class="icon-pencil"></i> {l s='Position' d='Modules.Webixapreview.Admin'} (' + product.position_x + '%, ' + product.position_y + '%)</button>' +
                    '<button type="button" class="btn btn-danger btn-xs remove-product" ' +
                    'data-product-id="' + product.id_product + '" ' +
                    'data-image-id="' + imageId + '">' +
                    '<i class="icon-trash"></i></button>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
                productsList.append(productHtml);
            });
        } else {
            productsList.html('<p class="text-muted">{l s='No products associated with this image' d='Modules.Webixapreview.Admin'}</p>');
        }
    }

    // Function to refresh markers for an image based on productAssociations data
    function refreshImageMarkers(imageId) {
        // Clear existing markers first
        clearImageMarkers(imageId);

        // Add markers from productAssociations data
        if (productAssociations[imageId]) {
            var imageContainer = $('.image-container[data-id="' + imageId + '"] .image-preview-container');
            if (imageContainer.length) {
                productAssociations[imageId].forEach(function(product) {
                    var productName = $('.product-item[data-product-id="' + product.id_product + '"]').find('.col-xs-8').text().trim();
                    var newMarker = $('<div class="product-marker draggable" data-product-id="' + product.id_product + '" data-image-id="' + imageId + '" title="' + productName + '"></div>');
                    newMarker.css({
                        'position': 'absolute',
                        'left': product.position_x + '%',
                        'top': product.position_y + '%',
                        'width': '20px',
                        'height': '20px',
                        'background-color': 'rgba(255, 0, 0, 0.7)',
                        'border-radius': '50%',
                        'transform': 'translate(-50%, -50%)',
                        'cursor': 'move',
                        'z-index': '10'
                    });
                    imageContainer.append(newMarker);
                });

                // Initialize draggable for all markers
                initDraggableMarkers();
            }
        }
    }

    // Function to refresh both product list and markers for an image
    function refreshImageData(imageId) {
        refreshImageProductList(imageId);
        refreshImageMarkers(imageId);
    }
    
    updateProductAssociationsField();

    $('#inspiration-image-input').change(function() {
        if ($(this).val()) {
            // Submit the form to upload images
            var formData = new FormData();
            var files = $(this)[0].files;

            for (var i = 0; i < files.length; i++) {
                formData.append('images[]', files[i]);
            }

            formData.append('ajax', 1);
            formData.append('action', 'UploadImage');            
            var idInspiration = 0;            
            var urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('id_inspiration')) {
                idInspiration = parseInt(urlParams.get('id_inspiration'));                
            }            
            else if ($('#current_inspiration_id').length && parseInt($('#current_inspiration_id').val()) > 0) {
                idInspiration = parseInt($('#current_inspiration_id').val());                
            }            
            else if ({if isset($id_inspiration) && $id_inspiration > 0}true{else}false{/if}) {
                idInspiration = {if isset($id_inspiration)}{$id_inspiration|intval}{else}0{/if};                
            }
            formData.append('id_inspiration', idInspiration);            

            // If this is a new inspiration, we need to save the form first
            if (idInspiration <= 0) {
                // Use the PrestaShop's native showErrorMessage function instead of showWarningMessage
                showErrorMessage('{l s='Please save the inspiration first before uploading images' d='Modules.Webixapreview.Admin'}');
                return;
            }

            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    try {
                        var data = JSON.parse(response);
                        if (data.success) {
                            // Reload the page to show the new images
                            location.reload();
                        } else {
                            showErrorMessage(data.message || '{l s='Failed to upload images' d='Modules.Webixapreview.Admin'}');
                        }
                    } catch (e) {
                        showErrorMessage('{l s='Invalid response from server' d='Modules.Webixapreview.Admin'}');
                    }
                },
                error: function() {
                    showErrorMessage('{l s='Failed to upload images' d='Modules.Webixapreview.Admin'}');
                }
            });
        }
    });

    // Set main image
    $(document).on('click', '.set-main-image', function(e) {
        e.preventDefault();
        var imageId = $(this).data('id');
        $('#main_image_id').val(imageId);

        // Update UI
        $('.image-container .panel').removeClass('panel-success').addClass('panel-default');
        $('.image-container[data-id="' + imageId + '"] .panel').removeClass('panel-default').addClass('panel-success');

        // Update labels
        $('.image-container .label-success').remove();
        $('.image-container .panel-heading .label').addClass('label-default').removeClass('label-success');
        $('.image-container[data-id="' + imageId + '"] .panel-heading .label')
            .removeClass('label-default')
            .addClass('label-success')
            .html('<i class="icon-check"></i> {l s='Main image' d='Modules.Webixapreview.Admin'}');

        showSuccessMessage('{l s='Main image updated' d='Modules.Webixapreview.Admin'}');
    });

    // Delete image
    $(document).on('click', '.delete-image', function(e) {
        e.preventDefault();
        if (confirm('{l s='Are you sure you want to delete this image?' d='Modules.Webixapreview.Admin'}')) {
            var imageId = $(this).data('id');
            var imageContainer = $('.image-container[data-id="' + imageId + '"]');

            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                type: 'POST',
                data: {
                    ajax: 1,
                    action: 'DeleteImage',
                    id_inspiration_image: imageId
                },
                success: function(response) {
                    try {
                        var data = JSON.parse(response);
                        if (data.success) {
                            // Remove the image container
                            imageContainer.remove();

                            // Remove from product associations
                            delete productAssociations[imageId];
                            updateProductAssociationsField();

                            showSuccessMessage('{l s='Image deleted successfully' d='Modules.Webixapreview.Admin'}');
                        } else {
                            showErrorMessage('{l s='Failed to delete image' d='Modules.Webixapreview.Admin'}');
                        }
                    } catch (e) {
                        showErrorMessage('{l s='Invalid response from server' d='Modules.Webixapreview.Admin'}');
                    }
                },
                error: function() {
                    showErrorMessage('{l s='Failed to delete image' d='Modules.Webixapreview.Admin'}');
                }
            });
        }
    });

    // Product search autocomplete
    $('.product-search').autocomplete({
        source: function(request, response) {            
            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                dataType: 'json',
                data: {
                    ajax: 1,
                    action: 'searchProducts',
                    q: request.term,
                    id_lang: {$id_lang|intval}
                },
                success: function(data) {                    
                    response($.map(data, function(item) {
                        return {
                            label: item.name,
                            value: item.name,
                            id: item.id_product
                        };
                    }));
                },
                error: function(xhr, status, error) {
                    console.error('Error searching for products:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            var imageId = $(this).data('image-id');
            var productId = ui.item.id;

            // Check if product already exists for this image
            var exists = false;
            if (productAssociations[imageId]) {
                for (var i = 0; i < productAssociations[imageId].length; i++) {
                    if (productAssociations[imageId][i].id_product == productId) {
                        exists = true;
                        break;
                    }
                }
            }

            // Also check if product exists in the DOM (double check)
            var existsInDOM = $('.product-item[data-product-id="' + productId + '"]').length > 0;

            if (exists || existsInDOM) {
                showErrorMessage('{l s='This product is already associated with this image' d='Modules.Webixapreview.Admin'}');
                console.log('Product already exists - productAssociations:', exists, 'DOM:', existsInDOM);
            } else {
                // Add product to the list
                if (!productAssociations[imageId]) {
                    productAssociations[imageId] = [];
                }

                // Add with default positions and store product name for later use
                productAssociations[imageId].push({
                    id_product: productId,
                    position_x: 50,
                    position_y: 50,
                    name: ui.item.label // Store the name for refreshImageData
                });

                // Refresh product list and markers for this image to avoid duplicates
                refreshImageData(imageId);

                // Update hidden field
                updateProductAssociationsField();

                // Clear the search input
                $(this).val('');

                // Show success message
                showSuccessMessage('{l s='Product added successfully' d='Modules.Webixapreview.Admin'}');
            }

            return false;
        }
    });

    // Add product button (for when autocomplete doesn't trigger select)
    $('.add-product-btn').click(function() {
        var imageId = $(this).data('image-id');
        var searchInput = $('.product-search[data-image-id="' + imageId + '"]');
        var searchTerm = searchInput.val();

        if (searchTerm.length > 0) {
            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                dataType: 'json',
                data: {
                    ajax: 1,
                    action: 'searchProducts',
                    q: searchTerm,
                    id_lang: {$id_lang|intval},
                    limit: 1
                },
                success: function(data) {
                    if (data && data.length > 0) {
                        var product = data[0];

                        // Simulate select from autocomplete
                        searchInput.autocomplete('option', 'select')(null, {
                            item: {
                                label: product.name,
                                value: product.name,
                                id: product.id_product
                            }
                        });
                    } else {
                        showErrorMessage('{l s='No products found matching your search' d='Modules.Webixapreview.Admin'}');
                    }
                }
            });
        }
    });

    // Remove product
    $(document).on('click', '.remove-product', function() {
        var productId = $(this).data('product-id');
        var imageId = $(this).data('image-id');
        var productItem = $(this).closest('.product-item');

        // Remove from storage
        if (productAssociations[imageId]) {
            for (var i = 0; i < productAssociations[imageId].length; i++) {
                if (productAssociations[imageId][i].id_product == productId) {
                    productAssociations[imageId].splice(i, 1);
                    break;
                }
            }

            // Refresh product list and markers for this image to remove duplicates
            refreshImageData(imageId);

            // Update hidden field
            updateProductAssociationsField();
        }
    });
    
    $(document).on('click', '.edit-product-position', function() {
        var productId = $(this).data('product-id');
        var imageId = $(this).data('image-id');
        var positionX = $(this).data('position-x') || 50;
        var positionY = $(this).data('position-y') || 50;

        $('#position-x').val(positionX);
        $('#position-y').val(positionY);
        $('#modal-product-id').val(productId);
        $('#modal-image-id').val(imageId);
        
        $('#position-modal').modal('show');
    });
    
    $('#save-position').click(function() {
        var productId = $('#modal-product-id').val();
        var imageId = $('#modal-image-id').val();
        var positionX = $('#position-x').val();
        var positionY = $('#position-y').val();        
        
        if (productAssociations[imageId]) {
            for (var i = 0; i < productAssociations[imageId].length; i++) {
                if (productAssociations[imageId][i].id_product == productId) {
                    productAssociations[imageId][i].position_x = positionX;
                    productAssociations[imageId][i].position_y = positionY;                    
                    break;
                }
            }
         
            var editButton = $('.edit-product-position[data-product-id="' + productId + '"][data-image-id="' + imageId + '"]');
            editButton
                .data('position-x', positionX)
                .data('position-y', positionY)
                .attr('data-position-x', positionX)
                .attr('data-position-y', positionY);

            editButton.html('<i class="icon-pencil"></i> {l s='Position' d='Modules.Webixapreview.Admin'} (' + positionX + '%, ' + positionY + '%)');

            // Refresh product list and markers for this image to ensure consistency
            refreshImageData(imageId);
            
            updateProductAssociationsField();            
            showSuccessMessage('{l s='Product position updated' d='Modules.Webixapreview.Admin'}');
            $('#position-modal').modal('hide');
        } else {
            console.error('Product associations not found for image ' + imageId);
        }
    });

    // Allow setting position by clicking on the image
    $(document).on('click', '.image-preview-container img', function(e) {
        var container = $(this).parent();
        var imageId = container.closest('.image-container').data('id');

        // Only proceed if we have products for this image
        if (!productAssociations[imageId] || productAssociations[imageId].length === 0) {
            return;
        }

        // Calculate position in percentage
        var offset = $(this).offset();
        var x = (e.pageX - offset.left) / $(this).width() * 100;
        var y = (e.pageY - offset.top) / $(this).height() * 100;

        // Round to 2 decimal places
        x = Math.round(x * 100) / 100;
        y = Math.round(y * 100) / 100;

        // If we have only one product, set its position directly
        if (productAssociations[imageId].length === 1) {
            var productId = productAssociations[imageId][0].id_product;

            // Update position in memory
            productAssociations[imageId][0].position_x = x;
            productAssociations[imageId][0].position_y = y;

            // Update marker position
            var marker = $('.product-marker[data-product-id="' + productId + '"][data-image-id="' + imageId + '"]');
            marker.css({
                'left': x + '%',
                'top': y + '%'
            });

            // Update button text
            var editButton = $('.edit-product-position[data-product-id="' + productId + '"][data-image-id="' + imageId + '"]');
            editButton
                .data('position-x', x)
                .data('position-y', y)
                .attr('data-position-x', x)
                .attr('data-position-y', y)
                .html('<i class="icon-pencil"></i> {l s='Position' d='Modules.Webixapreview.Admin'} (' + x + '%, ' + y + '%)');

            // Update hidden field
            updateProductAssociationsField();

            showSuccessMessage('{l s='Product position updated' d='Modules.Webixapreview.Admin'}');
        } else {
            // If we have multiple products, show a modal to select which product to position
            // First, populate the modal with product options
            var modalContent = '<div class="list-group">';
            for (var i = 0; i < productAssociations[imageId].length; i++) {
                var product = productAssociations[imageId][i];
                var productName = $('.product-item[data-product-id="' + product.id_product + '"]').find('.col-xs-8').text().trim();
                modalContent += '<a href="#" class="list-group-item select-product-for-position" ' +
                                'data-product-id="' + product.id_product + '" ' +
                                'data-image-id="' + imageId + '" ' +
                                'data-position-x="' + x + '" ' +
                                'data-position-y="' + y + '">' +
                                productName +
                                '</a>';
            }
            modalContent += '</div>';

            // Create and show the modal
            var selectProductModal = $('<div class="modal fade" id="select-product-modal" tabindex="-1" role="dialog">' +
                '<div class="modal-dialog" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>' +
                '<h4 class="modal-title">{l s='Select product to position' d='Modules.Webixapreview.Admin'}</h4>' +
                '</div>' +
                '<div class="modal-body">' +
                modalContent +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>');

            // Remove any existing modal
            $('#select-product-modal').remove();

            // Add the new modal to the page
            $('body').append(selectProductModal);

            // Show the modal
            $('#select-product-modal').modal('show');
        }
    });

    // Handle product selection for positioning
    $(document).on('click', '.select-product-for-position', function(e) {
        e.preventDefault();

        var productId = $(this).data('product-id');
        var imageId = $(this).data('image-id');
        var x = $(this).data('position-x');
        var y = $(this).data('position-y');

        // Update position in memory
        for (var i = 0; i < productAssociations[imageId].length; i++) {
            if (productAssociations[imageId][i].id_product == productId) {
                productAssociations[imageId][i].position_x = x;
                productAssociations[imageId][i].position_y = y;
                break;
            }
        }

        // Update marker position
        var marker = $('.product-marker[data-product-id="' + productId + '"][data-image-id="' + imageId + '"]');
        marker.css({
            'left': x + '%',
            'top': y + '%'
        });

        // Update button text
        var editButton = $('.edit-product-position[data-product-id="' + productId + '"][data-image-id="' + imageId + '"]');
        editButton
            .data('position-x', x)
            .data('position-y', y)
            .attr('data-position-x', x)
            .attr('data-position-y', y)
            .html('<i class="icon-pencil"></i> {l s='Position' d='Modules.Webixapreview.Admin'} (' + x + '%, ' + y + '%)');

        // Update hidden field
        updateProductAssociationsField();

        // Close the modal
        $('#select-product-modal').modal('hide');

        showSuccessMessage('{l s='Product position updated' d='Modules.Webixapreview.Admin'}');
    });

    // Initialize product lists and markers for all images on page load
    $('.image-container').each(function() {
        var imageId = $(this).data('id');
        if (imageId && productAssociations[imageId]) {
            refreshImageData(imageId);
        }
    });

    // Make product markers draggable
    initDraggableMarkers();

    function initDraggableMarkers() {
        $('.draggable').each(function() {
            var marker = $(this);
            var container = marker.closest('.image-preview-container');
            var img = container.find('img');
            var productId = marker.data('product-id');
            var imageId = marker.data('image-id');
            var isDragging = false;
            var startX, startY;

            // Mouse events for desktop
            marker.on('mousedown', function(e) {
                e.preventDefault();
                isDragging = true;
                startX = e.pageX;
                startY = e.pageY;

                // Add active class for visual feedback
                marker.addClass('active-dragging').css('background-color', 'rgba(0, 255, 0, 0.7)');

                // Add temporary overlay to capture mouse events
                $('body').append('<div id="drag-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 9999; cursor: move;"></div>');

                // Show position info
                showPositionInfo(marker);
            });

            $(document).on('mousemove', function(e) {
                if (!isDragging) return;

                var imgOffset = img.offset();
                var imgWidth = img.width();
                var imgHeight = img.height();

                // Calculate new position in percentage
                var newX = ((e.pageX - imgOffset.left) / imgWidth) * 100;
                var newY = ((e.pageY - imgOffset.top) / imgHeight) * 100;

                // Constrain to image boundaries
                newX = Math.max(0, Math.min(100, newX));
                newY = Math.max(0, Math.min(100, newY));

                // Round to 2 decimal places
                newX = Math.round(newX * 100) / 100;
                newY = Math.round(newY * 100) / 100;

                // Update marker position
                marker.css({
                    'left': newX + '%',
                    'top': newY + '%'
                });

                // Update position info
                updatePositionInfo(marker, newX, newY);
            });

            $(document).on('mouseup', function() {
                if (!isDragging) return;

                isDragging = false;

                // Remove active class
                marker.removeClass('active-dragging').css('background-color', 'rgba(255, 0, 0, 0.7)');

                // Remove overlay
                $('#drag-overlay').remove();

                // Get final position
                var finalX = parseFloat(marker.css('left')) / img.width() * 100;
                var finalY = parseFloat(marker.css('top')) / img.height() * 100;

                // Round to 2 decimal places
                finalX = Math.round(finalX * 100) / 100;
                finalY = Math.round(finalY * 100) / 100;

                // Update in memory and UI
                updateProductPosition(productId, imageId, finalX, finalY);

                // Hide position info
                hidePositionInfo();
            });

            // Touch events for mobile
            marker.on('touchstart', function(e) {
                e.preventDefault();
                isDragging = true;
                var touch = e.originalEvent.touches[0];
                startX = touch.pageX;
                startY = touch.pageY;

                // Add active class for visual feedback
                marker.addClass('active-dragging').css('background-color', 'rgba(0, 255, 0, 0.7)');

                // Show position info
                showPositionInfo(marker);
            });

            $(document).on('touchmove', function(e) {
                if (!isDragging) return;

                var touch = e.originalEvent.touches[0];
                var imgOffset = img.offset();
                var imgWidth = img.width();
                var imgHeight = img.height();

                // Calculate new position in percentage
                var newX = ((touch.pageX - imgOffset.left) / imgWidth) * 100;
                var newY = ((touch.pageY - imgOffset.top) / imgHeight) * 100;

                // Constrain to image boundaries
                newX = Math.max(0, Math.min(100, newX));
                newY = Math.max(0, Math.min(100, newY));

                // Round to 2 decimal places
                newX = Math.round(newX * 100) / 100;
                newY = Math.round(newY * 100) / 100;

                // Update marker position
                marker.css({
                    'left': newX + '%',
                    'top': newY + '%'
                });

                // Update position info
                updatePositionInfo(marker, newX, newY);
            });

            $(document).on('touchend', function() {
                if (!isDragging) return;

                isDragging = false;

                // Remove active class
                marker.removeClass('active-dragging').css('background-color', 'rgba(255, 0, 0, 0.7)');

                // Get final position
                var finalX = parseFloat(marker.css('left')) / img.width() * 100;
                var finalY = parseFloat(marker.css('top')) / img.height() * 100;

                // Round to 2 decimal places
                finalX = Math.round(finalX * 100) / 100;
                finalY = Math.round(finalY * 100) / 100;

                // Update in memory and UI
                updateProductPosition(productId, imageId, finalX, finalY);

                // Hide position info
                hidePositionInfo();
            });
        });
    }

    // Function to update product position in memory and UI
    function updateProductPosition(productId, imageId, posX, posY) {
        // Update in memory
        if (productAssociations[imageId]) {
            for (var i = 0; i < productAssociations[imageId].length; i++) {
                if (productAssociations[imageId][i].id_product == productId) {
                    productAssociations[imageId][i].position_x = posX;
                    productAssociations[imageId][i].position_y = posY;
                    break;
                }
            }
        }

        // Update data attributes for the edit button
        var editButton = $('.edit-product-position[data-product-id="' + productId + '"][data-image-id="' + imageId + '"]');
        editButton
            .data('position-x', posX)
            .data('position-y', posY)
            .attr('data-position-x', posX)
            .attr('data-position-y', posY);

        // Update the button text to show the new position
        editButton.html('<i class="icon-pencil"></i> {l s='Position' d='Modules.Webixapreview.Admin'} (' + posX + '%, ' + posY + '%)');

        // Update hidden field
        updateProductAssociationsField();

        // Show success message
        showSuccessMessage('{l s='Product position updated' d='Modules.Webixapreview.Admin'}');
    }

    // Function to show position info while dragging
    function showPositionInfo(marker) {
        // Remove any existing position info
        $('.position-info').remove();

        // Get current position
        var posX = parseFloat(marker.css('left'));
        var posY = parseFloat(marker.css('top'));

        // Create position info element
        var posInfo = $('<div class="position-info" style="position: absolute; background-color: rgba(0, 0, 0, 0.7); color: white; padding: 2px 5px; border-radius: 3px; font-size: 12px; z-index: 100;"></div>');
        marker.after(posInfo);

        // Update position info
        updatePositionInfo(marker, posX, posY);
    }

    // Function to update position info while dragging
    function updatePositionInfo(marker, posX, posY) {
        var posInfo = $('.position-info');
        if (posInfo.length) {
            posInfo.html('X: ' + posX.toFixed(2) + '%, Y: ' + posY.toFixed(2) + '%');
            posInfo.css({
                'left': (posX + 2) + '%',
                'top': (posY - 5) + '%'
            });
        }
    }

    // Function to hide position info after dragging
    function hidePositionInfo() {
        $('.position-info').remove();
    }

    // Form submission - ensure product associations are included
    $('form').submit(function() {
        updateProductAssociationsField();
    });
});
</script>

<style>
.product-item {
    margin-bottom: 5px;
    padding: 5px;
    border-bottom: 1px solid #eee;
}
.product-item:last-child {
    border-bottom: none;
}
.associated-products-list {
    max-height: 150px;
    overflow-y: auto;
    margin-bottom: 10px;
    padding: 5px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
}
.add-product-form {
    margin-top: 10px;
}
</style>
