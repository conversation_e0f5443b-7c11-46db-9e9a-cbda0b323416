{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<script type="text/javascript">
    var urlUpload = '{$link->getAdminLink('AdminMassUpdateProducts')|strval}&tab=masschangeimageofproduct&upload';
    var cover = '{l s='cover' mod='massupdateproducts'}';
</script>
{if $temp neq 'combination'}
    <div class="file-upload-block inactive">
        <input type="file" multiple class="file-upload" style="width:0px;height:0px;display: none;" accept="image/png,image/jpeg,image/gif" />
        <button class="button-upload" style="cursor: pointer;border: 1px solid #FFF;border-radius: 5px;background-color: #FFF;margin-bottom: 2px;" data-style="expand-right" data-size="s" type="button" id="file-add-button">
            <i class="fa fa-folder-open-o"></i>
            {l s='Add files' mod='massupdateproducts'}...
        </button>
        <table class="upload" style="background-color: #FFF;table-layout: fixed;">
            <colgroup>
                <col width="40">
                <col width="100">
                <col width="100">
                <col width="700">
                <col width="50">
            </colgroup>
            <thead>
                <tr>
                    <th></th>
                    <th>{l s='Name' mod='massupdateproducts'}</th>
                    <th>{l s='Picture' mod='massupdateproducts'}</th>
                    <th>{l s='Type' mod='massupdateproducts'}</th>
                    <th>{l s='Size' mod='massupdateproducts'}</th>
                </tr>
            </thead>
            <tbody>

            </tbody>
        </table>
    </div>
{else}
    <!--<div class="image-show-contents inactive">
        <div class="image-content-droppable" style="border: 1px solid #000; height: 100%;text-align: center; line-height: 125px;">
    {l s='Drop images from the main product here to add to the combination' mod='massupdateproducts'}
</div>
</div>-->
    {if $products_images}
        <div class="image-show-contents inactive">
            <div class="image-show-content">
                {foreach $products_images as $product_image}
                    <div class="image-show-content-block" pic-id="{$product_image['id']|intval}">
                        <div class="image-show-content-tools">
                            <span class="picture_belong add-from-main-inactive" title="{l s='Add' mod='massupdateproducts'}">
                                <i class="fa fa-plus-square"></i>
                                <input type="hidden" send-name="picture_belong" class="to-send" value="0" />
                            </span>
                        </div>
                        <div class="image-show-content-picture">
                            <img src="{$product_image['link']|strval}" style="width: 125px;height: 125px;border: 1px solid #000;">
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>
    {/if}
{/if}