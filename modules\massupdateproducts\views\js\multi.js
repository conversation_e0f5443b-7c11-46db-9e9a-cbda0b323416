/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function() {
    var $products_list_head = $('#products_list').find('.main_head');
    var $products_list_body = $('#products_list').find('.main_body');

    $products_list_head.on('change', '.multi', function() {
	var $scope = $(this);
	var $name = $scope.attr('send-name');
	$products_list_body.find('.to-send[send-name="' + $name + '"]').each(function() {
	    var $element = $(this);
	    var $tr = $element.closest('.product-row');
	    var $check = $tr.find('.check_single');
	    if ($check.prop('checked') && $tr.hasClass('product-row'))
	    {
		if ($element.is(':checkbox'))
		{
		    if ($element.hasClass('no-checkbox'))
		    {
			if ($element.val() == $scope.val())
			    $element.prop('checked', $scope.prop('checked')).trigger('change');
		    }
		    else
			$element.prop('checked', $scope.prop('checked')).trigger('change');
		}
		else
		    $element.val($scope.val()).trigger('change');
	    }
	});
    });

    $products_list_body.on('click', '.ic-copy', function() {
	var $scope = $(this);
	var $tr = $scope.closest('.combination-row');
	$products_list_body.find('.combination-row[c-ids="' + $tr.attr('c-ids') + '"]').each(function() {
	    var $this = $(this);
	    if ($this.find('.check_single').prop('checked'))
		$tr.find('.to-send').each(function() {
		    var $this_2 = $(this);
		    var $td = $this_2.closest('.td-element')
		    if (!$td.hasClass('none-copy'))
			$this.find('.to-send[send-name="' + $this_2.attr('send-name') + '"]').val($this_2.val()).trigger('change');
		});
	});
    });

    $products_list_body.on('click', '.ic-copy-single', function() {
	var $scope = $(this);
	var $send_name = $scope.closest('.td-element').find('.to-send');
	var $tr = $scope.closest('.combination-row');
	$products_list_body.find('.combination-row[c-ids="' + $tr.attr('c-ids') + '"]').each(function() {
	    var $this = $(this);
	    if ($this.find('.check_single').prop('checked'))
		$this.find('.to-send[send-name="' + $send_name.attr('send-name') + '"]').val($send_name.val()).trigger('change');
	});
    });

    $products_list_body.on('change', '.check_product', function() {
	var $scope = $(this);
	var $tr = $scope.closest('.product-row');
	$products_list_body.find('.combination-row[p-id="' + $tr.attr('p-id') + '"] .check_combination').prop('checked', $scope.prop('checked'));
    });

    $products_list_body.on('change', '.check_combination', function() {
	var $scope = $(this);
	var $tr = $scope.closest('.product-row');
	if ($scope.prop('checked'))
	    $products_list_body.find('.product-row[p-id="' + $tr.attr('p-id') + '"] .check_product').prop('checked', $scope.prop('checked'));
    });
    
    $products_list_head.on('click', '#multi-get-prod', function() {
        var $scope = $(this);
        $scope.removeClass('fa-plus').addClass('fa-minus').attr('id', 'multi-get-prod-r');
        $products_list_body.find('.fa-plus').each(function() {
            $(this).trigger('click');
        });
    });
    
    $products_list_head.on('click', '#multi-get-prod-r', function() {
        var $scope = $(this);
        $scope.removeClass('fa-minus').addClass('fa-plus').attr('id', 'multi-get-prod');
        $products_list_body.find('.fa-minus').each(function() {
            $(this).trigger('click');
        });
    });
});