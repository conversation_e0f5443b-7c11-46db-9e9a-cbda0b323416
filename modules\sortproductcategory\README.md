# Sort Product Category - Moduł PrestaShop

## Opis

Zaawansowany moduł do zarządzania kolejnością produktów w kategoriach PrestaShop. Moduł został zaprojektowany specjalnie do obsługi dużej liczby produktów (1500+) z zaawansowanymi funkcjami sortowania, wyszukiwania i operacji grupowych.

## Funkcjonalności

### Podstawowe funkcje
- **Zarządzanie pozycjami produktów** - intuicyjny interfejs do zmiany kolejności produktów
- **Obsługa dużej liczby produktów** - paginacja i optymalizacja dla kategorii z 1500+ produktami
- **Drag & Drop** - przeciąganie produktów dla szybkiej zmiany pozycji
- **Wyszukiwanie w czasie rzeczywistym** - szybkie znajdowanie produktów po nazwie lub kodzie

### Zaawansowane funkcje
- **Operacje grupowe (Bulk Operations)**:
  - Przenoszenie wielu produktów jednocześnie
  - Przypisywanie pozycji początkowej dla grupy produktów
  - Maksymalnie 100 produktów w jednej operacji

- **Automatyczne sortowanie**:
  - Sortowanie według nazwy, kodu, ceny, daty dodania
  - Kierunek rosnący/malejący
  - Automatyczne uporządkowanie pozycji (usuwanie luk)

- **Zaawansowane wyszukiwanie**:
  - Filtrowanie według statusu aktywności
  - Filtrowanie według zakresu cen
  - Filtrowanie według minimalnej ilości w magazynie

### Bezpieczeństwo
- **Walidacja uprawnień** - sprawdzanie dostępu do kategorii i produktów
- **CSRF Protection** - ochrona przed atakami cross-site request forgery
- **Sanityzacja danych** - bezpieczne przetwarzanie danych wejściowych
- **Limity operacji** - ograniczenia dla operacji grupowych

## Instalacja

1. Skopiuj folder `sortproductcategory` do katalogu `modules/` w PrestaShop
2. Przejdź do panelu administracyjnego PrestaShop
3. Idź do **Moduły** > **Menedżer modułów**
4. Znajdź moduł "Sort Product Category" i kliknij **Instaluj**
5. Po instalacji moduł będzie dostępny w menu **Katalog** > **Sortowanie produktów**

## Użytkowanie

### Wybór kategorii
1. Przejdź do **Katalog** > **Sortowanie produktów**
2. Wybierz kategorię z listy rozwijanej
3. Kliknij **Przejdź do zarządzania produktami**

### Zarządzanie pozycjami produktów

#### Metoda 1: Drag & Drop
- Przeciągnij produkty za pomocą uchwytu (⋮⋮) aby zmienić ich pozycję
- Zmiany są automatycznie zapisywane (jeśli włączone w ustawieniach)

#### Metoda 2: Ręczne wprowadzanie pozycji
- Wpisz nową pozycję w polu "Nowa pozycja"
- Kliknij przycisk ✓ aby zastosować zmianę

#### Metoda 3: Operacje grupowe
1. Zaznacz produkty które chcesz przenieść
2. Wpisz pozycję początkową w polu "Pozycja początkowa"
3. Kliknij **Przenieś zaznaczone**

### Wyszukiwanie i filtrowanie
- **Podstawowe wyszukiwanie**: Wpisz nazwę lub kod produktu w polu wyszukiwania
- **Zaawansowane filtry**: Kliknij "Filtry zaawansowane" aby uzyskać dostęp do dodatkowych opcji filtrowania

### Sortowanie automatyczne
- Użyj listy rozwijanej "Sortuj według" aby wybrać kryterium
- Wybierz kierunek sortowania (rosnąco/malejąco)
- Kliknij **Szukaj** aby zastosować sortowanie

### Uporządkowanie pozycji
- Kliknij **Uporządkuj pozycje** aby usunąć luki w numeracji pozycji
- Produkty zostaną ponumerowane od 0 w aktualnej kolejności

## Ustawienia modułu

Dostępne w zakładce **Ustawienia**:

- **Liczba produktów na stronie** (10-200): Określa ile produktów wyświetlać na jednej stronie
- **Włącz wyszukiwanie produktów**: Pokazuje/ukrywa pole wyszukiwania
- **Włącz operacje grupowe**: Pokazuje/ukrywa sekcję operacji grupowych
- **Automatyczne zapisywanie zmian**: Automatycznie zapisuje zmiany pozycji przy drag&drop

## Wskazówki dotyczące wydajności

### Dla kategorii z dużą liczbą produktów:
- Użyj wyszukiwania aby znaleźć konkretne produkty
- Wykorzystaj operacje grupowe zamiast przeciągania pojedynczych produktów
- Zmniejsz liczbę produktów na stronie dla lepszej wydajności

### Optymalizacja pracy:
- Sortuj produkty według nazwy lub kodu przed zmianą pozycji
- Użyj funkcji "Uporządkuj pozycje" aby usunąć luki w numeracji
- Zapisuj zmiany regularnie przy dużych operacjach

## Rozwiązywanie problemów

### Problemy z wydajnością
- Zmniejsz liczbę produktów na stronie w ustawieniach
- Użyj wyszukiwania zamiast przeglądania wszystkich produktów
- Wyłącz automatyczne zapisywanie dla bardzo dużych kategorii

### Problemy z drag&drop
- Upewnij się, że JavaScript jest włączony w przeglądarce
- Sprawdź czy jQuery jest załadowane na stronie
- Odśwież stronę i spróbuj ponownie

### Błędy uprawnień
- Sprawdź czy masz uprawnienia do zarządzania produktami
- Skontaktuj się z administratorem systemu

## Wymagania techniczne

- **PrestaShop**: ******* lub nowszy
- **PHP**: 7.1 lub nowszy
- **MySQL**: 5.6 lub nowszy
- **JavaScript**: Włączony w przeglądarce
- **jQuery**: Załadowane (standardowo w PrestaShop)

## Struktura bazy danych

Moduł wykorzystuje istniejące tabele PrestaShop:
- `ps_category_product` - przechowuje pozycje produktów w kategoriach
- `ps_product` - informacje o produktach
- `ps_product_lang` - nazwy produktów w różnych językach
- `ps_category` - informacje o kategoriach

## Bezpieczeństwo

Moduł implementuje następujące zabezpieczenia:
- Walidacja wszystkich danych wejściowych
- Sprawdzanie uprawnień użytkownika
- CSRF protection dla żądań AJAX
- Sanityzacja danych przed zapisem do bazy
- Limity dla operacji grupowych (max 100 produktów)

## Wsparcie

W przypadku problemów lub pytań:
1. Sprawdź sekcję "Rozwiązywanie problemów" w tej dokumentacji
2. Sprawdź logi błędów PrestaShop
3. Skontaktuj się z deweloperem modułu

## Licencja

Moduł jest dostępny na licencji komercyjnej.
Copyright © 2025 Kardo IT

## Historia wersji

### v1.0.0
- Pierwsza wersja modułu
- Podstawowe funkcje zarządzania pozycjami
- Drag & Drop interface
- Operacje grupowe
- Zaawansowane wyszukiwanie
- Zabezpieczenia i walidacja
