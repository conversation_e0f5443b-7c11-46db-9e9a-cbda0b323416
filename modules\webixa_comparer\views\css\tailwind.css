.comparison-notification {
    @apply fixed top-4 right-4 z-50 transition-opacity duration-300;
}

.comparison-table th {
    @apply bg-gray-100 px-4 py-2 text-left;
}

.comparison-table td {
    @apply px-4 py-2 border-b border-gray-200;
}

.comparison-table tr:hover td {
    @apply bg-gray-50;
}

.compare-button {
    @apply inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md transition-colors;
}

.compare-button.add {
    @apply bg-blue-600 text-white hover:bg-blue-700;
}

.compare-button.remove {
    @apply bg-red-600 text-white hover:bg-red-700;
}

.comparer {
    position: relative;
}

.comparer .comparison-count {
    position: absolute;
    right: -0.625rem;
    top: -0.688rem;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background-color: #ae7d8d;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    display: grid;
    place-items: center;
}

.transparent-solid-button .compare-button-text {
    font-size: .75rem;
    line-height: 1rem;
    font-weight: 700;
    text-transform: uppercase;
	padding: 0.5rem;
	color: #000;
}