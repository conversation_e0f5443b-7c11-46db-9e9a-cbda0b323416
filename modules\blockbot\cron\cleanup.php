<?php
/**
 * BlockBot Cron Job - Cleanup old logs
 * 
 * This script should be run via cron job to automatically clean up logs older than 3 months
 * 
 * Example cron job (run daily at 2 AM):
 * 0 2 * * * /usr/bin/php /path/to/your/prestashop/modules/blockbot/cron/cleanup.php
 * 
 * <AUTHOR> Name
 * @copyright 2025
 * @license   Commercial
 */

// Include PrestaShop configuration
$prestashopPath = dirname(dirname(dirname(dirname(__FILE__))));
require_once($prestashopPath . '/config/config.inc.php');

// Check if we're running from command line or web
$isCli = (php_sapi_name() === 'cli');

// Security check for web access
if (!$isCli) {
    // Allow access only with secret key
    $secretKey = Configuration::get('BLOCKBOT_CRON_KEY');
    if (!$secretKey) {
        // Generate and save secret key if it doesn't exist
        $secretKey = Tools::passwdGen(32);
        Configuration::updateValue('BLOCKBOT_CRON_KEY', $secretKey);
    }
    
    $providedKey = Tools::getValue('key');
    if ($providedKey !== $secretKey) {
        http_response_code(403);
        die('Access denied. Invalid key.');
    }
}

// Check if BlockBot module is installed and enabled
if (!Module::isInstalled('blockbot')) {
    logMessage('BlockBot module is not installed');
    exit(1);
}

$blockBotModule = Module::getInstanceByName('blockbot');
if (!$blockBotModule) {
    logMessage('Could not load BlockBot module');
    exit(1);
}

if (!Configuration::get('BLOCKBOT_ENABLED')) {
    logMessage('BlockBot module is disabled');
    exit(0);
}

// Get cleanup settings
$cleanupMonths = (int)Configuration::get('BLOCKBOT_CLEANUP_MONTHS');
if ($cleanupMonths <= 0) {
    $cleanupMonths = 3; // Default to 3 months
}

// Log start of cleanup
logMessage('Starting BlockBot cleanup process...');
logMessage('Cleaning logs older than ' . $cleanupMonths . ' months');

// Get count of logs before cleanup
$totalLogsBefore = $blockBotModule->getLogCount();
logMessage('Total logs before cleanup: ' . $totalLogsBefore);

// Perform cleanup
$result = $blockBotModule->cleanOldLogs($cleanupMonths);

if ($result) {
    // Get count of logs after cleanup
    $totalLogsAfter = $blockBotModule->getLogCount();
    $deletedLogs = $totalLogsBefore - $totalLogsAfter;
    
    logMessage('Cleanup completed successfully');
    logMessage('Deleted logs: ' . $deletedLogs);
    logMessage('Remaining logs: ' . $totalLogsAfter);
    
    // Update last cleanup time
    Configuration::updateValue('BLOCKBOT_LAST_CLEANUP', date('Y-m-d H:i:s'));
    
    if (!$isCli) {
        echo json_encode([
            'success' => true,
            'deleted_logs' => $deletedLogs,
            'remaining_logs' => $totalLogsAfter,
            'cleanup_date' => date('Y-m-d H:i:s')
        ]);
    }
} else {
    logMessage('Cleanup failed');
    
    if (!$isCli) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Cleanup failed'
        ]);
    }
    exit(1);
}

/**
 * Log message to file and/or output
 */
function logMessage($message)
{
    global $isCli;
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}";
    
    // Output to console if CLI
    if ($isCli) {
        echo $logMessage . "\n";
    }
    
    // Log to file
    $logFile = dirname(__FILE__) . '/cleanup.log';
    file_put_contents($logFile, $logMessage . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * Get memory usage
 */
function getMemoryUsage()
{
    return round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB';
}

// Log memory usage
logMessage('Memory usage: ' . getMemoryUsage());
logMessage('BlockBot cleanup process completed');

exit(0);
