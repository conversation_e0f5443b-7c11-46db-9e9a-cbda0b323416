.ph-panel {
    display: block;
    position: relative;
    overflow: hidden;
    width: 100%;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
    background-color: #FFF;
    margin-bottom: 25px;
}

.ph-panel *:not(.fa):not(.mce-ico) {
    font-family: "Open Sans", Arial, sans-serif !important;
    color: #555;
}

.ph-panel-e {
    background-color: #FFF!important;
}
.ph-panel-c {
    background-color: #FCFFDE!important;
}
.ph-panel-head {
    display: block;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 50px;
    border-bottom: solid 1px #D3D8DB;
    line-height: 50px;
}
.ph-panel-head-nu-e {
    display: inline-block;
    position: relative;
    overflow: hidden;
    width: 50px;
    height: 50px;
    line-height: 50px;
    float: left;
    text-align: center;
    font-size: 14px;
    border-right: 1px solid #D3D8DB;
    box-sizing: border-box;
}
.ph-panel-head-na-e {
    display: inline-block;
    position: relative;
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    float: left;
    margin-left: 20px;
    font-size: 16px;
}
.ph-panel-head-dt-e {
    display: inline-block;
    position: relative;
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    float: right;
    margin-right: 20px;
}
.ph-panel-head-nu-c {
    display: inline-block;
    position: relative;
    overflow: hidden;
    width: 50px;
    height: 50px;
    line-height: 50px;
    float: right;
    text-align: center;
    font-size: 14px;
    border-left: 1px solid #D3D8DB;
    box-sizing: border-box;
}
.ph-panel-head-na-c {
    display: inline-block;
    position: relative;
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    float: right;
    margin-right: 20px;
}
.ph-panel-head-dt-c {
    display: inline-block;
    position: relative;
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    float: left;
    margin-left: 20px;
}
.ph-panel-content {
    display: block;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: auto;
    padding: 20px;
    box-sizing: border-box;
}

.ph-panel-head-main {
    margin-left: 20px;
    margin-right: 20px;
    font-size: 20px;
    height: 48px;
}
.ph-panel-new-message-field {
    height: 300px;
    padding: 15px;
}

.ph-panel-foot {
    display: block;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 50px;
    line-height: 50px;
    border-top: 1px solid #D3D8DB;
}
.ph-panel-content-m-list {
    margin-top: 20px;
    display: block;
    position: relative;
    overflow: hidden;
    height: 150px;
    overflow-y: scroll;
}

.ph-panel-content-m-list ul {
    list-style: none;
    padding-left: 13px;
}

.ph-panel-content-m-list ul li {
    padding: 10px 15px;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
    cursor: pointer;
    box-sizing: border-box;
}
.ph-panel-content-m-list ul li:hover {
    background-color: #83D0FF;
}

.ph-panel-content-m-head-search {
    float: right;
}

.ph-panel-content-m-head-search .search-elements {
    height: 25px;
    width: 200px;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
    line-height: 25px;
    padding: 0px 5px;
}

.tinyAC { 
    display: none; 
    position: absolute;
    overflow-y: scroll;
    overflow-x: hidden;
    width: 150px;
    height: 200px;
    background-color: #FFF;
    z-index: 485;
    top: 0;
    left:0;
    box-sizing: border-box;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
}

.tinyAC-e {
    display: block;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #D3D8DB;
}

.tinyAC-e:hover {
    background-color: #D3D8DB;
}

.ph-panel-content-m-header-add-link {
    float: right;
    line-height: 25px;
    width: 50%;
    position: relative;
    height: 25px;
    box-sizing: border-box;
}

.ph-panel-content-m-header-add-link .up-link {
    width: 100%;
    padding: 0px 28px 0px 5px;
}

.ph-panel-content-m-header-add-link .add_link {
    position: absolute;
    right: 3px;
    font-size: 25px;
    top: 4px;
    cursor: pointer;
}

.ph-panel .remove-link, .ph-panel .remove-file {
    float: right;
    font-size: 18px;
}

.ph-panel input[type="text"]:not(.input_range) {
    padding: 0px;
    border: 1px solid #DEDEDE !important;
    box-sizing: border-box !important;
    line-height: 20px;
    padding-left: 5px;
    padding-right: 5px;
}

.ph-panel select {
    border: 1px solid #DEDEDE !important;
    min-width: 25% !important;
    padding: 0px !important;
    box-sizing: border-box !important;
    height: 23px;
}

.ph-panel .hidden {
    display: none;
}

.ph-panel .ph-table {
    border-collapse: separate;
    width: 100%;
    border-spacing: 0px;
}

.ph-panel .ph-table thead > tr > th {
    border: none;
    font-weight: normal;
    vertical-align: top;
    border-bottom: solid 1px #a0d0eb;
}

.ph-panel .ph-table thead > tr.filter > th { 
    background-color: #ecf6fb;
}

.ph-panel .ph-table thead > tr > td,
.ph-panel .ph-table tbody > tr > td{
    border-top: none;
    color: #666666;
    background-color: white;
    padding: 3px 7px;
    vertical-align: middle;
    font-size: 12px;
    border-bottom: solid 1px #eaedef;
}

.ph-panel-footer {
    display: block;
    position: relative;
    height: 66px;
    width: 100%;
    border-bottom: solid 1px #D3D8DB;
}

.ph-panel-footer .ph-panel-footer-left,
.ph-panel-footer .ph-panel-footer-middle,
.ph-panel-footer .ph-panel-footer-right
{
    display: inline-block;
    position: relative;
    width: 33%;
}

.ph-panel-footer .ph-panel-footer-left,
.ph-panel-footer .ph-panel-footer-right {
    overflow: hidden;
}

.ph-panel-footer .ph-panel-footer-left,
.ph-panel-footer .ph-panel-footer-middle {
    float: left;
}

.ph-panel-footer .ph-panel-footer-right {
    float: right;
}

.ph-panel .dropdown-menu-panel {
    display: none;
    position: absolute;
    overflow: hidden;
    list-style: none;
    border: solid 1px #D3D8DB;
    border-radius: 5px;
    z-index: 1000;
    padding: 0px;
    margin: 0px;
    left: 50px;
}

.ph-panel .dropdown-menu-panel li {
    display: block;
    position: relative;
    overflow: hidden;
    padding: 10px;
    box-sizing: border-box;
    text-align: center;
    background-color: #FFF;
    cursor: pointer;
}

.ph-panel .pagination {
    display: block;
    position: relative;
    overflow: hidden;
    padding: 5px;
}

.ph-panel ul.pagination {
    float: right;
}

.ph-panel .pagination-select {
    border: 1px solid #D3D8DB;
    border-radius: 5px;
    padding: 5px;
    cursor: pointer;
}

.ph-panel .badge {
    padding: 1px 5px;
    font-weight: normal;
    font-size: 1em;
    line-height: inherit;
    letter-spacing: 0.0625em;
    background-color: #00aff0;
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    color: #fff;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    border-radius: 10px;
}

.ph-panel .pagination-link-view {
    display: inline-block;
    position: relative;
    overflow: hidden;
    float: left;
}

.ph-panel .pagination-link-view.disabled span, .ph-panel .pagination-link-view.disabled i {
    cursor: not-allowed;
}
.ph-panel .pagination-link-view i {
    font-size: 28px;
}

.ph-panel .pagination-link-view.active span {
    background-color: #00aff0;
    border: 1px solid #00aff0;
}

.ph-panel .pagination-link-view.active span {    
    color: #FFF;
}


.ph-panel .pagination-link-view span {
    display: block;
    position: relative;
    overflow: hidden;
    width: 30px;
    height: 30px;
    background-color: #FFF;
    border: 1px solid #D3D8DB;
    line-height: 30px;
    text-align: center;
    border-radius: 5px;
    cursor: pointer;
}

.ph-panel .buttons-ph-panel {
    background-color: #FFF;
    padding: 5px;
    border-radius: 5px;
    display: inline-block;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.ph-panel .fixed {
    display: inline-block;
    position: relative;
    overflow: hidden;
    background-color: #FFF;
    float: right;
    border: 1px solid #D3D8DB;
    border-radius: 5px;
    padding: 0px 10px;
}

.ph-panel .header-fixed {
    z-index: 10000;
    bottom: 0px;
    position: fixed;
}

.ph-panel .td-element {
    display: inline-block;
    position: relative;
    float: left;
    min-height: 10px;
    box-sizing: border-box;
    margin-left: 10px;
}

/* priceByIdPanel */
#priceByIdPanel input.error {
    border-color: #ff0000;
}
#priceByIdPanel select.error {
    border-color: #ff0000;
}
#priceByIdPanel .form-group.errors {
    background-color: #f8d7da;
}
#priceByIdPanel .form-group {
    padding: 5px;
}

.itemUpdate {
    cleaR:both;
    overflow: hidden;
    width: 100%;
    background: #fff;
    color: #000;
    padding: 10px 15px;
    margin-bottom: 10px;
}
.itemStatusfalse {
    background: #f8d7da;
}
.itemStatustrue {
    background: #d4edda;
}

.priceByIdResult {
    padding: 15px;
    background: #fff;
    margin-top: 15px;
    display: none;
}

.loadingPrice {
    clear:both;
    overflow: hidden;
    width: 100%;
    padding: 15px 25px;
    text-align: center;
    background: #d6d8d9;
    color: #1b1e21;
    border: 1px solid #c6c8ca;
    box-shadow: #c6c8ca 0 0 10px;
    margin-bottom: 15px;
    font-weight: 700;
}

.field_handler {
    margin-bottom: 25px;
}

#massupdateproducts-products .panel-content .td-element .field_handler.field_handler_tiny {
    margin: 0 0 14px 0;
}
#massupdateproducts-products .panel-content .td-element .field_handler textarea {
    height: 39px
}
#configuration_form.massupdateproducts i {
    font-family: "Material Icons" !important;
}
