<?php

/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateMetaTagsProductsModel extends MassUpdateProductsAbstract
{

	public function __construct(&$module, &$object, &$context)
	{
		$this->settings_name = Tools::strtoupper('MassUpdateMetaTagsProductsModel');
		parent::__construct($module, $object, $context);

                //name
		$this->fields['name'] = array(
			'display_name' => $this->module->l('Name', $this->languages_name),
			'name' => 'name',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_STR,
			'sort' => true,
			'base_name' => 'pl.name'
		);

		//product description
		$this->fields['product_description'] = array(
			'display_name' => $this->module->l('Desciption', $this->languages_name),
			'name' => 'product_description',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_TINY,
			'sort' => true,
			'base_name' => 'pl.description'
		);

		//product short description
		$this->fields['product_short_description'] = array(
			'display_name' => $this->module->l('Short desciption', $this->languages_name),
			'name' => 'product_short_description',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_TINY,
			'sort' => true,
			'base_name' => 'pl.short_description'
		);

		//title
		$this->fields['title'] = array(
			'display_name' => $this->module->l('Meta title', $this->languages_name),
			'name' => 'title',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_STR,
			'sort' => true,
			'base_name' => 'pl.meta_title'
		);

		//keywords
		$this->fields['keywords'] = array(
			'display_name' => $this->module->l('Meta keywords', $this->languages_name),
			'name' => 'keywords',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_STR,
			'sort' => true,
			'base_name' => 'pl.meta_keywords'
		);

		//description
		$this->fields['description'] = array(
			'display_name' => $this->module->l('Meta description', $this->languages_name),
			'name' => 'description',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_STR,
			'sort' => true,
			'base_name' => 'pl.meta_description'
		);

		//link_rewrite
		$this->fields['link_rewrite'] = array(
			'display_name' => $this->module->l('Link rewrite', $this->languages_name),
			'name' => 'link_rewrite',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_STR,
			'sort' => true,
			'base_name' => 'pl.link_rewrite'
		);

		//available_now
		$this->fields['available_now'] = array(
			'display_name' => $this->module->l('Available now', $this->languages_name),
			'name' => 'available_now',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_STR,
			'sort' => true,
			'base_name' => 'pl.available_now'
		);

		//available_later
		$this->fields['available_later'] = array(
			'display_name' => $this->module->l('Available later', $this->languages_name),
			'name' => 'available_later',
			'active' => true,
			'lang' => true,
			'type' => self::TYPE_STR,
			'sort' => true,
			'base_name' => 'pl.available_later'
		);
	}

	public function save(array $data)
	{
		$result = array();
		$end = false;
		if ($data)
			foreach ($data as $id_product => $params)
			{
				$result[$id_product] = array(
					'combinations' => array(),
					'error' => true,
					'message' => ''
				);

				$product_params = array_key_exists('data', $params) ? $params['data'] : null;

				$product = new Product($id_product);

				if (!Validate::isLoadedObject($product))
				{
					$result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
					continue;
				}

				if ($product_params)
				{
					$languages = Language::getLanguages(false);
                    
					if ($languages)
						foreach ($languages as $language)
						{
                                                        if (isset($product_params['name_'.$language['iso_code']]))
								$product->name[$language['id_lang']] = $product_params['name_'.$language['iso_code']];
							if (isset($product_params['title_'.$language['iso_code']]))
								$product->meta_title[$language['id_lang']] = $product_params['title_'.$language['iso_code']];

							if (isset($product_params['product_description_'.$language['iso_code']]))
							{
								$product->description[$language['id_lang']] = $product_params['product_description_'.$language['iso_code']];
							}

							if (isset($product_params['product_short_description_'.$language['iso_code']]))
							{
								$product->description_short[$language['id_lang']] = $product_params['product_short_description_'.$language['iso_code']];
							}

							if (isset($product_params['keywords_'.$language['iso_code']]))
								$product->meta_keywords[$language['id_lang']] = $product_params['keywords_'.$language['iso_code']];

							if (isset($product_params['description_'.$language['iso_code']]))
								$product->meta_description[$language['id_lang']] = $product_params['description_'.$language['iso_code']];

							if (isset($product_params['link_rewrite_'.$language['iso_code']]))
								$product->link_rewrite[$language['id_lang']] = $product_params['link_rewrite_'.$language['iso_code']];

							if (isset($product_params['available_now_'.$language['iso_code']]))
								$product->available_now[$language['id_lang']] = $product_params['available_now_'.$language['iso_code']];

							if (isset($product_params['available_later_'.$language['iso_code']]))
								$product->available_later[$language['id_lang']] = $product_params['available_later_'.$language['iso_code']];
						}
					$errors = $product->validateFields(false, true);
					$errors2 = $product->validateFieldsLang(false, true);
					if ($errors !== true || $errors2 !== true)
					{
						$result[$id_product]['message'] = '';
						if ($errors !== true)
							$result[$id_product]['message'] .= '<p style="color: #FFF;">'.(is_bool($errors) ?
									$this->module->l('Validate error', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors)).'</p>';
						if ($errors2 !== true)
							$result[$id_product]['message'] .= '<p style="color: #FFF;">'.(is_bool($errors2) ?
									$this->module->l('Validate error', $this->languages_name) : (is_array($errors2) ? implode(' | ', $errors2) : $errors2)).'</p>';
						continue;
					}
					else
					{
						if ($product->update())
						{
							$result[$id_product]['error'] = false;
							$result[$id_product]['message'] = $this->displayProduct($product);
						}
						else
						{
							$result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
							continue;
						}
					}
				}
				else
				{
					$result[$id_product]['error'] = false;
					$result[$id_product]['message'] = $this->displayProduct($product);
				}
			}
		else
			$end = true;

		return array(
			'raport' => $result,
			'end' => $end
		);
	}

	public function display($result)
	{
		$ids_product = $result['result'];
		$result['result'] = '';
		$result['table'] = true;

		if ($ids_product)
			foreach ($ids_product as $product_arr)
			{
				$product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
				if (!Validate::isLoadedObject($product))
				{
					$result['dates']['products_count'] --;
					continue;
				}

				$result['result'] .= $this->displayProduct($product);
			}

		return $result;
	}

	public function displayProduct(&$product)
	{
		parent::displayProduct($product);
		$product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
				$this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
		$product->quantity = $this->object->shop_group->share_stock ? $product->quantity : StockAvailable::getQuantityAvailableByProduct($product->id);
		$product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;
		$product->full_name = $product->name[$this->context->language->id];
		$this->fields['product_description']['value'] = $product->description;
		$this->fields['product_short_description']['value'] = $product->description_short;
		$this->fields['title']['value'] = $product->meta_title;
		$this->fields['keywords']['value'] = $product->meta_keywords;
		$this->fields['description']['value'] = $product->meta_description;
		$this->fields['link_rewrite']['value'] = $product->link_rewrite;
		$this->fields['available_now']['value'] = $product->available_now;
		$this->fields['available_later']['value'] = $product->available_later;
                $this->fields['name']['value'] = $product->name;

		$this->fields['product_description']['class'] = 'ta-tiny p-desc';
		$this->fields['product_short_description']['class'] = 'ta-tiny ps-desc';

		$this->context->smarty->assign(array(
			'product' => $product,
			'fields' => $this->getFields(),
			'languages' => $this->object->languages,
			'iso' => file_exists(_PS_ROOT_DIR_.'/js/tinymce/langs/'.$iso.'.js') ? $iso : 'en',
			'path_css' => _THEME_CSS_DIR_,
			'ad' => dirname($_SERVER['PHP_SELF']),
			'tinymce' => true
		));

		return $this->object->createTemplate('tr-product.tpl')->fetch();
	}

	public function displayCombination(&$product, &$combination)
	{
		if (!parent::displayCombination($product, $combination))
			return '';
	}
}