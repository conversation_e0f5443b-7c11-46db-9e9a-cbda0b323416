<div class="inspiration-categories-container">
    <div class="panel">
        <div class="panel-heading">
            {l s='Inspiration Categories' d='Modules.Webixapreview.Admin'}
        </div>
        <div class="form-wrapper">
            <div class="form-group">
                <div class="col-lg-12">
                    <div class="category-tree-container">
                        {if $inspiration_categories|count > 0}
                            <div class="category-tree">
                                <ul class="tree">
                                    {foreach from=$inspiration_categories item=category}
                                        <li class="tree-item">
                                            <span class="tree-item-name">
                                                <input type="checkbox" name="categoryBox[]" value="{$category.id_inspiration_category}" 
                                                    {if $selected_categories && $category.id_inspiration_category|in_array:$selected_categories}checked="checked"{/if} />
                                                <label>{$category.name|escape:'html':'UTF-8'}</label>
                                            </span>
                                            {if isset($category.children) && $category.children|count > 0}
                                                <ul class="tree">
                                                    {foreach from=$category.children item=child}
                                                        <li class="tree-item">
                                                            <span class="tree-item-name">
                                                                <input type="checkbox" name="categoryBox[]" value="{$child.id_inspiration_category}" 
                                                                    {if $selected_categories && $child.id_inspiration_category|in_array:$selected_categories}checked="checked"{/if} />
                                                                <label>{$child.name|escape:'html':'UTF-8'}</label>
                                                            </span>
                                                        </li>
                                                    {/foreach}
                                                </ul>
                                            {/if}
                                        </li>
                                    {/foreach}
                                </ul>
                            </div>
                        {else}
                            <div class="alert alert-info">
                                {l s='No categories available. Please create categories first.' d='Modules.Webixapreview.Admin'}
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.category-tree {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f8f8f8;
}
.tree {
    list-style: none;
    padding-left: 15px;
}
.tree-item {
    margin-bottom: 5px;
}
.tree-item-name {
    display: flex;
    align-items: center;
}
.tree-item-name label {
    margin-left: 5px;
    margin-bottom: 0;
    font-weight: normal;
}
</style>
