<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

class WebixaContentLink extends ObjectModel
{
    const _TYPE_IMG_DIR_ = 'img/links';
    const _IMG_DIR_ = _PS_MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    const _IMG_PATH_ = _MODULE_DIR_ . 'webixa_content/' . self::_TYPE_IMG_DIR_ . '/';
    public $image_dir = self::_IMG_DIR_;

    public $id_webixa_content_block;
    public $image = false;
    public $position;
    public $active = true;
    public $title;
    public $description;
    public $link;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'webixa_content_link',
        'primary' => 'id_webixa_content_link',
        'multilang' => true,
        'fields' => [
            'id_webixa_content_block' => ['type' => self::TYPE_INT, 'validate' => 'isInt', 'required' => true],
            'position' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => false],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            /* Lang fields */
            'title' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml', 'size' => 128],
            'description' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml', 'size' => 3999999999999],
            'link' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isUrl', 'size' => 512],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null, $translator = null)
    {
        if (version_compare(_PS_VERSION_, '1.7.1', '<')) {
            parent::__construct($id, $id_lang, $id_shop);
        } else {
            parent::__construct($id, $id_lang, $id_shop, $translator);
        }
        if (file_exists(self::_IMG_DIR_ . $this->id . '.' . $this->image_format)) {
            $this->image = self::_IMG_PATH_ . $this->id . '.' . $this->image_format;
        }
    }

    public function add($auto_date = true, $null_values = false)
    {
        $this->position = static::getLastPosition($this->id_webixa_content_block);

        return parent::add($auto_date, $null_values);
    }

    public function delete()
    {
        if (parent::delete()) {
            return static::cleanPositions($this->id_webixa_content_block);
        }

        return false;
    }

    public function deleteImage($force_delete = false)
    {
        if ($force_delete || !$this->hasMultishopEntries()) {
            foreach (Shop::getShops(false, null, true) as $id_shop) {
                if (
                    file_exists(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                    && !unlink(_PS_TMP_IMG_DIR_ . $this->def['table'] . '_mini_' . $this->id . '_' . (int)$id_shop . '.' . $this->image_format)
                ) {
                    return false;
                }
            }
        }
        return parent::deleteImage($force_delete);
    }

    public static function updatePositions(array $positions)
    {
        // todo: check if this work properly
        $query = 'UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '` SET `position` = CASE `' . static::$definition['primary'] . '` ';

        foreach ($positions as $pos => $args) {
            preg_match('/tr_\d+_(\d+)_\d+/', $args, $matches);
            if (!empty($matches[1])) {
                $query .= 'WHEN ' . $matches[1] . ' THEN ' . $pos . ' ';
            }
        }

        $query .= 'ELSE `position` END WHERE 1';

        return Db::getInstance()->execute($query);
    }

    public static function cleanPositions($blockId)
    {
        return Db::getInstance()->execute('
            UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '` psi1
            JOIN (
                SELECT `' . static::$definition['primary'] . '`, @i := @i+1 new_position
                FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`, (select @i:=-1) temp
                WHERE `' . WebixaContentBlock::$definition['primary'] . '`=' . (int)$blockId . '
                ORDER BY position asc
            ) psi2 ON psi1.`' . static::$definition['primary'] . '` = psi2.`' . static::$definition['primary'] . '`
            SET psi1.position = psi2.new_position
        ');
    }

    public static function getLastPosition($blockId)
    {
        $sql = '
		SELECT MAX(`position`)
		FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`
		WHERE `' . WebixaContentBlock::$definition['primary'] . '`=' . (int)$blockId;
        $value = Db::getInstance()->getValue($sql);

        return null === $value ? 0 : (int) $value + 1;
    }

    public static function getActiveByWebixaContentBlockId($idBlock, $idLang = null, $idShop = null)
    {
        $links = [];
        $query = new DbQuery();
        $query->select('a.' . static::$definition['primary']);
        $query->from(static::$definition['table'], 'a');
        $query->where('a.active=' . 1);
        $query->where('a.' . WebixaContentBlock::$definition['primary'] . '=' . (int)$idBlock);

        if (null !== $idShop) {
            $query->innerJoin(
                static::$definition['table'] . '_shop',
                'sa',
                'a.' . static::$definition['primary'] . '=' . 'sa.' . static::$definition['primary'] . ' AND sa.id_shop=' . (int) $idShop
            );
        }
        if (null !== $idLang) {
            $query->innerJoin(
                static::$definition['table'] . '_lang',
                'al',
                'a.' . static::$definition['primary'] . '=' . 'al.' . static::$definition['primary'] . ' AND al.id_lang=' . (int) $idLang
            );

            $query->where('al.title IS NOT NULL AND al.title<>""');
        }

        $query->orderBy('a.position ASC');
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
        foreach ($result as $row) {
            $webixaContentLink = new WebixaContentLink($row[static::$definition['primary']], $idLang, $idShop);
            if (Validate::isLoadedObject($webixaContentLink)) {
                $links[] = $webixaContentLink;
            }
        }

        return $links;
    }
}
