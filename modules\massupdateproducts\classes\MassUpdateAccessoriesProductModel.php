<?php

class MassUpdateAccessoriesProductModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateAccessoriesProductModel');
        parent::__construct($module, $object, $context);
        //name
        $this->fields['accessories_off'] = array(
            'display_name' => $this->module->l('Accessories', $this->languages_name),
            'name' => 'accessories_off',
            'active' => true,
            'type' => self::TYPE_ACCESSORIES_OFF,
            'lang' => false
        );

        $this->fields['accessories_on'] = array(
            'display_name' => $this->module->l('Accessories on', $this->languages_name),
            'name' => 'accessories_on',
            'active' => true,
            'type' => self::TYPE_ACCESSORIES_ON,
            'lang' => false
        );
        
        $this->fields['accessories_remove'] = array(
            'display_name' => $this->module->l('Remove accessory', $this->languages_name),
            'name' => 'accessories_remove',
            'active' => true,
            'type' => self::TYPE_BOOL,
            'lang' => false
        );
    }

    public function filterOn($count = false, $ids = array())
    {
        if (Tools::isSubmit('filter')) {
            $categories = Tools::getValue('categories', null);
            $categories_default = Tools::getValue('categories_default', null);
            $manufacturer = Tools::getValue('manufacturer', null);
            $help_arr = explode('-', Tools::getValue('price', ''));
            $price_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $price_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('quantity', ''));
            $quantity_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[0]) : null;
            $quantity_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('weight', ''));
            $weight_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $weight_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $date_add_from = Tools::getValue('date_add_from', null);
            $date_add_to = Tools::getValue('date_add_to', null);
            $date_upd_from = Tools::getValue('date_update_from', null);
            $date_upd_to = Tools::getValue('date_update_to', null);
            $active = (int)Tools::getValue('active', null);
            $promotion = (int)Tools::getValue('promotion', null);
            $show_empty = (int)Tools::getValue('show_empty', null);
            $page = (int)Tools::getValue('page', 0);
            $elements = (int)Tools::getValue('elements', 20);
            $product_name = pSQL(Tools::getValue('product', null));
            $sorted_type = (int)Tools::getValue('sorted_type', 0);
            $sorted_by = pSQL(Tools::getValue('sorted_by', ''));

            $select = '';
            $limit = '';
            $method = '';

            $from = 'FROM `'._DB_PREFIX_.'product` as p ';
            $join = 'JOIN `'._DB_PREFIX_.'product_shop` as ps ON p.id_product = ps.id_product AND ps.id_shop = '.pSQL($this->context->shop->id).' ';
            $join .= 'JOIN `'._DB_PREFIX_.'product_lang` as pl ON p.id_product = pl.id_product AND ps.id_shop = pl.id_shop AND pl.id_lang = '
                    .(int)$this->context->language->id.' ';

            $where = 'WHERE 1=1 AND ps.`id_product` IN (SELECT acc.`id_product_2` FROM `'._DB_PREFIX_.'accessory` as acc WHERE acc.`id_product_1` IN ('.implode(',', $ids).')) ';
            $where_e = '';
            $where_e .= $price_from ? ('AND ps.price >= '.$price_from.' ') : '';
            $where_e .= $price_to ? ('AND ps.price <= '.$price_to.' ') : '';
            $where .= $categories_default ? ('AND ps.id_category_default IN ('.implode(',', $categories_default).') ') : '';
            $where_e .= $weight_from ? ('AND p.weight >= '.$weight_from.' ') : '';
            $where_e .= $weight_to ? ('AND p.weight <= '.$weight_to.' ') : '';
            $where .= $product_name ? ('AND (pl.name LIKE \'%'.$product_name.'%\' OR p.reference LIKE \'%'.$product_name.'%\') ') : '';
            $where .= $date_add_from ? ('AND ps.date_add >= \''.$date_add_from.'\' ') : '';
            $where .= $date_add_to ? ('AND ps.date_add <= \''.$date_add_to.'\' ') : '';
            $where .= $date_upd_from ? ('AND ps.date_upd >= \''.$date_upd_from.'\' ') : '';
            $where .= $date_upd_to ? ('AND ps.date_upd <= \''.$date_upd_to.'\' ') : '';
            //$where .= $ids ? 'AND ps.id_product IN ('.implode(',', $ids).') ' : '';
            $where .= $active ? ($active == 1 ? 'AND ps.active = 1 ' : 'AND ps.active =  0 ') : '';
            $where .= $manufacturer ? ('AND p.id_manufacturer IN ('.implode(',', $manufacturer).') ') : '';
            $where .= $categories ? ('AND ps.id_product IN (SELECT cp.id_product FROM `'
                    ._DB_PREFIX_.'category_product` as cp WHERE cp.id_category IN ('.
                    implode(',', $categories).'))') : '';

            if (!(int)$this->object->shop_group->share_stock) {
                $join .= 'JOIN `'._DB_PREFIX_
                        .'stock_available` as sa ON p.id_product = sa.id_product AND sa.id_product_attribute = 0 AND sa.id_shop = ps.id_shop ';
                $where_e .= $quantity_from ? ('AND sa.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND sa.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND sa.quantity = 0 ' : ($show_empty == 2 ? ' AND sa.quantity != 0 ' : '') );
            } else {
                $where_e .= $quantity_from ? ('AND p.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND p.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND p.quantity = 0 ' : ($show_empty == 2 ? ' AND p.quantity != 0 ' : '') );
            }

            if ($promotion == 1)
                $where .= ' AND p.id_product IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp) ';
            elseif ($promotion == 2)
                $where .= ' AND p.id_product NOT IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp WHERE sp.id_product_attribute = 0) ';

            $sort_name = (int)Tools::getValue('sort_name');
            $sort = ' ORDER BY '.($sorted_by ? $sorted_by.' ' : ($sort_name ? 'pl.name ' : 'ps.id_product '));

            if ($sorted_type)
                $sort .= 'DESC ';
            else
                $sort .= 'ASC ';

            if (!$count) {
                if ($page < 1)
                    $page = 1;
                $select = 'SELECT p.id_product, ps.id_shop ';
                $limit = 'LIMIT '.(($page - 1) * $elements).','.$elements;
                $method = 'executeS';
            }
            else {
                $select = 'SELECT COUNT(p.id_product) as q ';
                $limit = '';
                $method = 'getValue';
            }

            return array(
                'datas' => array_merge($this->object->getDatas($from.$join.$where.$where_e), array('product_count' =>
                    Db::getInstance()->getValue('SELECT COUNT(p.id_product) as product_count '.
                            $from.$join.$where.$where_e))),
                'result' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort.$limit),
                'result2' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort),
                'sql' => $select.$from.$join.$where.$where_e.$sort.$limit
            );
        }
        return null;
    }

    public function filterOff($count = false, $ids = array())
    {
        if (Tools::isSubmit('filter')) {
            $categories = Tools::getValue('categories', null);
            $categories_default = Tools::getValue('categories_default', null);
            $manufacturer = Tools::getValue('manufacturer', null);
            $help_arr = explode('-', Tools::getValue('price', ''));
            $price_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $price_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('quantity', ''));
            $quantity_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[0]) : null;
            $quantity_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9-]/', '', $help_arr[1]) : null;
            $help_arr = explode(' - ', Tools::getValue('weight', ''));
            $weight_from = isset($help_arr[0]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[0]) : null;
            $weight_to = isset($help_arr[1]) ? (int)preg_replace('/[^0-9]/', '', $help_arr[1]) : null;
            $date_add_from = Tools::getValue('date_add_from', null);
            $date_add_to = Tools::getValue('date_add_to', null);
            $date_upd_from = Tools::getValue('date_update_from', null);
            $date_upd_to = Tools::getValue('date_update_to', null);
            $active = (int)Tools::getValue('active', null);
            $promotion = (int)Tools::getValue('promotion', null);
            $show_empty = (int)Tools::getValue('show_empty', null);
            $page = (int)Tools::getValue('page', 0);
            $elements = (int)Tools::getValue('elements', 20);
            $product_name = pSQL(Tools::getValue('product', null));
            $sorted_type = (int)Tools::getValue('sorted_type', 0);
            $sorted_by = pSQL(Tools::getValue('sorted_by', ''));

            $select = '';
            $limit = '';
            $method = '';

            $from = 'FROM `'._DB_PREFIX_.'product` as p ';
            $join = 'JOIN `'._DB_PREFIX_.'product_shop` as ps ON p.id_product = ps.id_product AND ps.id_shop = '.pSQL($this->context->shop->id).' ';
            $join .= 'JOIN `'._DB_PREFIX_.'product_lang` as pl ON p.id_product = pl.id_product AND ps.id_shop = pl.id_shop AND pl.id_lang = '
                    .(int)$this->context->language->id.' ';

            $where = 'WHERE 1=1 AND ps.`id_product` NOT IN ('.implode(',', $ids).') ';
            $where_e = '';
            $where_e .= $price_from ? ('AND ps.price >= '.$price_from.' ') : '';
            $where_e .= $price_to ? ('AND ps.price <= '.$price_to.' ') : '';
            $where .= $categories_default ? ('AND ps.id_category_default IN ('.implode(',', $categories_default).') ') : '';
            $where_e .= $weight_from ? ('AND p.weight >= '.$weight_from.' ') : '';
            $where_e .= $weight_to ? ('AND p.weight <= '.$weight_to.' ') : '';
            $where .= $product_name ? ('AND (pl.name LIKE \'%'.$product_name.'%\' OR p.reference LIKE \'%'.$product_name.'%\') ') : '';
            $where .= $date_add_from ? ('AND ps.date_add >= \''.$date_add_from.'\' ') : '';
            $where .= $date_add_to ? ('AND ps.date_add <= \''.$date_add_to.'\' ') : '';
            $where .= $date_upd_from ? ('AND ps.date_upd >= \''.$date_upd_from.'\' ') : '';
            $where .= $date_upd_to ? ('AND ps.date_upd <= \''.$date_upd_to.'\' ') : '';
            //$where .= $ids ? 'AND ps.id_product IN ('.implode(',', $ids).') ' : '';
            $where .= $active ? ($active == 1 ? 'AND ps.active = 1 ' : 'AND ps.active =  0 ') : '';
            $where .= $manufacturer ? ('AND p.id_manufacturer IN ('.implode(',', $manufacturer).') ') : '';
            $where .= $categories ? ('AND ps.id_product IN (SELECT cp.id_product FROM `'
                    ._DB_PREFIX_.'category_product` as cp WHERE cp.id_category IN ('.
                    implode(',', $categories).'))') : '';

            if (!(int)$this->object->shop_group->share_stock) {
                $join .= 'JOIN `'._DB_PREFIX_
                        .'stock_available` as sa ON p.id_product = sa.id_product AND sa.id_product_attribute = 0 AND sa.id_shop = ps.id_shop ';
                $where_e .= $quantity_from ? ('AND sa.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND sa.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND sa.quantity = 0 ' : ($show_empty == 2 ? ' AND sa.quantity != 0 ' : '') );
            } else {
                $where_e .= $quantity_from ? ('AND p.quantity >= '.$quantity_from.' ') : '';
                $where_e .= $quantity_to ? ('AND p.quantity <= '.$quantity_to.' ') : '';
                $where_e .= ($show_empty == 1 ? ' AND p.quantity = 0 ' : ($show_empty == 2 ? ' AND p.quantity != 0 ' : '') );
            }

            if ($promotion == 1)
                $where .= ' AND p.id_product IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp) ';
            elseif ($promotion == 2)
                $where .= ' AND p.id_product NOT IN (SELECT sp.id_product FROM `'._DB_PREFIX_.'specific_price` as sp WHERE sp.id_product_attribute = 0) ';
            $sort_name = (int)Tools::getValue('sort_name');
            $sort = ' ORDER BY '.($sorted_by ? $sorted_by.' ' : ($sort_name ? 'pl.name ' : 'ps.id_product '));

            if ($sorted_type)
                $sort .= 'DESC ';
            else
                $sort .= 'ASC ';

            if (!$count) {
                if ($page < 1)
                    $page = 1;
                $select = 'SELECT p.id_product, ps.id_shop ';
                $limit = 'LIMIT '.(($page - 1) * $elements).','.$elements;
                $method = 'executeS';
            }
            else {
                $select = 'SELECT COUNT(p.id_product) as q ';
                $limit = '';
                $method = 'getValue';
            }
            return array(
                'datas' => array_merge($this->object->getDatas($from.$join.$where.$where_e), array('product_count' =>
                    Db::getInstance()->getValue('SELECT COUNT(p.id_product) as product_count '.
                            $from.$join.$where.$where_e))),
                'result' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort.$limit),
                'result2' => Db::getInstance()->$method($select.$from.$join.$where.$where_e.$sort),
                'sql' => $select.$from.$join.$where.$where_e.$sort.$limit
            );
        }
        return null;
    }

    public function displayAccessories($products, $filter_name)
    {
        $id_product = Tools::getValue('id_product');
        $product_result = array();
        if ($products['result']) {
            foreach ($products['result'] as &$product) {
                $product_object = new Product($product['id_product'], false, $this->context->language->id, $product['id_shop']);
                $product_object->is_accessories = !!Db::getInstance()->getValue('SELECT `id_product_2` FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_1` = '.pSQL($id_product).' AND `id_product_2` = '.pSQL($product_object->id));
                $product_object->is_mirror = !!Db::getInstance()->getValue('SELECT `id_product_2` FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_2` = '.pSQL($id_product).' AND `id_product_1` = '.pSQL($product_object->id));
                $product_result[] = $product_object;
            }
        }

        $page = (int)Tools::getValue('page', 1);
        if ($page < 1) {
            $page = 1;
        }
        $this->context->smarty->assign(array(
            'select' => $product_result,
            'count' => $products['datas']['product_count'],
            'page' => $page
        ));

        $content = $this->object->createTemplate('fields/field_'.($filter_name == 'filterOff' ? '13' : '14').'.tpl')->fetch();

        return array(
            'products' => $products,
            'filter_name' => $filter_name,
            'content' => $content
        );
    }

    public function display($result)
    {
        $ids_product = $result['result'];
        $result['result'] = '';
        $result['table'] = true;

        if ($ids_product)
            foreach ($ids_product as $product_arr) {
                $product = new Product($product_arr['id_product'], false, null, $product_arr['id_shop']);
                if (!Validate::isLoadedObject($product)) {
                    $result['dates']['products_count'] --;
                    continue;
                }
                $product->id_shop_object = $product_arr['id_shop'];
                $result['result'] .= $this->displayProduct($product);
            }

        return $result;
    }

    public function displayProduct(&$product)
    {
        parent::displayProduct($product);
        $product->image_link = $product->getCoverWs() ? $this->context->link->getImageLink(
                        $this->object->img_type, $product->getCoverWs(), $this->object->img_type) : '';
        $product->full_name = $product->name[$this->context->language->id];
        $this->context->smarty->assign(array(
            'product' => $product,
            'fields' => $this->getFields(),
            'languages' => $this->object->languages,
            'iso' => file_exists(_PS_ROOT_DIR_.'/js/tinymce/langs/'.$this->object->language->iso_code.'.js') ? $this->object->language->iso_code : 'en',
            'path_css' => _THEME_CSS_DIR_,
            'ad' => dirname($_SERVER['PHP_SELF'])
        ));

        return $this->object->createTemplate('tr-product.tpl')->fetch();
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product => $params) {
                $result[$id_product] = array(
                    'combinations' => array(),
                    'error' => true,
                    'message' => ''
                );

                $product_params = array_key_exists('data', $params) ? $params['data'] : null;

                $product = new Product($id_product);

                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Product not found', $this->languages_name);
                    continue;
                }

                if ($product_params) {
                    try {
                        Db::getInstance()->execute('START TRANSACTION');
                        if (isset($product_params['accessories_off']) && $product_params['accessories_off']) {
                            $accessories_to_off = array();
                            $accessories_to_on = array();
                            foreach ($product_params['accessories_off'] as $id => $p_p) {
                                if ((int)$p_p) {
                                    $accessories_to_on[] = $id;
                                } else {
                                    $accessories_to_off[] = $id;
                                }
                            }

                            if ($accessories_to_off) {
                                Db::getInstance()->execute('DELETE FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_1` = '.pSQL($id_product).' AND `id_product_2` IN ('.implode(',', $accessories_to_off).')');
                            }

                            if ($accessories_to_on) {
                                foreach ($accessories_to_on as $id) {
                                    if (!Db::getInstance()->getValue('SELECT COUNT(*) FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_2` = '.pSQL($id).' AND `id_product_1` = '.pSQL($id_product))) {
                                        Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_.'accessory` (`id_product_1`, `id_product_2`) VALUES ('.pSQL($id_product).', '.pSQL($id).')');
                                    }
                                }
                            }
                        }

                        if (isset($product_params['accessories_on']) && $product_params['accessories_on']) {
                            $accessories_to_off = array();
                            foreach ($product_params['accessories_on'] as $id => $p_p) {
                                if (!(int)$p_p) {
                                    $accessories_to_off[] = $id;
                                }
                            }

                            if ($accessories_to_off) {
                                Db::getInstance()->execute('DELETE FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_1` = '.pSQL($id_product).' AND `id_product_2` IN ('.implode(',', $accessories_to_off).')');
                            }
                        }

                        if (isset($product_params['accessories_mirror_off']) && $product_params['accessories_mirror_off']) {
                            $accessories_to_off = array();
                            $accessories_to_on = array();
                            foreach ($product_params['accessories_mirror_off'] as $id => $p_p) {
                                if ((int)$p_p) {
                                    $accessories_to_on[] = $id;
                                } else {
                                    $accessories_to_off[] = $id;
                                }
                            }

                            if ($accessories_to_off) {
                                Db::getInstance()->execute('DELETE FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_2` = '.pSQL($id_product).' AND `id_product_1` IN ('.implode(',', $accessories_to_off).')');
                            }

                            if ($accessories_to_on) {
                                foreach ($accessories_to_on as $id) {
                                    if (!Db::getInstance()->getValue('SELECT COUNT(*) FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_1` = '.pSQL($id).' AND `id_product_2` = '.pSQL($id_product))) {
                                        Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_.'accessory` (`id_product_2`, `id_product_1`) VALUES ('.pSQL($id_product).', '.pSQL($id).')');
                                    }
                                }
                            }
                        }


                        if (isset($product_params['accessories_mirror_on']) && $product_params['accessories_mirror_on']) {
                            $accessories_to_off = array();
                            $accessories_to_on = array();
                            foreach ($product_params['accessories_mirror_on'] as $id => $p_p) {
                                if ((int)$p_p) {
                                    $accessories_to_on[] = $id;
                                } else {
                                    $accessories_to_off[] = $id;
                                }
                            }

                            if ($accessories_to_off) {
                                Db::getInstance()->execute('DELETE FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_2` = '.pSQL($id_product).' AND `id_product_1` IN ('.implode(',', $accessories_to_off).')');
                            }

                            if ($accessories_to_on) {
                                foreach ($accessories_to_on as $id) {
                                    if (!Db::getInstance()->getValue('SELECT COUNT(*) FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_1` = '.pSQL($id).' AND `id_product_2` = '.pSQL($id_product))) {
                                        Db::getInstance()->execute('INSERT INTO `'._DB_PREFIX_.'accessory` (`id_product_2`, `id_product_1`) VALUES ('.pSQL($id_product).', '.pSQL($id).')');
                                    }
                                }
                            }
                        }
                        
                        if ((int)$product_params['accessories_remove']) {
                            Db::getInstance()->execute('DELETE FROM `'._DB_PREFIX_.'accessory` WHERE `id_product_1` = '.pSQL($id_product));
                        }
                        
                        Db::getInstance()->execute('COMMIT');
                        $result[$id_product]['error'] = false;
                        $result[$id_product]['message'] = $this->displayProduct($product);
                    } catch (Exception $e) {
                        Db::getInstance()->execute('ROLLBACK');
                        $result[$id_product]['message'] = $this->module->l('Problem with update', $this->languages_name);
                        continue;
                    }

                    if ($product->update()) {
                        
                    }
                }
            } else
            $end = true;

        return array(
            'raport' => $result,
            'end' => $end
        );
    }

}
