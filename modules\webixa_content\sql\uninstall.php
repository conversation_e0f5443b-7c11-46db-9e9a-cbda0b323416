<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

$sql = [];

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_hook_block`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_hook`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_item_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_item_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_item`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_link_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_link_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_link`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_slide_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_slide_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_slide`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_categoryproducts_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_categoryproducts`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_group_restriction`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_category_restriction`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_block_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_block_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'webixa_content_block`';



foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        throw new Exception('Uninstall DB failed');
    }
}

return true;
