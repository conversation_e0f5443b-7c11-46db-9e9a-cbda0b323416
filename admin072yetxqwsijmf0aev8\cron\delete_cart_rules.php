<?php

require dirname(__FILE__) . '/../../config/config.inc.php';

$sql = [];
$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule
    WHERE description = "<PERSON>bat do afiliacji" AND (code LIKE "So%") AND code NOT LIKE "sovExvh1hs5"
';

$sql[] = '
    DELETE ' . _DB_PREFIX_ . 'cart_rule FROM ' . _DB_PREFIX_ . 'cart_rule
    LEFT JOIN ' . _DB_PREFIX_ . 'pshow_lp_exchange ON(' . _DB_PREFIX_ . 'pshow_lp_exchange.id_voucher=' . _DB_PREFIX_ . 'cart_rule.id_cart_rule)
    WHERE
    ' . _DB_PREFIX_ . 'cart_rule.quantity=0 AND ' . _DB_PREFIX_ . 'cart_rule.date_to <"' . date('Y-m-d H:i:s', strtotime('-1 month')) . '" AND ' . _DB_PREFIX_ . 'pshow_lp_exchange.id_voucher IS NULL
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_carrier
    WHERE id_cart_rule IN(
        SELECT
        vcrc.id_cart_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_carrier vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule vcr ON(vcrc.id_cart_rule=vcr.id_cart_rule)
        WHERE vcr.id_cart_rule IS NULL
    )
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_country
    WHERE id_cart_rule IN(
        SELECT
        vcrc.id_cart_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_country vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule vcr ON(vcrc.id_cart_rule=vcr.id_cart_rule)
        WHERE vcr.id_cart_rule IS NULL
    )
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_group
    WHERE id_cart_rule IN(
        SELECT
        vcrc.id_cart_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_group vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule vcr ON(vcrc.id_cart_rule=vcr.id_cart_rule)
        WHERE vcr.id_cart_rule IS NULL
    )
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_lang
    WHERE id_cart_rule IN(
        SELECT
        vcrc.id_cart_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_lang vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule vcr ON(vcrc.id_cart_rule=vcr.id_cart_rule)
        WHERE vcr.id_cart_rule IS NULL
    )
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_shop
    WHERE id_cart_rule IN(
        SELECT
        vcrc.id_cart_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_shop vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule vcr ON(vcrc.id_cart_rule=vcr.id_cart_rule)
        WHERE vcr.id_cart_rule IS NULL
    )
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_product_rule_group
    WHERE id_cart_rule IN(
        SELECT
        vcrc.id_cart_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_product_rule_group vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule vcr ON(vcrc.id_cart_rule=vcr.id_cart_rule)
        WHERE vcr.id_cart_rule IS NULL
    )
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_product_rule
    WHERE id_product_rule IN(
        SELECT
        vcrc.id_product_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_product_rule vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule_product_rule_group vcr ON(vcrc.id_product_rule_group=vcr.id_product_rule_group)
        WHERE vcr.id_product_rule_group IS NULL
    )
';

$sql[] = '
    DELETE FROM ' . _DB_PREFIX_ . 'cart_rule_product_rule_value
    WHERE id_product_rule IN(
        SELECT
        vcrc.id_product_rule
        FROM ' . _DB_PREFIX_ . 'cart_rule_product_rule_value vcrc
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule_product_rule vcr ON(vcrc.id_product_rule=vcr.id_product_rule)
        WHERE vcr.id_product_rule IS NULL
    )
';


$db = Db::getInstance();
foreach ($sql as $query) {
    $db->execute($query);
}

return 1;
