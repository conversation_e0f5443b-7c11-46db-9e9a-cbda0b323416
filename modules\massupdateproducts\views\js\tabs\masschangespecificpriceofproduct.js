/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function() {
    var $products_list = $('#products_list').find('.main_body');

    $products_list.on('change', '.to-send[send-name="reduction"]', function() {
	var $scope = $(this);
	var $tr = $scope.closest('.element-row');
	var $old_price = $tr.find('.to-send[send-name="old_price"]');
	var $new_price = $tr.find('.to-send[send-name="promotion_price"]');
	var $type = $tr.find('.to-send[send-name="reduction_type"]');

	switch (parseInt($type.val()))
	{
	    case 1:
		$new_price.val((parseFloat($old_price.val()) - parseFloat($scope.val())).toFixed(2));
		break;
	    case 2:
		$new_price.val((parseFloat($old_price.val()) - (parseFloat(parseFloat($old_price.val()) * (parseFloat($scope.val()) / 100)))).toFixed(2));
		break;
	    default:
		$new_price.val(0);
		break;
	}
    });

    $products_list.on('change', '.to-send[send-name="reduction_type"]', function() {
	var $scope = $(this);
	var $tr = $scope.closest('.element-row');
	var $reduction = $tr.find('.to-send[send-name="reduction"]');
	$reduction.trigger('change');
    });

    $products_list.on('filter', function() {
	$products_list.find('.product-row, .combination-row').each(function() {
	    var $main = $(this);
	    $main.find('.to-send[send-name="date_from"]').datepicker({
		changeMonth: true,
		dateFormat: "yy-mm-dd",
		numberOfMonths: 3,
		onClose: function(selectedDate) {
		    $main.find('.to-send[send-name="date_to"]').datepicker("option", "minDate", selectedDate);
		}
	    });
	    $main.find('.to-send[send-name="date_to"]').datepicker({
		changeMonth: true,
		dateFormat: "yy-mm-dd",
		numberOfMonths: 3,
		onClose: function(selectedDate) {
		    $main.find('.to-send[send-name="date_from"]').datepicker("option", "maxDate", selectedDate);
		}
	    });
	});

    });

    $products_list.on('click', '.icon-remove', function() {
	var $scope = $(this);
	var $tr = $scope.closest('.element-row');

	if (massProcess())
	    return false;

	$massupdateproductsProcess = true;

	$.ajax({
	    data: {
		remove: true,
		is_ajax: true,
		id_specific: $scope.attr('s-id')
	    },
	    type: 'POST',
	    dataType: 'json',
	    success: function($response) {
		if ($response.error)
		    $.notify($response.message, 'error', {
			autoHideDelay: 2000
		    });
		else
		{
		    $.notify($response.message, 'success', {
			autoHideDelay: 2000
		    });
		    $massupdateproductsProcess = false;
		    $tr.find('.fa-refresh').trigger('click');
		}
	    },
	    complete: function() {
		$massupdateproductsProcess = false;
	    }
	});

    });
});