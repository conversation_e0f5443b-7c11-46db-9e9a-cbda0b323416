name: CICD

on:
  push:
    branches: [main, develop]

env:
  TOKEN: ${{ secrets.TOKEN_GH }}

jobs:
  # prod-deploy:
  #   env:
  #     JOB_BRANCH: main
  #   runs-on: ubuntu-latest
  #   if: ${{ github.ref_name == 'main' }}
  #   steps:
  #     - name: Pull changes
  #       uses: appleboy/ssh-action@master
  #       with:
  #         host: ${{ secrets.HOST_PROD_IP }}
  #         username: ${{ secrets.HOST_PROD_USERNAME }}
  #         password: ${{ secrets.HOST_PROD_PASSWORD }}
  #         port: ${{ secrets.HOST_PROD_PORT }}
  #         envs: TOKEN
  #         script: |
  #           cd ${{ secrets.HOST_PROD_PATH }}
  #           git checkout ${{ env.JOB_BRANCH }}
  #           git fetch https://token:${{ env.TOKEN }}@github.com/${{ github.repository_owner }}/${{ github.event.repository.name }}.git ${{ env.JO<PERSON>_<PERSON>ANCH }}
  #           git reset --hard FETCH_HEAD

  dev-deploy:
    env:
      JOB_BRANCH: develop
    runs-on: ubuntu-latest
    if: ${{ github.ref_name == 'develop' }}
    steps:
      - name: Pull changes
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_DEV_IP }}
          username: ${{ secrets.HOST_DEV_USERNAME }}
          password: ${{ secrets.HOST_DEV_PASSWORD }}
          port: ${{ secrets.HOST_DEV_PORT }}
          envs: TOKEN
          script: |
            cd ${{ secrets.HOST_DEV_PATH }}
            git checkout ${{ env.JOB_BRANCH }}
            git fetch https://token:${{ env.TOKEN }}@github.com/${{ github.repository_owner }}/${{ github.event.repository.name }}.git ${{ env.JOB_BRANCH }}
            git reset --hard FETCH_HEAD
