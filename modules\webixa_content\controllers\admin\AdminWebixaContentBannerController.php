<?php

class AdminWebixaContentBannerController extends ModuleAdminController
{
    public $bootstrap = true;
    protected $position_identifier;
    protected $block_identifier;
    /** @var WebixaContentBlock */
    protected $webixaContentBlock;

    public function __construct()
    {
        $this->bootstrap = true;
        parent::__construct();

        if (version_compare(_PS_VERSION_, '1.7', '>=')) {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
            $this->tabAccess['view'] = Module::getPermissionStatic($this->module->id, 'view');

            $configAccess = Module::getPermissionStatic($this->module->id, 'configure');
            $this->tabAccess['add'] = $configAccess;
            $this->tabAccess['edit'] = $configAccess;
            $this->tabAccess['delete'] = $configAccess;
        } else {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
        }
    }

    public function initProcess()
    {
        $this->initList();

        parent::initProcess();
    }

    public function initList()
    {
        $this->fieldImageSettings = [
            'name' => 'image',
            'dir' => '../modules/' . $this->module->name . '/' . WebixaContentBanner::_TYPE_IMG_DIR_,
        ];

        $this->table = WebixaContentBanner::$definition['table'];
        $this->className = 'WebixaContentBanner';
        $this->identifier = WebixaContentBanner::$definition['primary'];
        $this->block_identifier = WebixaContentBlock::$definition['primary'];
        $this->position_identifier = $this->identifier;
        $this->position_group_identifier = $this->block_identifier;
        $this->_defaultOrderBy = 'position';
        $this->_defaultOrderWay = 'ASC';

        $this->lang = true;
        if (Shop::isFeatureActive()) {
            Shop::addTableAssociation($this->table, ['type' => 'shop']);
        }
        $this->webixaContentBlock = new WebixaContentBlock((int)Tools::getValue($this->block_identifier));

        if (!Validate::isLoadedObject($this->webixaContentBlock)) {
            Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentBlock'));
        }

        self::$currentIndex .= '&' . $this->block_identifier . '=' . (int)$this->webixaContentBlock->id;

        $this->_select = 'a.' . $this->identifier . ' as image, GROUP_CONCAT(gl.name SEPARATOR ",\n") as `group_restrictions`';

        $this->_join = '
            LEFT JOIN `' . _DB_PREFIX_ . WebixaContentBanner::$definition['table'] . '_group_restriction` gr ON (a.' . WebixaContentBanner::$definition['primary'] . '=gr.' . WebixaContentBanner::$definition['primary'] . ')
            LEFT JOIN `' . _DB_PREFIX_ . Group::$definition['table'] . '_lang` gl ON (gr.' . Group::$definition['primary'] . '=gl.' . Group::$definition['primary'] . ' AND gl.id_lang=' . $this->context->language->id . ')
        ';

        $this->_where = 'AND ' . $this->block_identifier . '=' . (int)$this->webixaContentBlock->id;
        $this->_group = 'GROUP BY a.' . $this->identifier;

        $this->fields_list = [
            $this->identifier => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
            ],
            'image' => [
                'title' => $this->l('Image'),
                'orderby' => false,
                'search' => false,
                'align' => 'center',
                'callback' => 'displayImageDefaultOnList',
            ],
            'alt' => [
                'title' => $this->l('Alternate'),
                'filter_key' => 'b!alt',
                'maxlength' => 50,
            ],
            'link' => [
                'title' => $this->l('Link'),
                'filter_key' => 'b!link',
                'maxlength' => 50,
            ],
            'show_on_home' => [
                'title' => $this->l('Show on Home'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'ajax' => false,
                'type' => 'bool',
                'orderby' => false,
            ],
            'show_to_guest' => [
                'title' => $this->l('Show to Guest'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'ajax' => false,
                'type' => 'bool',
                'orderby' => false,
            ],
            'show_to_logged' => [
                'title' => $this->l('Show to Logged'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'ajax' => false,
                'type' => 'bool',
                'orderby' => false,
            ],
            'group_restrictions' => [
                'title' => $this->l('Restricted to groups'),
                'orderby' => false,
                'search' => false,
                'class' => 'fixed-width-sm',
                'callback_object' => 'Tools',
                'callback' => 'nl2br',
            ],
            'has_category_restrictions' => [
                'title' => $this->l('Restricted to Categories'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'ajax' => false,
                'type' => 'bool',
                'orderby' => false,
            ],
            'position' => [
                'title' => $this->l('Position'),
                'filter_key' => 'a!position',
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'position' => 'position',
            ],
            'active' => [
                'title' => $this->l('Displayed'),
                'align' => 'center',
                'active' => 'status',
                'class' => 'fixed-width-xs',
                'ajax' => true,
                'type' => 'bool',
                'orderby' => false,
            ],
        ];

        $this->addRowAction('edit');
        $this->addRowAction('delete');

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected banners?'),
            ],
        ];
    }

    public function renderForm()
    {
        /** @var WebixaContentBanner $obj */
        if (!($obj = $this->loadObject(true))) {
            return;
        }
        $image = WebixaContentBanner::_IMG_DIR_ . $obj->id . '.' . $this->imageType;

        $image_url = ImageManager::thumbnail(
            $image,
            $this->table . '_' . (int) $obj->id . '.' . $this->imageType,
            350,
            $this->imageType,
            true,
            true
        );
        $image_size = file_exists($image) ? filesize($image) / 1000 : false;
        $image_delete_url = file_exists($image) ? self::$currentIndex . '&' . $this->identifier . '=' . (int) $this->object->id . '&action=deleteImage&token=' . $this->token : false;

        $this->show_form_cancel_button = false;
        $this->fields_form = [
            'tinymce' => true,
            'legend' => [
                'title' => Validate::isLoadedObject($obj) ? $this->l('Update content Banner') : $this->l('Add content Banner'),
                'icon' => 'icon-cogs',
            ],
            'input' => [
                [
                    'type' => 'hidden',
                    'name' => 'id_webixa_content_block',
                    'value' => $this->webixaContentBlock->id,
                ],
                [
                    'type' => 'file',
                    'label' => $this->l('Image'),
                    'name' => 'image',
                    'image' => $image_url ? $image_url : false,
                    'size' => $image_size,
                    'delete_url' => $image_delete_url,
                    'display_image' => true,
                    'hint' => $this->l('Upload image from your computer.'),
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Alternate'),
                    'name' => 'alt',
                    'lang' => true,
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Link'),
                    'name' => 'link',
                    'lang' => true,
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show on Home'),
                    'name' => 'show_on_home',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'show_on_home_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'show_on_home_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show to Guest'),
                    'name' => 'show_to_guest',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'show_to_guest_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'show_to_guest_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show to Logged in Customers'),
                    'name' => 'show_to_logged',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'show_to_logged_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'show_to_logged_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
                [
                    'type' => 'checkbox',
                    'label' => $this->l('Show restricted to Groups'),
                    'name' => 'group_restrictions',
                    'values' => [
                        'query' => Group::getGroups($this->context->language->id),
                        'id' => Group::$definition['primary'],
                        'name' => 'name',
                    ],
                ],
                [
                    'type' => 'categories',
                    'label' => $this->l('Show restricted to Categories'),
                    'name' => 'category_restrictions',
                    'id' => 'category_restrictions',
                    'tree' => array(
                        'root_category' => (int)Configuration::get('PS_ROOT_CATEGORY'),
                        'id' => 'id_category',
                        'name' => 'name_category',
                        'selected_categories' => $this->object->category_restrictions,
                        'use_checkbox' => true,
                    ),
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Displayed'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
            ],
            'buttons' => [
                [
                    'title' => $this->l('Cancel'),
                    'id' => $this->table . '_form_cancel_btn',
                    'href' => self::$currentIndex . '&token=' . $this->token,
                    'icon' => 'process-icon-cancel',
                ],
            ],
        ];
        if (Shop::isFeatureActive()) {
            $this->fields_form['input'][] = [
                'type' => 'shop',
                'label' => $this->l('Shop association:'),
                'name' => 'checkBoxShopAsso',
            ];
        }

        $this->getFieldsValues($obj);

        return parent::renderForm();
    }


    public function getFieldsValues($obj)
    {
        $groups = Group::getGroups($this->context->language->id);
        $this->fields_value['group_restrictions'] = $obj->group_restrictions;
        foreach ($groups as $group) {
            $this->fields_value['group_restrictions_' . $group['id_group']] = Tools::getValue('group_restrictions_' . $group['id_group'], (in_array($group['id_group'], $obj->group_restrictions)));
        }
    }

    protected function copyFromPost(&$object, $table)
    {
        parent::copyFromPost($object, $table);
        $object->category_restrictions = Tools::getValue('category_restrictions', []);

        $object->group_restrictions = [];
        $groups = Group::getGroups($this->context->language->id);
        foreach ($groups as $group) {
            if (Tools::getValue('group_restrictions_' . $group['id_group'])) {
                $object->group_restrictions[] = (int)$group['id_group'];
            }
        }
    }

    public function renderList()
    {
        return $this->module->prepareAdminInfoBanner() . parent::renderList();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Banners in') . ' ' . $this->webixaContentBlock->title[$this->context->language->id];
    }

    public function initToolbar()
    {
        parent::initToolbar();

        if ('edit' == $this->display || 'add' == $this->display) {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => self::$currentIndex . '&token=' . $this->token,
                'desc' => $this->l('Back'),
                'icon' => 'process-icon-back',
            ];
        } else {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => $this->context->link->getAdminLink('AdminWebixaContentBlock'),
                'desc' => $this->l('Back to Blocks'),
                'icon' => 'process-icon-back',
            ];
        }
    }

    public function setMedia($isNewTheme = false)
    {
        if (!$this->module->active && version_compare(_PS_VERSION_, '8.0.2', '>=')) {
            $this->module->hookDisplayBackOfficeHeader([]);
        }
        parent::setMedia($isNewTheme);
        $this->addJqueryUI([
            'ui.core',
        ]);
        $this->addJqueryPlugin('tablednd');
        if ($isNewTheme) {
            $this->registerJavascript('dnd', _PS_JS_DIR_ . 'admin/dnd.js', ['position' => 'bottom', 'priority' => 150]);
        } else {
            $this->addJS(_PS_JS_DIR_ . 'admin/dnd.js');
        }
    }

    public function postProcess()
    {
        $ret = parent::postProcess();
        if (!$this->ajax) {
            if (!empty($this->action) && method_exists($this, 'process' . ucfirst(Tools::toCamelCase($this->action)))) {
                $this->module->clearModuleCache('*');
            }
        }

        return $ret;
    }

    protected function postImage($id)
    {
        parent::postImage($id);

        return !count($this->errors) ? true : false;
    }

    protected function uploadImage($id, $name, $dir, $ext = false, $width = null, $height = null)
    {
        if (isset($_FILES[$name]['error']) && !in_array($_FILES[$name]['error'], [UPLOAD_ERR_OK, UPLOAD_ERR_NO_FILE])) {
            $this->errors[] = $this->module->codeToMessage($_FILES[$name]['error']);
            return false;
        }

        if (isset($_FILES[$name]['tmp_name']) && !empty($_FILES[$name]['tmp_name'])) {
            // Delete old image
            if (Validate::isLoadedObject($object = $this->loadObject())) {
                $object->deleteImage();
            } else {
                return false;
            }

            // Check image validity
            $max_size = isset($this->max_image_size) ? $this->max_image_size : 0;
            if ($error = ImageManager::validateUpload($_FILES[$name], Tools::getMaxUploadSize($max_size))) {
                $this->errors[] = $error;
            }

            if (!move_uploaded_file($_FILES[$name]['tmp_name'], _PS_IMG_DIR_ . $dir . $id . '.' . $this->imageType)) {
                $this->errors[] = $this->trans('An error occurred while uploading the image.', [], 'Admin.Notifications.Error');
            }


            if (count($this->errors)) {
                return false;
            }

            return $this->afterImageUpload();
        }

        return true;
    }

    public function processDeleteImage()
    {
        $field = Tools::getValue('field');
        $object = $this->loadObject();
        if (!Validate::isLoadedObject($object) || !$object->deleteImage(true, $field)) {
            $this->errors[] = $this->l('Unable to delete image');
        } else {
            $this->confirmations[] = $this->l('Image successfully deleted');
        }

        $this->module->clearModuleCache('*');

        Tools::redirectAdmin(
            self::$currentIndex.'&'.$this->identifier.'='.$object->id.'&update'.$this->table.'&token='.$this->token
        );
    }

    public function ajaxProcessStatusWebixaContentBanner()
    {
        $id_object = (int) Tools::getValue($this->identifier);

        $sql = 'UPDATE ' . _DB_PREFIX_ . $this->table . ' SET `active`= NOT `active` WHERE ' . $this->identifier . '=' . $id_object;
        $result = Db::getInstance()->execute($sql);

        if ($result) {
            $this->module->clearModuleCache('*');
            $response = json_encode(['success' => 1, 'text' => $this->l('The status has been updated successfully.')]);
        } else {
            $response = json_encode(['success' => 0, 'text' => $this->l('An error occurred while updating the status.')]);
        }
        $this->ajaxDie($response);
    }

    public function ajaxProcessUpdatePositions()
    {
        if (
            !isset($this->position_identifier) ||
            !($positions = Tools::getValue($this->table, false)) ||
            !WebixaContentBanner::updatePositions($positions)
        ) {
            $this->ajaxDie(
                json_encode(
                    [
                        'hasError' => true,
                        'errors' => $this->l('Update position failed'),
                    ]
                )
            );
        }
        $this->module->clearModuleCache('*');
        $this->ajaxDie(
            json_encode(
                [
                    'success' => true,
                ]
            )
        );
    }

    protected function ajaxDie($value = null, $controller = null, $method = null)
    {
        if (ob_get_length() > 0) {
            ob_end_clean();
        }
        header('Content-Type: application/json');

        if (version_compare(_PS_VERSION_, '1.6.1', '>=')) {
            return parent::ajaxDie($value, $controller, $method);
        }

        header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        echo $value;
        exit;
    }

    public static function displayImageDefaultOnList($id, $row = null)
    {
        $img_path = WebixaContentBanner::_IMG_DIR_ . $id . '.jpg';

        $path_to_image = false;
        if (file_exists($img_path)) {
            $path_to_image = $img_path;
        }

        $context = Context::getContext();
        return ImageManager::thumbnail($path_to_image, WebixaContentBanner::$definition['table'] . '_mini_' . $id . '_' . $context->shop->id . '.jpg', 45, 'jpg');
    }
}
