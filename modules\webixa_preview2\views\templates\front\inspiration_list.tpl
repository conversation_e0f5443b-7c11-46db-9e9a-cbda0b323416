{extends file='page.tpl'}

{block name='page_title'}
  <h1 class="text-2xl font-semibold mb-4">{$page_title}</h1>
{/block}

{block name='page_content'}  
  <style>    
    .inspiration-hotspot {
      position: absolute;
      transform: translate(-50%, -50%);
      z-index: 100;
      cursor: pointer;
      width: 30px;
      height: 30px;
      pointer-events: auto;
    }
    .hotspot-cross {
      position: absolute;
      top: 0;
      left: 0;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.9);
      border: 3px solid #2fb5d2;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 18px;
      color: #2fb5d2;
      transition: all 0.3s ease;
      z-index: 500;
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(47, 181, 210, 0.7);
      }
      70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(47, 181, 210, 0);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(47, 181, 210, 0);
      }
    }
    .inspiration-hotspot:hover .hotspot-cross {
      transform: scale(1.2) !important;
      background-color: #2fb5d2 !important;
      color: white !important;
      animation: none; /* Stop pulsing on hover */
    }

	

    
    .inspiration-item {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    .inspiration-item:hover {
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .inspiration-item img {
      transition: transform 0.5s ease;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .inspiration-item:hover img {
      transform: scale(1.05);
    }
    .inspiration-title {
      color: #fff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      text-align: center;
      padding: 0 15px;
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 15px;
      transform: translateY(20px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    .inspiration-overlay {
      position: absolute;
      inset: 0;
      background-color: rgba(47, 181, 210, 0);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      opacity: 0;
      visibility: hidden;
    }
    .inspiration-item:hover .inspiration-overlay {
      background-color: rgba(47, 181, 210, 0.8);
      opacity: 1;
      visibility: visible;
    }
    .circular-arrow {
      width: 60px;
      height: 60px;
      background-color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: scale(0.5);
      opacity: 0;
      transition: all 0.3s ease 0.1s;
    }
    .circular-arrow svg {
      width: 30px;
      height: 30px;
      fill: #2fb5d2;
    }
    .inspiration-item:hover .circular-arrow {
      transform: scale(1);
      opacity: 1;
    }
    .inspiration-item:hover .inspiration-title {
      transform: translateY(0);
      opacity: 1;
    }

    .inspiration-title-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 10px 15px;
      font-weight: 600;
      transform: translateY(100%);
      transition: transform 0.3s ease;
    }
    .inspiration-item:hover .inspiration-title-overlay {
      transform: translateY(0);
    }

    .inspiration-hotspot {
      position: absolute !important;
      transform: translate(-50%, -50%) !important;
      z-index: 100 !important;
      cursor: pointer !important;
      width: 30px !important;
      height: 30px !important;
    }

    .hotspot-cross {
      width: 30px !important;
      height: 30px !important;
      border-radius: 50% !important;
      background-color: rgba(255, 255, 255, 0.9) !important;
      border: 3px solid #2fb5d2 !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-weight: bold !important;
      font-size: 18px !important;
      color: #2fb5d2 !important;
      transition: all 0.3s ease !important;
      z-index: 100 !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
    }

    .inspiration-hotspot:hover .hotspot-cross {
      transform: scale(1.2) !important;
      background-color: #2fb5d2 !important;
      color: white !important;
    }




	
	.flex-col {
		flex-direction: row-reverse !important;
	}

    /* Custom tooltip styles */
    .custom-tooltip {
        background: white !important;
        color: #333 !important;
        border: 1px solid #ddd !important;
        border-radius: 8px !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        padding: 0 !important;
    }

    .custom-tooltip .tooltip-product-content {
        padding: 15px;
    }

    .custom-tooltip .d-flex {
        display: flex !important;
    }

    .custom-tooltip .align-items-start {
        align-items: flex-start !important;
    }

    .tooltip-product-image {
        width: 64px;
        height: 64px;
        object-fit: cover;
        border-radius: 4px;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .tooltip-product-info {
        flex: 1;
    }

    .tooltip-product-name {
        font-size: 14px;
        font-weight: 600;
        color: #232323;
        line-height: 1.2;
        margin-bottom: 8px;
    }

    .tooltip-product-price {
        color: #2fb5d2;
        font-weight: 700;
        font-size: 12px;
        margin-bottom: 12px;
    }

    .tooltip-product-actions {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .tooltip-btn {
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        transition: all 0.2s;
        display: block;
        border: none;
        cursor: pointer;
    }

    .tooltip-btn-primary {
        background-color: #2fb5d2;
        color: white;
    }

    .tooltip-btn-primary:hover {
        background-color: #2592a9;
        text-decoration: none;
        color: white;
    }
  </style>

  <script>
  document.addEventListener('DOMContentLoaded', function() {
      function positionPopup(hotspot, popup) {
          const hotspotRect = hotspot.getBoundingClientRect();
          const popupWidth = 280;
          const popupHeight = popup.offsetHeight || 200;
          const margin = 15;
          const viewportWidth = window.innerWidth;
          const viewportHeight = window.innerHeight;

          popup.className = popup.className.replace(/popup-(top|bottom|left|right)/g, '');

          const hotspotCenterX = hotspotRect.left + hotspotRect.width / 2;
          const hotspotCenterY = hotspotRect.top + hotspotRect.height / 2;

          const spaceTop = hotspotRect.top;
          const spaceBottom = viewportHeight - hotspotRect.bottom;
          const spaceLeft = hotspotRect.left;
          const spaceRight = viewportWidth - hotspotRect.right;

          let position = 'popup-bottom';
          let left = hotspotCenterX - popupWidth / 2;
          let top = hotspotRect.bottom + margin;

          // Check if popup fits below hotspot
          if (spaceBottom >= popupHeight + margin) {
              position = 'popup-bottom';
              top = hotspotRect.bottom + margin;

              // Adjust horizontal position if popup would go off screen
              if (left < margin) {
                  left = margin;
              } else if (left + popupWidth > viewportWidth - margin) {
                  left = viewportWidth - popupWidth - margin;
              }
          }
          // Check if popup fits above hotspot
          else if (spaceTop >= popupHeight + margin) {
              position = 'popup-top';
              top = hotspotRect.top - popupHeight - margin;

              // Adjust horizontal position if popup would go off screen
              if (left < margin) {
                  left = margin;
              } else if (left + popupWidth > viewportWidth - margin) {
                  left = viewportWidth - popupWidth - margin;
              }
          }
          // Check if popup fits to the right
          else if (spaceRight >= popupWidth + margin) {
              position = 'popup-right';
              left = hotspotRect.right + margin;
              top = hotspotCenterY - popupHeight / 2;

              // Adjust vertical position if popup would go off screen
              if (top < margin) {
                  top = margin;
              } else if (top + popupHeight > viewportHeight - margin) {
                  top = viewportHeight - popupHeight - margin;
              }
          }
          // Check if popup fits to the left
          else if (spaceLeft >= popupWidth + margin) {
              position = 'popup-left';
              left = hotspotRect.left - popupWidth - margin;
              top = hotspotCenterY - popupHeight / 2;

              // Adjust vertical position if popup would go off screen
              if (top < margin) {
                  top = margin;
              } else if (top + popupHeight > viewportHeight - margin) {
                  top = viewportHeight - popupHeight - margin;
              }
          }
          // Fallback: position below with adjustments
          else {
              position = 'popup-bottom';
              top = hotspotRect.bottom + margin;

              if (top + popupHeight > viewportHeight - margin) {
                  top = hotspotRect.top - popupHeight - margin;
                  position = 'popup-top';
              }

              if (left < margin) {
                  left = margin;
              } else if (left + popupWidth > viewportWidth - margin) {
                  left = viewportWidth - popupWidth - margin;
              }
          }

          popup.classList.add(position);
          popup.style.left = left + 'px';
          popup.style.top = top + 'px';
      }


  });

  // Custom tooltip implementation
  function initCustomTooltips() {
      const hotspots = document.querySelectorAll('[data-toggle="tooltip"]');

      hotspots.forEach(function(hotspot) {
          let tooltip = null;
          let hideTimeout = null;

          function showTooltip() {
              if (tooltip) return; // Already showing

              // Clear any pending hide
              if (hideTimeout) {
                  clearTimeout(hideTimeout);
                  hideTimeout = null;
              }

              // Create tooltip
              tooltip = document.createElement('div');
              tooltip.className = 'custom-tooltip fade show';

              // Get and parse the HTML content
              const htmlContent = hotspot.getAttribute('data-content') || hotspot.getAttribute('title');
              tooltip.innerHTML = htmlContent;
              tooltip.style.cssText = `
                  position: fixed;
                  z-index: 99999;
                  background: white;
                  color: #333;
                  border: 1px solid #ddd;
                  border-radius: 8px;
                  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                  font-size: 12px;
                  max-width: 300px;
                  pointer-events: auto;
                  opacity: 0;
                  transition: opacity 0.3s ease;
                  padding: 0;
              `;

              document.body.appendChild(tooltip);

              // Position tooltip intelligently
              const rect = hotspot.getBoundingClientRect();
              const tooltipRect = tooltip.getBoundingClientRect();
              const margin = 15;

              let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
              let top = rect.top - tooltipRect.height - margin;

              // Adjust if tooltip goes off screen
              if (left < margin) left = margin;
              if (left + tooltipRect.width > window.innerWidth - margin) {
                  left = window.innerWidth - tooltipRect.width - margin;
              }
              if (top < margin) {
                  top = rect.bottom + margin;
              }

              tooltip.style.left = left + 'px';
              tooltip.style.top = top + 'px';

              // Show tooltip
              setTimeout(() => {
                  if (tooltip) tooltip.style.opacity = '1';
              }, 10);

              // Add hover listeners to tooltip
              tooltip.addEventListener('mouseenter', function() {
                  if (hideTimeout) {
                      clearTimeout(hideTimeout);
                      hideTimeout = null;
                  }
              });

              tooltip.addEventListener('mouseleave', function() {
                  hideTooltip();
              });
          }

          function hideTooltip() {
              if (hideTimeout) return; // Already hiding

              hideTimeout = setTimeout(() => {
                  if (tooltip) {
                      tooltip.style.opacity = '0';
                      setTimeout(() => {
                          if (tooltip && tooltip.parentNode) {
                              tooltip.parentNode.removeChild(tooltip);
                              tooltip = null;
                          }
                      }, 300);
                  }
                  hideTimeout = null;
              }, 100);
          }

          hotspot.addEventListener('mouseenter', showTooltip);
          hotspot.addEventListener('mouseleave', hideTooltip);
      });
  }

  // Function to add product to cart from tooltip
  function addToCartFromTooltip(productId) {
      // Create a hidden form with the working button structure
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = prestashop.urls.pages.cart || (prestashop.urls.base_url + 'cart');
      form.style.display = 'none';

      // Add hidden inputs
      const addInput = document.createElement('input');
      addInput.type = 'hidden';
      addInput.name = 'add';
      addInput.value = '1';
      form.appendChild(addInput);

      const productInput = document.createElement('input');
      productInput.type = 'hidden';
      productInput.name = 'id_product';
      productInput.value = productId;
      form.appendChild(productInput);

      const qtyInput = document.createElement('input');
      qtyInput.type = 'hidden';
      qtyInput.name = 'qty';
      qtyInput.value = '1';
      form.appendChild(qtyInput);

      const tokenInput = document.createElement('input');
      tokenInput.type = 'hidden';
      tokenInput.name = 'token';
      tokenInput.value = prestashop.static_token;
      form.appendChild(tokenInput);

      // Create the button with the exact same attributes as the working one
      const button = document.createElement('button');
      button.className = 'w-full text-xs px-2 py-1 rounded bg-primary text-white hover:bg-primary-dark transition duration-200';
      button.setAttribute('data-button-action', 'add-to-cart');
      button.type = 'submit';
      button.textContent = 'Add to cart';
      form.appendChild(button);

      document.body.appendChild(form);

      // Submit the form
      form.submit();
  }

  // Initialize custom tooltips when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
      initCustomTooltips();
  });
  </script>

  <section id="inspirations-list" class="container mx-auto px-4">
    <div class="flex flex-col md:flex-row gap-8">

      {if isset($categories) && $categories}
        <aside class="w-full md:w-1/4 lg:w-1/5">
          <nav class="inspirations-categories bg-gray-100 p-4 rounded shadow">
            <h3 class="text-lg font-semibold mb-3 border-b pb-2">{l s='Categories' d='Modules.Webixapreview.Shop'}</h3>
            <ul class="space-y-1">
              <li>
                <a href="{url entity='module' name='webixa_preview' controller='inspiration'}"
                   class="block px-2 py-1 rounded hover:bg-gray-200 {if !$currentCategory}font-bold text-blue-700{/if}">
                  {l s='All Inspirations' d='Modules.Webixapreview.Shop'}
                </a>
              </li>

              {foreach from=$categories item=category}
                {if $category.id_parent == 0}
                  <li class="category-item">
                    <a href="{url entity='module' name='webixa_preview' controller='inspiration' params=['category_rewrite' => $category.link_rewrite]}"
                       class="block px-2 py-1 rounded hover:bg-gray-200 {if isset($currentCategory) && $currentCategory->id == $category.id_inspiration_category}font-bold text-blue-700{/if}">
                      {$category.name}
                    </a>

                    {assign var="hasSubcategories" value=false}
                    {foreach from=$categories item=subcategory}
                      {if $subcategory.id_parent == $category.id_inspiration_category}
                        {assign var="hasSubcategories" value=true}
                        {break}
                      {/if}
                    {/foreach}

                    {if $hasSubcategories}
                      <ul class="pl-4 mt-1 space-y-1 border-l border-gray-300">
                        {foreach from=$categories item=subcategory}
                          {if $subcategory.id_parent == $category.id_inspiration_category}
                            <li>
                              <a href="{url entity='module' name='webixa_preview' controller='inspiration' params=['category_rewrite' => $subcategory.link_rewrite]}"
                                 class="block px-2 py-1 text-sm rounded hover:bg-gray-200 {if isset($currentCategory) && $currentCategory->id == $subcategory.id_inspiration_category}font-bold text-blue-700{/if}">
                                {$subcategory.name}
                              </a>
                            </li>
                          {/if}
                        {/foreach}
                      </ul>
                    {/if}
                  </li>
                {/if}
              {/foreach}
            </ul>
          </nav>
        </aside>
      {/if}
      <div class="flex-1">
        {if isset($inspirations) && $inspirations}
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {foreach from=$inspirations item=inspiration}
              <article class="inspiration-item border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-lg" data-id-inspiration="{$inspiration.id_inspiration}">
                <a href="{$inspiration.url}" class="block relative">
                  {if $inspiration.image_url}
                    <div class="relative h-64">
                      <img src="{$inspiration.image_url}" alt="{$inspiration.title|escape:'htmlall':'UTF-8'}" loading="lazy" class="w-full h-full object-cover">
                    </div>
                  {else}
                    <div class="w-full h-64 bg-gray-200 flex items-center justify-center text-gray-500 italic">
                        <span>{$inspiration.title|escape:'htmlall':'UTF-8'}</span>
                    </div>
                  {/if}

                  {* Title overlay at bottom of image *}
                  <div class="inspiration-title-overlay">
                    <h4 class="text-white font-semibold text-base">
                      {$inspiration.title|escape:'htmlall':'UTF-8'}
                    </h4>
                  </div>

                  {* Hover overlay with circular arrow *}
                  <div class="inspiration-overlay">
                    <h4 class="inspiration-title">
                      {$inspiration.title|escape:'htmlall':'UTF-8'}
                    </h4>
                    <div class="circular-arrow">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                      </svg>
                    </div>
                  </div>
                </a>
              </article>
            {/foreach}
          </div>

          {* Pagination *}
          <div class="mt-8">
            {include file='_partials/pagination.tpl' pagination=$pagination}
          </div>

        {else}
          <div class="bg-yellow-100 border border-yellow-300 text-yellow-800 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{l s='No inspirations found in this category.' d='Modules.Webixapreview.Shop'}</span>
          </div>
        {/if}
      </div>

    </div>
  </section>
{/block}
