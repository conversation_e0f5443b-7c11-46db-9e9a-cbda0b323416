<div style="display: block;position: relative;overflow: hidden;text-align: left;">
    <span class="buttons-ph-panel filter_on accessories_filter" data-page="{if isset($page)}{$page|intval}{else}1{/if}">
        <i class="fa fa-floppy-o"></i>&nbsp;{l s='Filter' mod='massupdateproducts'}
    </span>
    <p>
        <input type="checkbox" {if (int)Tools::getValue('sort_name')} checked="checked" {/if} class="sort-name accessory_on" />&nbsp;{l s='sorted by name' mod='massupdateproducts'}
    </p>
    {if $select}
        <p>
            <input type="checkbox" class="select_all_accessory_on" />&nbsp;{l s='select all' mod='massupdateproducts'}
        </p>
        <p>
            <input type="checkbox" class="select_all_accessory_mirror_on" />&nbsp;{l s='select all mirror' mod='massupdateproducts'}
        </p>
        <div class="mass-multi-select" style="height: auto;overflow-x: scroll;overflow-y: hidden;">
            {foreach $select as $product}
                <div class="mass-multi-select-el">
                    <span>
                        <input title="{l s='accessory' mod='massupdateproducts'}" class="to-send accessories_on val-element" send-name="accessories_on" type="checkbox" {if $product->is_accessories} checked="checked" {/if} id-e="{$product->id|intval}" />
                    </span>
                    <span>
                        <input title="{l s='accessory mirror' mod='massupdateproducts'}" class="to-send accessories_mirror_on val-element" send-name="accessories_mirror_on" type="checkbox" {if $product->is_mirror} checked="checked" {/if} id-e="{$product->id|intval}" />
                    </span>
                    <span>
                        {$product->name|strval}&nbsp;({$product->id|strval})
                    </span>
                </div>
            {/foreach}
        </div>
        <div>
            {if $page > 1}
                <span class="accessories_back" style="cursor: pointer;color: #00aff0;">
                    <i class="fa fa-arrow-left"></i>
                </span>
            {/if}
            {if $count} 
                {for $var=1 to ceil($count/20)}
                    <span data-page="{$var|intval}" class="accessories_number" style="cursor: pointer;color: #00aff0">
                        &nbsp;{$var|intval}
                    </span>
                {/for}
            {/if}
            &nbsp;
            {if $page*20 < $count}
                <span class="accessories_next" style="cursor: pointer;color: #00aff0;">
                    <i class="fa fa-arrow-right"></i>
                </span>
            {/if}
        </div>
    {/if}
</div>