{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

{assign var='temp' value='combination'}
<div class="combination-row element-row" p-id="{$product->id|intval}" c-id="{$combination->id|intval}" c-ids="{$combination->ids_attribute|intval}">
    <div class="td-element first-td-element" style="width: 25px;">
	<div class="td-element-inside" style="width: 25px; text-align: center;">
	    <i style="cursor: pointer;" class="fa fa-refresh ir-com"></i>
	</div>
    </div>
    <div class="td-element" style="width: 27px;">
	<div class="td-element-inside" style="width: 27px; text-align: center;">
	    <input checked type="checkbox" class="check_single check_combination" />
	</div>
    </div>
    <div  class="td-element" style="width: 27px;">
	<div class="td-element-inside" style="width: 27px; text-align: center;">
	    {$combination->id|intval}
	</div>
    </div>
    <div class="td-element" style="width: 114px;">
	<div class="td-element-inside" style="width: 114px;">
	    <img src="{$combination->image_link|strval}" style="width: 98px;height: 98px;border: 1px solid #000;" />
	</div>
    </div>
    <div class="td-element" style="width: 208px;">
	<div class="td-element-inside" style="width: 208px;">
	    {$combination->full_name|strval} <span title="{l s='Copy to twins all elements' mod='massupdateproducts'}"><i style="cursor: pointer;" class="fa fa-files-o ic-copy"></i></span>{if $combination->reference}&nbsp;[{$combination->reference|strval}]{/if}
	</div>
    </div>
    {assign var='counter' value='1'}
    {if $fields}
        {foreach $fields as $field}
            {if $field['active']}
                {assign var='counter' value=$counter + 1}
                <div  class="td-element {if $field['none-copy']}none-copy{/if}" style="width: {if $field['type'] eq 7}200{else}{$widths[$field['type']]}{/if}px;">
		    <div class="td-element-inside" style="{if $field['type'] neq 0}text-align: center;{/if}width: {if $field['type'] eq 7}200{else}{$widths[$field['type']]}{/if}px;">
			{if $field['combination']}
			    {assign var=file value=$field['type']}
			    {if $field['display']}
				{if $field['lang']}
				    {foreach $languages as $language}
					<span style="position: relative;display: block;height: 23px;">
					    {include file="./fields/field_$file.tpl" select=$field['select'] name=$field['name'] value=$field['value'][$language['id_lang']] lang=$language['iso_code'] extra=$field['extra'] class_mass=$field['class'] validate=$field['validate'] attr=$field['attr']}
					    <span style="position: absolute;top: 4px;left: 3px;z-index: 201;">
						<img title="{$language['name']|strval}" style="width: 16px; height: 10px;" src="{$img_lang_src|strval}{$language['id_lang']|intval}.jpg" />
					    </span>
					</span>
				    {/foreach}
				{else}
				    {include file="./fields/field_$file.tpl" select=$field['select'] class_mass=$field['class'] name=$field['name'] value=$field['value'] extra=$field['extra'] validate=$field['validate'] attr=$field['attr']}
				    {if !$field['none-copy']}
					<span title="{l s='Copy to twins selected element' mod='massupdateproducts'}"><i style="cursor: pointer;" class="fa fa-files-o ic-copy-single"></i></span>
					{/if}
				    {/if}
				{/if}
			    {/if}
		    </div>
		</div>
            {/if}
        {/foreach}
    {/if}
    <div class="td-element" style="width: 25px;"></div>
</div>
<div class="combination-row-raport" style="line-height: 104px;" p-id="{$product->id|intval}" c-id="{$combination->id|intval}">
    <div class="td-element" style="color: #FFF !important;font-weight: bold;"></div>
</div>