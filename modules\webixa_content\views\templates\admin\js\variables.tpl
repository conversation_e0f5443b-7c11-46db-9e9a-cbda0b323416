<script>
  {foreach from=$webixa_js_variables item=value key=k}
    {if !empty($k) && is_string($k)}
      {if is_bool($value)}
        var {$k} = {$value|var_export:true};
      {elseif is_int($value)}
        var {$k} = {$value|intval};
      {elseif is_float($value)}
        var {$k} = {$value|floatval|replace:',':'.'};
      {elseif is_string($value)}
        var {$k} = '{$value|strval}';
      {elseif is_array($value) || is_object($value)}
        var {$k} = {$value|json_encode};
      {elseif is_null($value)}
        var {$k} = null;
      {else}
        var {$k} = '{$value|@addcslashes:'\''}';
      {/if}
    {/if}
  {/foreach}
</script>