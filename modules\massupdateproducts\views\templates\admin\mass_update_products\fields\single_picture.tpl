{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<div class="image-show-content-block" pic-id="{$id_image|strval}">
    <div class="image-show-content-tools">
        {if $extra}
            <span class="cover-{if $image['cover']}active{else}inactive{/if}" title="{l s='Cover' mod='massupdateproducts'}">
                <i class="fa fa-eye"></i>
            </span>
        {/if}
        <span class="picture_belong rem-active" title="{l s='Remove' mod='massupdateproducts'}">
            <i class="fa fa-times"></i>
            <input type="hidden" send-name="picture_belong" class="to-send" value="{$id_image|strval}" />
        </span>
    </div>
    <div class="image-show-content-picture">
        <img src="{$image['link']|strval}" style="width: 125px;height: 125px;border: 1px solid #000;" />
        {if $extra}
            <div class="image-show-content-picture-legend">
                {foreach $languages as $language}
                    <span style="position: relative;display: block;height: 23px;">
                        {assign var="name_legend" value="`$name`_`$id_image`"}
                        {include file="./field_0.tpl" select=$select name=$name_legend value=$image['legend'][$language['id_lang']] lang=$language['iso_code'] extra=$extra class_mass=$class_mass validate=$validate attr=$attr}
                        <span style="position: absolute;top: 4px;left: 3px;z-index: 201;">
                            <img title="{$language['name']|strval}" style="width: 16px; height: 10px;" src="{$img_lang_src|strval}{$language['id_lang']|intval}.jpg" />
                        </span>
                    </span>
                {/foreach}
            </div>
        {/if}
    </div>
</div>