{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<ul class="pagination pull-right">
    <li class="pagination-link-view  {if !$larrows}disabled{/if}">
	<span class="pagination-link" data-page="1">
	    <i class="fa fa-angle-double-left"></i>
	</span>
    </li>
    <li class="pagination-link-view  {if !$larrows}disabled{/if}">
	<span class="pagination-link" data-page="{($current - 1)|intval}">
	    <i class="fa fa-angle-left"></i>
	</span>
    </li>
    {if $lmore}
	<li class="pagination-link-view disabled">
	    <span>…</span>
	</li>
    {/if}
    {foreach $pages as $page}
	<li class="pagination-link-view  {if $page eq $current}active{/if}">
	    <span class="pagination-link {if $page eq $current}current{/if}" data-page="{$page|strval}">{$page|strval}</span>
	</li>
    {/foreach}
    {if $rmore}
	<li class="pagination-link-view disabled">
	    <span>…</span>
	</li>
    {/if}
    <li class="pagination-link-view  {if !$rarrows}disabled{/if}">
	<span class="pagination-link" data-page="{if !$rarrows}{$max|intval}{else}{($current + 1)|intval}{/if}">
	    <i class="fa fa-angle-right"></i>
	</span>
    </li>
    <li class="pagination-link-view {if !$rarrows}disabled{/if}">
	<span class="pagination-link" data-page="{$max|intval}">
	    <i class="fa fa-angle-double-right"></i>
	</span>
    </li>
</ul>