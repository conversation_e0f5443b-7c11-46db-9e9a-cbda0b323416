<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

namespace Webixa\Content\Module;

use Configuration;
use Context;
use Language;

class WebixaConfig
{
    const PREFIX = 'WB_C_';
    //const DISPLAY_HOME = self::PREFIX . 'DISPLAY_HOME';

    const BLOCK_TYPE_LINKS = 'links';
    const BLOCK_TYPE_SLIDES = 'slides';
    const BLOCK_TYPE_ITEMS = 'items';
    const BLOCK_TYPE_CATEGORY_PRODUCTS = 'category_products';
    const BLOCK_TYPE_BANNERS = 'banners';
    const BLOCK_TYPE_HTML = 'html';

    const TEMPLATE_SIMPLE = 'simple';
    const TEMPLATE_HOME_SLIDE = 'home_slide';
    const TEMPLATE_CATEGORY_SLIDE = 'category_slide';
    const TEMPLATE_DROPDOWN_LINKS = 'dropdown_links';
    const TEMPLATE_FOOTER_MENU = 'footer_menu';
    const TEMPLATE_EXPLORE_PRODUCTS = 'explore_products';

    const TEMPLATE_CATEGORY_BANNER = 'category_banner';
    const TEMPLATE_TRUST_US = 'trust_us';
    const TEMPLATE_TOP_RATED = 'top_rated';
    const TEMPLATE_CATEGORY_PRODUCTS_HOME = 'category_products_home';
    const TEMPLATE_PAYMENTS_FOOTER = 'payments_footer';

    const TEMPLATE_SLIDER_FOOTER = 'slider_footer';
    const TEMPLATE_ADDITIONAL_FOOTER = 'additional_footer';
    const TEMPLATE_TRUSTPILOT = 'trustpilot';
    const TEMPLATE_PRODUCT_STATE='product_state';
    const TEMPLATE_PRODUCT_TABS='product_tabs';

    const AVAILABLE_TYPES = [
        self::BLOCK_TYPE_LINKS,
        self::BLOCK_TYPE_SLIDES,
        self::BLOCK_TYPE_ITEMS,
        self::BLOCK_TYPE_CATEGORY_PRODUCTS,
        self::BLOCK_TYPE_BANNERS,
        self::BLOCK_TYPE_HTML,
    ];

    const AVAILABLE_LINKS_TEMPLATES = [
        self::TEMPLATE_SIMPLE => [
            'id' => self::TEMPLATE_SIMPLE,
            'name' => 'Simple',
            'path' => 'links/simple.tpl',
        ],
        self::TEMPLATE_EXPLORE_PRODUCTS => [
            'id' => self::TEMPLATE_EXPLORE_PRODUCTS,
            'name' => 'Explore products',
            'path' => 'links/explore_products.tpl',
        ],
        self::TEMPLATE_DROPDOWN_LINKS => [
            'id' => self::TEMPLATE_DROPDOWN_LINKS,
            'name' => 'Dropdown links',
            'path' => 'links/dropdown_links.tpl',
        ],
        self::TEMPLATE_FOOTER_MENU => [
            'id' => self::TEMPLATE_FOOTER_MENU,
            'name' => 'Footer links',
            'path' => 'links/footer_menu.tpl',
        ],
        self::TEMPLATE_CATEGORY_SLIDE => [
            'id' => self::TEMPLATE_CATEGORY_SLIDE,
            'name' => 'Category Slider',
            'path' => 'links/category_slide.tpl',
        ],
        self::TEMPLATE_CATEGORY_BANNER => [
            'id' => self::TEMPLATE_CATEGORY_BANNER,
            'name' => 'Category Banner',
            'path' => 'links/category_banner.tpl',
        ],
    ];

    const AVAILABLE_SLIDES_TEMPLATES = [
        self::TEMPLATE_SIMPLE => [
            'id' => self::TEMPLATE_SIMPLE,
            'name' => 'Simple',
            'path' => 'slides/simple.tpl',
        ],
        self::TEMPLATE_HOME_SLIDE => [
            'id' => self::TEMPLATE_HOME_SLIDE,
            'name' => 'Home slider',
            'path' => 'slides/home-slider.tpl',
        ],
        self::TEMPLATE_PAYMENTS_FOOTER => [
            'id' => self::TEMPLATE_PAYMENTS_FOOTER,
            'name' => 'Payment footer',
            'path' => 'slides/payments_footer.tpl',
        ],
        self::TEMPLATE_SLIDER_FOOTER => [
            'id' => self::TEMPLATE_SLIDER_FOOTER,
            'name' => 'Footer Slider',
            'path' => 'slides/slider_footer.tpl',
        ],
        self::TEMPLATE_ADDITIONAL_FOOTER => [
            'id' => self::TEMPLATE_ADDITIONAL_FOOTER,
            'name' => 'Footer Additional',
            'path' => 'slides/additional_footer.tpl',
        ],
    ];

    const AVAILABLE_ITEMS_TEMPLATES = [
        self::TEMPLATE_SIMPLE => [
            'id' => self::TEMPLATE_SIMPLE,
            'name' => 'Simple',
            'path' => 'items/simple.tpl',
        ],
        self::TEMPLATE_TRUST_US => [
            'id' => self::TEMPLATE_TRUST_US,
            'name' => 'Choose Luxury Love',
            'path' => 'items/trust_us.tpl',
        ],
        self::TEMPLATE_TRUSTPILOT => [
            'id' => self::TEMPLATE_TRUSTPILOT,
            'name' => 'Trustpilot',
            'path' => 'items/trustpilot.tpl',
        ],
        self::TEMPLATE_PRODUCT_STATE => [
            'id' => self::TEMPLATE_PRODUCT_STATE,
            'name' => 'Product state modal',
            'path' => 'items/product_state.tpl',
        ],
        self::TEMPLATE_PRODUCT_TABS => [
            'id' => self::TEMPLATE_PRODUCT_TABS,
            'name' => 'Product tabs',
            'path' => 'items/product_tabs.tpl',
        ],
    ];

    const AVAILABLE_CATEGORY_PRODUCTS_TEMPLATES = [
        self::TEMPLATE_SIMPLE => [
            'id' => self::TEMPLATE_SIMPLE,
            'name' => 'Simple',
            'path' => 'category_products/simple.tpl',
        ],
        self::TEMPLATE_TOP_RATED => [
            'id' => self::TEMPLATE_TOP_RATED,
            'name' => 'Top Rated',
            'path' => 'category_products/top_rated.tpl',
        ],
        self::TEMPLATE_CATEGORY_PRODUCTS_HOME => [
            'id' => self::TEMPLATE_CATEGORY_PRODUCTS_HOME,
            'name' => 'Category products home',
            'path' => 'category_products/category_products-home.tpl',
        ],
    ];

    const AVAILABLE_BANNERS_TEMPLATES = [
        self::TEMPLATE_SIMPLE => [
            'id' => self::TEMPLATE_SIMPLE,
            'name' => 'Simple',
            'path' => 'banners/simple.tpl',
        ],
    ];

    const AVAILABLE_HTML_TEMPLATES = [
        self::TEMPLATE_SIMPLE => [
            'id' => self::TEMPLATE_SIMPLE,
            'name' => 'Simple',
            'path' => 'html/simple.tpl',
        ],
    ];

    const AVAILABLE_CONFIGURATION_KEYS = [];

    const DEFAULT_CONFIGURATION_VALUES = [];

    const DEFAULT_CONFIGURATION_LANG_VALUES = [];

    /** @var Context */
    private $context;

    public function __construct(Context $context)
    {
        $this->context = $context;
    }

    public function get($configKey, $idLang = null)
    {
        if ($idLang) {
            return Configuration::get($configKey, $idLang);
        }

        return Configuration::get($configKey);
    }

    private function set($configKey, $configValue)
    {
        return Configuration::updateValue($configKey, $configValue, true);
    }

    private function delete($configKey)
    {
        return Configuration::deleteByName($configKey);
    }

    public function install()
    {
        $return = true;
        foreach (self::DEFAULT_CONFIGURATION_VALUES as $configKey => $configValue) {
            $return &= $this->set($configKey, $configValue);
        }

        foreach (self::DEFAULT_CONFIGURATION_LANG_VALUES as $configKey => $configValue) {
            $configLangValue = [];
            foreach (Language::getLanguages(false) as $lang) {
                $configLangValue[$lang['id_lang']] = isset($configValue[$lang['iso_code']]) ? $configValue[$lang['iso_code']] : $configValue['en'];
            }
            $return &= $this->set($configKey, $configLangValue);
        }
        return $return;
    }

    public function uninstall()
    {
        $return = true;
        foreach (self::AVAILABLE_CONFIGURATION_KEYS as $configKey) {
            $return &= $this->delete($configKey);
        }
        return $return;
    }
}
