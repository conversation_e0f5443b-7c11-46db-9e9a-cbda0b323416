<?php
$sql = array();

// --- Inspiration Tables ---
$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration` (
    `id_inspiration` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `active` tinyint(1) UNSIGNED NOT NULL DEFAULT 1,
    `position` int(10) UNSIGNED NOT NULL DEFAULT 0,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    `short_description` TEXT NULL,
    `display_on_homepage` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
    `rgb_color` VARCHAR(7) NULL DEFAULT NULL, -- Added RGB color field
    `id_shop` int(10) UNSIGNED NOT NULL DEFAULT 1,
    PRIMARY KEY (`id_inspiration`),
    KEY `idx_active_position` (`active`, `position`), -- Index for frontend listing
    KEY `id_shop` (`id_shop`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_image` (
    `id_inspiration_image` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `id_inspiration` int(11) UNSIGNED NOT NULL,
    `image` varchar(255) NOT NULL,
    `position` int(10) UNSIGNED NOT NULL DEFAULT 0,
    `main` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
    PRIMARY KEY (`id_inspiration_image`),
    KEY `id_inspiration` (`id_inspiration`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_lang` (
    `id_inspiration` int(11) UNSIGNED NOT NULL,
    `id_lang` int(11) UNSIGNED NOT NULL,
    `title` varchar(255) NOT NULL,
    `link_rewrite` varchar(255) NOT NULL,
    `description` text,
    `short_description` text,
    PRIMARY KEY (`id_inspiration`, `id_lang`),
    KEY `idx_link_rewrite` (`link_rewrite`) -- Index for URL lookup
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_shop` (
    `id_inspiration` int(11) UNSIGNED NOT NULL,
    `id_shop` int(11) UNSIGNED NOT NULL,
    PRIMARY KEY (`id_inspiration`, `id_shop`),
    KEY `id_shop` (`id_shop`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

// --- Category Tables ---
$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_category` (
    `id_inspiration_category` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `id_parent` int(11) UNSIGNED NOT NULL DEFAULT 0,
    `active` tinyint(1) UNSIGNED NOT NULL DEFAULT 1,
    `position` int(10) UNSIGNED NOT NULL DEFAULT 0,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    `id_shop` int(10) UNSIGNED NOT NULL DEFAULT 1,
    PRIMARY KEY (`id_inspiration_category`),
    KEY `idx_id_parent` (`id_parent`), -- Index for parent lookup
    KEY `idx_active` (`active`), -- Index for filtering active categories
    KEY `id_shop` (`id_shop`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_category_lang` (
    `id_inspiration_category` int(11) UNSIGNED NOT NULL,
    `id_lang` int(11) UNSIGNED NOT NULL,
    `name` varchar(255) NOT NULL,
    `link_rewrite` varchar(255) NOT NULL,
    PRIMARY KEY (`id_inspiration_category`, `id_lang`),
    KEY `idx_name` (`name`), -- Index for searching/sorting by name
    KEY `idx_link_rewrite` (`link_rewrite`) -- Index for URL lookup
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_category_shop` (
    `id_inspiration_category` int(11) UNSIGNED NOT NULL,
    `id_shop` int(11) UNSIGNED NOT NULL,
    PRIMARY KEY (`id_inspiration_category`, `id_shop`),
    KEY `id_shop` (`id_shop`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

// --- Link Table: Inspiration <-> Category ---
$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_to_category` (
    `id_inspiration` int(11) UNSIGNED NOT NULL,
    `id_inspiration_category` int(11) UNSIGNED NOT NULL,
    PRIMARY KEY (`id_inspiration`, `id_inspiration_category`),
    KEY `idx_id_inspiration_category` (`id_inspiration_category`) -- Index for looking up inspirations by category
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

// --- Link Table: Inspiration <-> Product ---
$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_product` (
    `id_inspiration` int(11) UNSIGNED NOT NULL,
    `id_product` int(11) UNSIGNED NOT NULL,
    `position_x` int(11) NOT NULL,
    `position_y` int(11) NOT NULL,
    PRIMARY KEY (`id_inspiration`, `id_product`),
    KEY `idx_id_product` (`id_product`) -- Index for potentially looking up inspirations by product
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

// --- Link Table: Inspiration Image <-> Product ---
$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_image_product` (
    `id_inspiration_image` int(11) UNSIGNED NOT NULL,
    `id_product` int(11) UNSIGNED NOT NULL,
    `position_x` DECIMAL(5,2) NOT NULL,
    `position_y` DECIMAL(5,2) NOT NULL,
    PRIMARY KEY (`id_inspiration_image`, `id_product`),
    KEY `idx_id_product` (`id_product`),
    KEY `idx_id_inspiration_image` (`id_inspiration_image`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

// --- Link Table: Inspiration <-> Related Inspirations ---
$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'inspiration_related` (
    `id_inspiration` int(11) UNSIGNED NOT NULL,
    `id_related_inspiration` int(11) UNSIGNED NOT NULL,
    `position` int(10) UNSIGNED NOT NULL DEFAULT 0,
    PRIMARY KEY (`id_inspiration`, `id_related_inspiration`),
    KEY `idx_id_related_inspiration` (`id_related_inspiration`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

$success = true;
foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        // Log error or display message if possible
        error_log('Error executing SQL query during ' . $this->name . ' installation: ' . Db::getInstance()->getMsgError() . ' - Query: ' . $query);
        $success = false;
        // Optionally break, or collect all errors
    }
}

// Return true only if all queries succeeded
return $success;

?>
