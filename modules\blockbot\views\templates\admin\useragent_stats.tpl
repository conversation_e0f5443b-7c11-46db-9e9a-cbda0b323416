
<div class="panel">
    <div class="panel-heading">
        <i class="icon-shield"></i>
        {l s='Block Bot Management' mod='blockbot'}
    </div>
    
    <!-- Navigation Tabs -->
    <div class="nav-tabs-container" style="margin-bottom: 20px;">
        <ul class="nav nav-tabs" role="tablist">
            <li style="margin-right: 5px;">
                <a href="{$admin_url}&tab=ip_stats" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-list"></i>
                    {l s='IP Statistics' mod='blockbot'}
                </a>
            </li>
            <li class="active" style="margin-right: 5px;">
                <a href="{$admin_url}&tab=useragent_stats" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-user"></i>
                    {l s='User Agent Statistics' mod='blockbot'}
                </a>
            </li>
            <li>
                <a href="{$admin_url}&tab=settings" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-cogs"></i>
                    {l s='Settings' mod='blockbot'}
                </a>
            </li>
        </ul>
    </div>

    <div class="panel-body">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="icon-user"></i>
                    {l s='User Agent Statistics' mod='blockbot'}
                </h3>
            </div>
            <div class="panel-body">
                <div class="alert alert-info">
                    <p><strong>{l s='Threshold for automatic blocking:' mod='blockbot'}</strong> {$threshold_useragent} {l s='visits' mod='blockbot'}</p>
                </div>

                <!-- Clear Log Button -->
                <div class="row" style="margin-bottom: 20px;">
                    <div class="col-md-12">
                        <form method="post" action="{$admin_url}&tab=useragent_stats" onsubmit="return confirm('{l s='Are you sure you want to clear all logs?' mod='blockbot'}');" style="display: inline-block;">
                            <button type="submit" name="clearLog" class="btn btn-danger">
                                <i class="icon-trash"></i>
                                {l s='Clear All Logs' mod='blockbot'}
                            </button>
                        </form>
                    </div>
                </div>

        {if $blocked_agents}
        <div class="alert alert-info">
            <h4>{l s='Currently Blocked User Agents:' mod='blockbot'}</h4>
            <div style="line-height: 2.5;">
                {foreach $blocked_agents as $blocked_agent}
                    <span class="label label-danger" style="margin: 2px; display: inline-block; max-width: 200px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                        {$blocked_agent|truncate:30:"..."}
                        <a href="{$admin_url}&tab=useragent_stats&unblockUserAgent=1&user_agent={$blocked_agent|urlencode}"
                           onclick="return confirm('{l s='Are you sure you want to unblock this User Agent?' mod='blockbot'}')"
                           style="color: white; margin-left: 5px;">×</a>
                    </span>
                {/foreach}
            </div>
        </div>
        {/if}

        <form method="post" action="{$admin_url}&tab=useragent_stats" id="blockAgentsForm">
            <div class="row" style="margin-bottom: 15px;">
                <div class="col-md-12 text-right">
                    <div class="btn-group">
                        <button class="btn btn-outline-secondary dropdown-toggle js-bulk-actions-btn" data-toggle="dropdown" aria-expanded="false" id="bulkActionsBtn" disabled>
                            {l s='Bulk actions' mod='blockbot'}
                            <i class="icon-caret-down"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a href="#" onclick="selectAllUserAgents(); return false;">
                                    <i class="icon-check"></i>
                                    {l s='Select All' mod='blockbot'}
                                </a>
                            </li>
                            <li>
                                <a href="#" onclick="deselectAllUserAgents(); return false;">
                                    <i class="icon-remove"></i>
                                    {l s='Deselect All' mod='blockbot'}
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="#" onclick="submitBulkAction('blockSelectedUserAgents'); return false;">
                                    <i class="icon-ban-circle"></i>
                                    {l s='Block selected User Agents' mod='blockbot'}
                                </a>
                            </li>
                            <li>
                                <a href="#" onclick="submitBulkAction('deleteSelectedUserAgentLogs'); return false;">
                                    <i class="icon-trash"></i>
                                    {l s='Delete logs for selected User Agents' mod='blockbot'}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAllAgentCheckbox">
                            </th>
                            <th>{l s='User Agent' mod='blockbot'}</th>
                            <th>{l s='Visit Count' mod='blockbot'}</th>
                            <th>{l s='Last Visit' mod='blockbot'}</th>
                            <th>{l s='IP Addresses' mod='blockbot'}</th>
                            <th>{l s='Actions' mod='blockbot'}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $agent_stats as $stat}
                        <tr {if $stat.count >= $threshold_useragent && !$stat.is_standard_browser}class="danger"{/if}>
                            <td>
                                <input type="checkbox" name="selected_agents[]" value="{$stat.user_agent}" class="agent-checkbox">
                            </td>
                            <td>
                                <div style="max-width: 300px; word-break: break-all;">
                                    <strong>{$stat.user_agent}</strong>
                                    {if $stat.count >= $threshold_useragent && !$stat.is_standard_browser}
                                        <br><span class="label label-danger">{l s='Suspicious' mod='blockbot'}</span>
                                    {elseif $stat.is_standard_browser}
                                        <br><span class="label label-info">{l s='Standard Browser' mod='blockbot'}</span>
                                    {/if}
                                </div>
                            </td>
                            <td>
                                <span class="badge {if $stat.count >= $threshold_useragent && !$stat.is_standard_browser}badge-danger{else}badge-info{/if}">
                                    {$stat.count}
                                </span>
                            </td>
                            <td>{$stat.last_visit}</td>
                            <td>
                                <div style="max-height: 100px; overflow: hidden;">
                                    {foreach $stat.ips as $ip}
                                        <span class="label label-default" style="margin: 1px; font-size: 10px;">
                                            {$ip}
                                        </span>
                                    {/foreach}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group-action">
                                    <div class="btn-group pull-right">
                                        <button class="btn btn-xs btn-default dropdown-toggle" type="button" data-toggle="dropdown" style="padding: 6px 12px;">
                                            {l s='Actions' mod='blockbot'}
                                            <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            {if !in_array($stat.user_agent, $blocked_agents)}
                                            <li>
                                                <a href="{$admin_url}&tab=useragent_stats&action=block&user_agent={$stat.user_agent|urlencode}"
                                                   onclick="return confirm('{l s='Are you sure you want to block this User Agent?' mod='blockbot'}');">
                                                    <i class="icon-ban-circle"></i>
                                                    {l s='Block User Agent' mod='blockbot'}
                                                </a>
                                            </li>
                                            {else}
                                            <li class="disabled">
                                                <a href="#"><i class="icon-ban-circle"></i> {l s='Already Blocked' mod='blockbot'}</a>
                                            </li>
                                            {/if}
                                            <li>
                                                <a href="{$admin_url}&tab=useragent_stats&action=delete&user_agent={$stat.user_agent|urlencode}"
                                                   onclick="return confirm('{l s='Are you sure you want to delete all logs for this User Agent?' mod='blockbot'}');">
                                                    <i class="icon-trash"></i>
                                                    {l s='Delete Logs' mod='blockbot'}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {foreachelse}
                        <tr>
                            <td colspan="6" class="text-center">
                                <em>{l s='No data available' mod='blockbot'}</em>
                            </td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
        </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllAgentCheckbox');
    const agentCheckboxes = document.querySelectorAll('.agent-checkbox');
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');

    function updateBulkActionsButton() {
        const checkedBoxes = document.querySelectorAll('.agent-checkbox:checked');
        if (bulkActionsBtn) {
            bulkActionsBtn.disabled = checkedBoxes.length === 0;
        }
    }

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            agentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsButton();
        });
    }

    agentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionsButton);
    });

    // Initial check
    updateBulkActionsButton();
});

function selectAllUserAgents() {
    const agentCheckboxes = document.querySelectorAll('.agent-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllAgentCheckbox');

    agentCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    if (selectAllCheckbox) selectAllCheckbox.checked = true;

    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    if (bulkActionsBtn) bulkActionsBtn.disabled = false;
}

function deselectAllUserAgents() {
    const agentCheckboxes = document.querySelectorAll('.agent-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllAgentCheckbox');

    agentCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    if (selectAllCheckbox) selectAllCheckbox.checked = false;

    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    if (bulkActionsBtn) bulkActionsBtn.disabled = true;
}

function submitBulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.agent-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('{l s='Please select at least one User Agent.' mod='blockbot'}');
        return false;
    }

    let confirmMessage = '';
    if (action === 'blockSelectedUserAgents') {
        confirmMessage = '{l s='Are you sure you want to block selected User Agents?' mod='blockbot'}';
    } else if (action === 'deleteSelectedUserAgentLogs') {
        confirmMessage = '{l s='Are you sure you want to delete logs for selected User Agents?' mod='blockbot'}';
    }

    if (confirm(confirmMessage)) {
        const form = document.getElementById('blockAgentsForm');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = action;
        input.value = '1';
        form.appendChild(input);
        form.submit();
    }
}
</script>
