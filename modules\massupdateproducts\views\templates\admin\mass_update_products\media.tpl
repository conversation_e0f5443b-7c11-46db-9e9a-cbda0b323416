{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

{if isset($tinymce) && $tinymce}
	<script type="text/javascript">
		var iso = '{$iso|addslashes}';
		var pathCSS = '{$smarty.const._THEME_CSS_DIR_|addslashes}';
		var ad = '{$ad|addslashes}';
		{literal}
		var tinySetup = tinySetup || function (config)
		{
			if (!config)
				config = {};

			//var editor_selector = 'rte';

			if (typeof config.editor_selector != 'undefined')
				config.selector = '.' + config.editor_selector;
			{/literal}{if $ps16}{literal}
			default_config = {
				selector: ".rte",
				plugins: "colorpicker link image paste pagebreak table contextmenu filemanager table code media autoresize textcolor anchor",
				browser_spellcheck: true,
				toolbar1: "code,|,bold,italic,underline,strikethrough,|,alignleft,aligncenter,alignright,alignfull,formatselect,|,blockquote,colorpicker,pasteword,|,bullist,numlist,|,outdent,indent,|,link,unlink,|,anchor,|,media,image",
				toolbar2: "",
				external_filemanager_path: ad + "/filemanager/",
				filemanager_title: "File manager",
				external_plugins: {"filemanager": ad + "/filemanager/plugin.min.js"},
				language: iso,
				skin: "prestashop",
				statusbar: false,
				relative_urls: false,
				convert_urls: false,
				entity_encoding: "raw",
				extended_valid_elements: "em[class|name|id]",
				valid_children: "+*[*]",
				valid_elements: "*[*]",
				menu: {
					edit: {title: 'Edit', items: 'undo redo | cut copy paste | selectall'},
					insert: {title: 'Insert', items: 'media image link | pagebreak'},
					view: {title: 'View', items: 'visualaid'},
					format: {title: 'Format', items: 'bold italic underline strikethrough superscript subscript | formats | removeformat'},
					table: {title: 'Table', items: 'inserttable tableprops deletetable | cell row column'},
					tools: {title: 'Tools', items: 'code'}
				}
			};
			{/literal}{else}{literal}
				default_config = {
					mode : "specific_textareas",
					theme : "advanced",
					skin:"cirkuit",
					editor_selector : "rte",
					editor_deselector : "noEditor",
					plugins : "safari,pagebreak,style,table,advimage,advlink,inlinepopups,media,contextmenu,paste,fullscreen,xhtmlxtras,preview",
					// Theme options
					theme_advanced_buttons1 : "newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
					theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,,|,forecolor,backcolor, media, fullscreen",
				//	theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,|,ltr,rtl,|",
				//	theme_advanced_buttons4 : "styleprops,|,cite,abbr,acronym,del,ins,attribs,pagebreak",
					theme_advanced_buttons3 : "",
					theme_advanced_buttons4 : "",	
					theme_advanced_toolbar_location : "top",
					theme_advanced_toolbar_align : "left",
					theme_advanced_statusbar_location : "bottom",
					theme_advanced_resizing : true,
					content_css : pathCSS+"global.css",
					document_base_url : ad,
					width: "600",
					height: "auto",
					font_size_style_values : "8pt, 10pt, 12pt, 14pt, 18pt, 24pt, 36pt",
					elements : "nourlconvert,ajaxfilemanager",
					file_browser_callback : "ajaxfilemanager",
					entity_encoding: "raw",
					convert_urls : false,
					language : iso
				}
				{/literal}{/if}{literal}
			$.each(default_config, function (index, el)
			{
				if (config[index] === undefined)
					config[index] = el;
			});

			tinyMCE.init(config);
		}
		{/literal}
		$(function () {
			tinySetup({
				mode: 'none',
			});
		});
	</script>
{/if}

<script type="text/javascript">
    var noSelected = '{l s='Choose or enter one of option' mod='massupdateproducts'}';
    var search = '{l s='Search' mod='massupdateproducts'}';
    var serverError = '{l s='Server error' mod='massupdateproducts'}';
    var productsNotFound = '{l s='Products not found' mod='massupdateproducts'}';
    var productsCountFound = '{l s='Number of products found' mod='massupdateproducts'}';
    var validateError = '{l s='Invalid value in the red fields' mod='massupdateproducts'}';
    var productSave = '{l s='Product save' mod='massupdateproducts'}';
    var productNotSave = '{l s='Product not save' mod='massupdateproducts'}';

    var dpcurrentText = '{l s='Now' mod='massupdateproducts'}';
    var dpcloseText = '{l s='Done' mod='massupdateproducts'}';
    var dptimeOnlyTitle = '{l s='Select time' mod='massupdateproducts'}';
    var dptimeText = '{l s='Time' mod='massupdateproducts'}';
    var dphourText = '{l s='Hour' mod='massupdateproducts'}';
    var dpminuteText = '{l s='Minute' mod='massupdateproducts'}';
    var productsRemove = '{l s='Promotion remove' mod='massupdateproducts'}';
    var productsNotRemove = '{l s='Promotion not remove' mod='massupdateproducts'}';

    var productRemoved = '{l s='Product removed' mod='massupdateproducts'}';
    var combinationRemoved = '{l s='Combination removed' mod='massupdateproducts'}';

    var allProductsSave = '{l s='All product saved' mod='massupdateproducts'}';

    var removeProduct = '{l s='Are you sure you want to remove this product?' mod='massupdateproducts'}';

    var errorInLanguage = '{l s='At least one of language is wrong or empty (red field)' mod='massupdateproducts'}';
    var exitProcess = '{l s='Are you sure you want leave this page? Still in progress!' mod='massupdateproducts'}';
    var busy = '{l s='busy' mod='massupdateproducts'}...';
    var picture_added = '{l s='Picture added' mod='massupdateproducts'}';
    var picture_not_added = '{l s='Picture not added' mod='massupdateproducts'}';
    
    var $page = 0;
    var $elements = 20;
</script>
<style>
    #products_list .error {
        background-color: #FF0000;
    }

    #products_list .succ_res td:first-of-type {
        background-color: #41b854;
    }

    #products_list .err_res td:first-of-type {
        background-color: #f66;
    }

    .mupp {
        overflow: hidden;
    }
</style>