{extends file="helpers/form/form.tpl"}
{block name="script"}
  $(document).ready(function() {
  $(document).on('click', 'button[name="copyToClippord"]', function(){
  var target = $(this).data('target');
  $(target).focus().select();
  document.execCommand("copy");
  });
  });
{/block}
{block name="input"}
  {if $input.type == 'copyToClippord'}
    <div class="input-group">
      <input type="text" class="col-xs-6 col-sm-4" readonly name="{$input.name|escape:'html':'UTF-8'}" id="{$input.name|escape:'html':'UTF-8'}" value="{$input.value}" />
      <span class="input-group-addon" style="padding: 0px;">
        <button type="button" name="copyToClippord" data-target="#{$input.name|escape:'html':'UTF-8'}" class="btn btn-success">
          {l s='Copy to clipboard' mod='webixa_content'}
        </button>
      </span>
    </div>
    {if !empty($input.help)}
      <p class="help-block">{$input.help|escape:'html':'UTF-8'}</p>
    {/if}
    </div>
  {else}
    {$smarty.block.parent}
  {/if}
{/block}