<?php

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_1_0_1($module)
{
    $sql = [];
    $charset = 'utf8';
    if (version_compare(_PS_VERSION_, '1.7.7', '>=')) {
        $charset = 'utf8mb4';
    }
    $mysql_row_format = '';
    if (_MYSQL_ENGINE_ == 'InnoDB') {
        $mysql_row_format = ' ROW_FORMAT=DYNAMIC';
    }

    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_categoryproducts` (
        `id_webixa_content_categoryproducts` INT(11) unsigned NOT NULL AUTO_INCREMENT,
        `id_webixa_content_block` INT(11) unsigned NOT NULL,
        `id_category` INT(11) unsigned NOT NULL,
        `id_group` INT(11) unsigned NULL DEFAULT NULL,
        `id_customer` INT(11) unsigned NULL DEFAULT NULL,
        `nbr_products` INT(11) unsigned NOT NULL DEFAULT "12",
        `search_subcategories` TINYINT(1) unsigned NOT NULL,
        `active` TINYINT(1) unsigned NOT NULL,
        PRIMARY KEY (`id_webixa_content_categoryproducts`),
        UNIQUE (`id_webixa_content_block`, `id_customer`),
        UNIQUE (`id_webixa_content_block`, `id_group`),
        INDEX (`id_category`),
        INDEX (`active`),
        FOREIGN KEY (`id_webixa_content_block`)
            REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_category`)
            REFERENCES `' . _DB_PREFIX_ . 'category`(`id_category`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_group`)
            REFERENCES `' . _DB_PREFIX_ . 'group`(`id_group`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_customer`)
            REFERENCES `' . _DB_PREFIX_ . 'customer`(`id_customer`)
            ON DELETE CASCADE
    ) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_categoryproducts_shop` (
        `id_webixa_content_categoryproducts` INT(11) unsigned NOT NULL,
        `id_shop` INT(11) NOT NULL,
        PRIMARY KEY (`id_webixa_content_categoryproducts`, `id_shop`),
        FOREIGN KEY (`id_webixa_content_categoryproducts`)
            REFERENCES `' . _DB_PREFIX_ . 'webixa_content_categoryproducts`(`id_webixa_content_categoryproducts`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_shop`)
            REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
            ON DELETE CASCADE
    ) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

    foreach ($sql as $query) {
        if (Db::getInstance()->execute($query) == false) {
            throw new Exception('Install DB failed');
        }
    }

    return $module->installAdminTabs();
}
