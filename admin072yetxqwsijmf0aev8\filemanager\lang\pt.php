<?php
/*
* Important - this file MUST implement all strings defined in base en.php file
*/
define('lang_Select', 'Seleccionar');
define('lang_Erase', 'Eliminar');
define('lang_Open', 'Abrir');
define('lang_Confirm_del', 'Tem certeza que pretende eliminar este arquivo?');
define('lang_All', 'Todos');
define('lang_Files', 'Ficheiros');
define('lang_Images', 'Imagens');
define('lang_Archives', 'Compactados');
define('lang_Error_Upload', 'O ficheiro enviado é maior que o limite permitido.');
define('lang_Error_extension', 'Extensão não permitida.');
define('lang_Upload_file', 'Carregar ficheiro');
define('lang_Filters', 'Filtro');
define('lang_Videos', 'Vídeos');
define('lang_Music', 'Música');
define('lang_New_Folder', 'Nova pasta');
define('lang_Folder_Created', 'Pasta criada com sucesso');
define('lang_Existing_Folder', 'Pasta existente');
define('lang_Confirm_Folder_del', 'Tem certeza que pretende eliminar a pasta e todo o seu conteúdo?');
define('lang_Return_Files_List', 'Voltar à lista de ficheiros');
define('lang_Preview', 'Pré-visualizar');
define('lang_Download', 'Descarregar');
define('lang_Insert_Folder_Name', 'Insira o nome da pasta:');
define('lang_Root', 'root');
define('lang_Rename', 'Mudar o nome');
define('lang_Back', 'de volta');
define('lang_View', 'View');
define('lang_View_list', 'List view');
define('lang_View_columns_list', 'Columns list view');
define('lang_View_boxes', 'Box view');
define('lang_Toolbar', 'Toolbar');
define('lang_Actions', 'Actions');
define('lang_Rename_existing_file', 'The file is already existing');
define('lang_Rename_existing_folder', 'The folder is already existing');
define('lang_Empty_name', 'The name is empty');
define('lang_Text_filter', 'text filter');
define('lang_Swipe_help', 'Swipe the name of file/folder to show options');
define('lang_Upload_base', 'Base upload');
define('lang_Upload_java', 'JAVA upload (big size files)');
define('lang_Upload_java_help', "If the Java Applet don't load 1. make sure you have Java installed otherwise <a href='http://java.com/en/download/'>[download link]</a> 2. make sure nothing is blocked from firewall");
define('lang_Upload_base_help', "Drag & Drop file/s inside above area or click in it (for modern browsers) otherwise select the file and click on button. When the upload end, click on upper return button.");
define('lang_Type_dir', 'dir');
define('lang_Type', 'Type');
define('lang_Dimension', 'Dimension');
define('lang_Size', 'Size');
define('lang_Date', 'Date');
define('lang_Filename', 'Name');
define('lang_Operations', 'Operations');
define('lang_Date_type', 'y-m-d');
define('lang_OK', 'OK');
define('lang_Cancel', 'Cancel');
define('lang_Sorting', 'sorting');
define('lang_Show_url', 'show URL');
define('lang_Extract', 'extract here');
define('lang_File_info', 'file info');
define('lang_Edit_image', 'edit image');
define('lang_Duplicate', 'Duplicate');
