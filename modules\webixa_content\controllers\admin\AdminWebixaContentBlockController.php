<?php

use Webixa\Content\Module\WebixaConfig;

class AdminWebixaContentBlockController extends ModuleAdminController
{
    public $bootstrap = true;

    public function __construct()
    {
        $this->bootstrap = true;
        parent::__construct();

        if (version_compare(_PS_VERSION_, '1.7', '>=')) {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
            $this->tabAccess['view'] = Module::getPermissionStatic($this->module->id, 'view');

            $configAccess = Module::getPermissionStatic($this->module->id, 'configure');
            $this->tabAccess['add'] = $configAccess;
            $this->tabAccess['edit'] = $configAccess;
            $this->tabAccess['delete'] = $configAccess;
        } else {
            $this->tabAccess = Profile::getProfileAccess($this->context->employee->id_profile, Tab::getIdFromClassName('AdminModules'));
        }
    }

    public function initProcess()
    {
        $this->initList();

        parent::initProcess();
    }

    public function initList()
    {
        $this->table = WebixaContentBlock::$definition['table'];
        $this->className = 'WebixaContentBlock';
        $this->identifier = WebixaContentBlock::$definition['primary'];
        $this->_defaultOrderBy = 'name';
        $this->_defaultOrderWay = 'ASC';

        $this->lang = true;
        if (Shop::isFeatureActive()) {
            Shop::addTableAssociation($this->table, ['type' => 'shop']);
        }

        $this->_select = '
            SUM(IF(i.' . $this->identifier . ' IS NOT NULL, 1, 0)) as ' . WebixaConfig::BLOCK_TYPE_ITEMS . '_nbr,
            SUM(IF(s.' . $this->identifier . ' IS NOT NULL, 1, 0)) as ' . WebixaConfig::BLOCK_TYPE_SLIDES . '_nbr,
            SUM(IF(l.' . $this->identifier . ' IS NOT NULL, 1, 0)) as ' . WebixaConfig::BLOCK_TYPE_LINKS . '_nbr,
            SUM(IF(cp.' . $this->identifier . ' IS NOT NULL, 1, 0)) as ' . WebixaConfig::BLOCK_TYPE_CATEGORY_PRODUCTS . '_nbr,
            SUM(IF(bn.' . $this->identifier . ' IS NOT NULL, 1, 0)) as ' . WebixaConfig::BLOCK_TYPE_BANNERS . '_nbr,
            NULL as ' . WebixaConfig::BLOCK_TYPE_HTML . '_nbr,
            a.type as nbr,
            h.hooks
        ';
        $this->_join = '
            LEFT JOIN `' . _DB_PREFIX_ . WebixaContentItem::$definition['table'] . '` i
                ON (a.' . $this->identifier . '=i.' . $this->identifier . ')
            LEFT JOIN `' . _DB_PREFIX_ . WebixaContentSlide::$definition['table'] . '` s
                ON (a.' . $this->identifier . '=s.' . $this->identifier . ')
            LEFT JOIN `' . _DB_PREFIX_ . WebixaContentLink::$definition['table'] . '` l
                ON (a.' . $this->identifier . '=l.' . $this->identifier . ')
            LEFT JOIN `' . _DB_PREFIX_ . WebixaContentCategoryProducts::$definition['table'] . '` cp
                ON (a.' . $this->identifier . '=cp.' . $this->identifier . ')
            LEFT JOIN `' . _DB_PREFIX_ . WebixaContentBanner::$definition['table'] . '` bn
                ON (a.' . $this->identifier . '=bn.' . $this->identifier . ')
            LEFT JOIN (
                SELECT GROUP_CONCAT(h.hook ORDER BY h.hook ASC SEPARATOR "\n") as hooks, ' . $this->identifier . '
                FROM `' . _DB_PREFIX_ . WebixaContentHook::$definition['table'] . '_block` hg
                LEFT JOIN `' . _DB_PREFIX_ . WebixaContentHook::$definition['table'] . '` h
                    ON (h.' . WebixaContentHook::$definition['primary'] . '=hg.' . WebixaContentHook::$definition['primary'] . ')
                GROUP BY hg.' . $this->identifier . '
            ) h ON (a.' . $this->identifier . '=h.' . $this->identifier . ')
        ';

        $this->_group = 'GROUP BY a.' . $this->identifier;

        $this->fields_list = [
            $this->identifier => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
            ],
            'name' => [
                'title' => $this->l('Name'),
                'maxlength' => 50,
            ],
            'title' => [
                'title' => $this->l('Title'),
                'filter_key' => 'b!title',
                'maxlength' => 50,
            ],
            'description' => [
                'title' => $this->l('Description'),
                'filter_key' => 'b!description',
                'maxlength' => 50,
            ],
            'type' => [
                'title' => $this->l('Type'),
                'maxlength' => 50,
            ],
            'nbr' => [
                'title' => $this->l('Number'),
                'orderby' => false,
                'search' => false,
                'callback' => 'displayNumber',
            ],
            'hooks' => [
                'title' => $this->l('Hooks'),
                'orderby' => false,
                'search' => false,
                'remove_onclick' => true,
                'callback_object' => 'Tools',
                'callback' => 'nl2br',
            ],
            'active' => [
                'title' => $this->l('Displayed'),
                'align' => 'center',
                'active' => 'status',
                'class' => 'fixed-width-sm',
                'ajax' => true,
                'type' => 'bool',
                'orderby' => false,
            ],
        ];

        $this->addRowAction('view');
        $this->addRowAction('edit');
        $this->addRowAction('assign');
        $this->addRowAction('delete');

        $this->list_skip_actions['view'] = WebixaContentBlock::getSkipAdminViewIds();

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected blocks?'),
            ],
        ];
    }

    public function displayAssignLink($token = null, $id)
    {
        $link = $this->context->link->getAdminLink('AdminWebixaContentHook') . '&id_webixa_content_block=' . $id . '&addwebixa_content_hook_block';

        return
            '<a href="' . $link . '" title="' . $this->l('Assign hook') . '" class="assign_button" data-id_webixa_content_block="' . $id . '">
                <i class="icon-anchor"></i> ' . $this->l('Assign hook') . '
            </a>';
    }

    public function renderView()
    {
        if (!($obj = $this->loadObject(false))) {
            Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentBlock'));
        }
        switch ($obj->type) {
            case WebixaConfig::BLOCK_TYPE_LINKS:
                Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentLink') . '&id_webixa_content_block=' . Tools::getValue('id_webixa_content_block'));
            case WebixaConfig::BLOCK_TYPE_SLIDES:
                Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentSlide') . '&id_webixa_content_block=' . Tools::getValue('id_webixa_content_block'));
            case WebixaConfig::BLOCK_TYPE_ITEMS:
                Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentItem') . '&id_webixa_content_block=' . Tools::getValue('id_webixa_content_block'));
            case WebixaConfig::BLOCK_TYPE_CATEGORY_PRODUCTS:
                Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentCategoryProducts') . '&id_webixa_content_block=' . Tools::getValue('id_webixa_content_block'));
            case WebixaConfig::BLOCK_TYPE_BANNERS:
                Tools::redirectAdmin($this->context->link->getAdminLink('AdminWebixaContentBanner') . '&id_webixa_content_block=' . Tools::getValue('id_webixa_content_block'));
        }
    }

    public function renderForm()
    {
        /** @var WebixaContentBlock $obj */
        if (!($obj = $this->loadObject(true))) {
            return;
        }

        $this->show_form_cancel_button = false;
        $this->fields_form = [
            'tinymce' => true,
            'legend' => [
                'title' => Validate::isLoadedObject($obj) ? $this->l('Update content Block') : $this->l('Add content Block'),
                'icon' => 'icon-cogs',
            ],
            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Type'),
                    'name' => 'type',
                    'options' => [
                        'query' => $this->module->getAvailableBlockTypes(),
                        'id' => 'id',
                        'name' => 'name',
                    ],
                    'readonly' => Validate::isLoadedObject($obj),
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Title'),
                    'name' => 'title',
                    'lang' => true,
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'autoload_rte' => true,
                    'lang' => true,
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Displayed'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Slide time'),
                    'name' => 'slide_time',
                    'class' => 'fixed-width-xl',
                    'suffix' => 'ms',
                    'desc' => $this->l('Used only for slider type'),
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
            ],
            'buttons' => [
                [
                    'title' => $this->l('Cancel'),
                    'id' => $this->table . '_form_cancel_btn',
                    'href' => self::$currentIndex . '&token=' . $this->token,
                    'icon' => 'process-icon-cancel',
                ],
            ],
        ];
        if (Shop::isFeatureActive()) {
            $this->fields_form['input'][] = [
                'type' => 'shop',
                'label' => $this->l('Shop association:'),
                'name' => 'checkBoxShopAsso',
            ];
        }

        return parent::renderForm();
    }

    public function renderList()
    {
        return $this->module->prepareAdminInfoBanner() . parent::renderList();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Webixa content blocks');
    }

    public function initToolbar()
    {
        parent::initToolbar();

        if ('edit' == $this->display || 'add' == $this->display) {
            $this->page_header_toolbar_btn['cancel'] = [
                'href' => self::$currentIndex . '&token=' . $this->token,
                'desc' => $this->l('Back'),
                'icon' => 'process-icon-back',
            ];
        }
    }

    public function setMedia($isNewTheme = false)
    {
        if (!$this->module->active && version_compare(_PS_VERSION_, '8.0.2', '>=')) {
            $this->module->hookDisplayBackOfficeHeader([]);
        }
        parent::setMedia($isNewTheme);
        $this->addJqueryUI([
            'ui.core',
        ]);
        $this->addJqueryPlugin('tablednd');
        if ($isNewTheme) {
            $this->registerJavascript('dnd', _PS_JS_DIR_ . 'admin/dnd.js', ['position' => 'bottom', 'priority' => 150]);
        } else {
            $this->addJS(_PS_JS_DIR_ . 'admin/dnd.js');
        }
    }

    public function postProcess()
    {
        $ret = parent::postProcess();
        if (!$this->ajax) {
            if (!empty($this->action) && method_exists($this, 'process' . ucfirst(Tools::toCamelCase($this->action)))) {
                $this->module->clearModuleCache('*');
            }
        }

        return $ret;
    }

    public static function displayNumber($type, $row)
    {
        return !empty($row[$type . '_nbr']) ? $row[$type . '_nbr'] : 0;
    }

    public function ajaxProcessStatusWebixaContentBlock()
    {
        $id_object = (int) Tools::getValue($this->identifier);

        $sql = 'UPDATE ' . _DB_PREFIX_ . $this->table . ' SET `active`= NOT `active` WHERE ' . $this->identifier . '=' . $id_object;
        $result = Db::getInstance()->execute($sql);

        if ($result) {
            $this->module->clearModuleCache('*');
            $response = json_encode(['success' => 1, 'text' => $this->l('The status has been updated successfully.')]);
        } else {
            $response = json_encode(['success' => 0, 'text' => $this->l('An error occurred while updating the status.')]);
        }
        $this->ajaxDie($response);
    }

    protected function ajaxDie($value = null, $controller = null, $method = null)
    {
        if (ob_get_length() > 0) {
            ob_end_clean();
        }
        header('Content-Type: application/json');

        if (version_compare(_PS_VERSION_, '1.6.1', '>=')) {
            return parent::ajaxDie($value, $controller, $method);
        }

        header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        echo $value;
        exit;
    }
}
