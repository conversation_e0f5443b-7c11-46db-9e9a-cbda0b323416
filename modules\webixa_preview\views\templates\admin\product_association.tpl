<div class="product-association-container">
    <div class="panel">
        <div class="panel-heading">
            {l s='Associated Products' d='Modules.Webixapreview.Admin'}
        </div>
        <div class="form-wrapper">
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Search Products' d='Modules.Webixapreview.Admin'}
                </label>
                <div class="col-lg-9">
                    <div class="input-group">
                        <input type="text" id="product_autocomplete_input" name="product_autocomplete_input"
                               class="form-control ac_input" autocomplete="off" placeholder="{l s='Search for a product' d='Modules.Webixapreview.Admin'}" />
                        <span class="input-group-addon"><i class="icon-search"></i></span>
                    </div>
                    <div id="product_autocomplete_container"></div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Current Images' d='Modules.Webixapreview.Admin'}
                </label>
                <div class="col-lg-9">
                    <div class="image-list row">
                        {if isset($inspiration_images) && $inspiration_images}
                            {foreach from=$inspiration_images item=image}
                                <div class="col-xs-6 col-md-3 image-item {if $image.main}main-image{/if}" data-id="{$image.id_inspiration_image}">
                                    <div class="panel">
                                        <div class="panel-heading">
                                            {if $image.main}
                                                <span class="label label-success">{l s='Main' d='Modules.Webixapreview.Admin'}</span>
                                            {/if}
                                            {l s='Image' d='Modules.Webixapreview.Admin'} #{$image.id_inspiration_image}
                                        </div>
                                        <div class="panel-body">
                                            <img src="{$image.url}" class="img-responsive" alt="" />
                                        </div>
                                        <div class="panel-footer">
                                            <button type="button" class="btn btn-default btn-sm set-main-image" data-id="{$image.id_inspiration_image}">
                                                <i class="icon-star"></i> {l s='Set as Main' d='Modules.Webixapreview.Admin'}
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm delete-image" data-id="{$image.id_inspiration_image}">
                                                <i class="icon-trash"></i> {l s='Delete' d='Modules.Webixapreview.Admin'}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {/foreach}
                        {else}
                            <div class="alert alert-info">
                                {l s='No images uploaded yet. Use the image upload field to add images.' d='Modules.Webixapreview.Admin'}
                            </div>
                        {/if}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Current Products' d='Modules.Webixapreview.Admin'}
                </label>
                <div class="col-lg-9">
                    <div id="associated-products-container" class="panel-body">
                        <table class="table" id="associated-products-table">
                            <thead>
                                <tr>
                                    <th>{l s='ID' d='Modules.Webixapreview.Admin'}</th>
                                    <th>{l s='Product' d='Modules.Webixapreview.Admin'}</th>
                                    <th>{l s='Position X (%)' d='Modules.Webixapreview.Admin'}</th>
                                    <th>{l s='Position Y (%)' d='Modules.Webixapreview.Admin'}</th>
                                    <th>{l s='Image' d='Modules.Webixapreview.Admin'}</th>
                                    <th>{l s='Actions' d='Modules.Webixapreview.Admin'}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {if isset($inspiration_products) && $inspiration_products}
                                    {foreach from=$inspiration_products item=product}
                                        <tr id="product_{$product.id_product}" class="product-row">
                                            <td>{$product.id_product}</td>
                                            <td>{$product.name|escape:'html':'UTF-8'}</td>
                                            <td>
                                                <input type="number" name="associated_products[{$product.id_product}][x]"
                                                       value="{$product.position_x|default:50}" min="0" max="100" class="form-control position-input" />
                                            </td>
                                            <td>
                                                <input type="number" name="associated_products[{$product.id_product}][y]"
                                                       value="{$product.position_y|default:50}" min="0" max="100" class="form-control position-input" />
                                            </td>
                                            <td>
                                                <select name="associated_products[{$product.id_product}][image_id]" class="form-control product-image-select">
                                                    <option value="0">{l s='Select image' d='Modules.Webixapreview.Admin'}</option>
                                                    {if isset($inspiration_images) && $inspiration_images}
                                                        {foreach from=$inspiration_images item=image}
                                                            <option value="{$image.id_inspiration_image}" {if isset($product.id_inspiration_image) && $product.id_inspiration_image == $image.id_inspiration_image}selected="selected"{/if}>
                                                                {l s='Image' d='Modules.Webixapreview.Admin'} #{$image.id_inspiration_image} {if $image.main}({l s='Main' d='Modules.Webixapreview.Admin'}){/if}
                                                            </option>
                                                        {/foreach}
                                                    {/if}
                                                </select>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-default remove-product-btn">
                                                    <i class="icon-trash"></i> {l s='Remove' d='Modules.Webixapreview.Admin'}
                                                </button>
                                            </td>
                                        </tr>
                                    {/foreach}
                                {/if}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Product autocomplete using jQuery UI
    $('#product_autocomplete_input').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                dataType: 'json',
                data: {
                    ajax: 1,
                    action: 'searchProducts',
                    q: request.term,
                    id_lang: {$id_lang|intval}
                },
                success: function(data) {
                    response($.map(data, function(item) {
                        return {
                            label: item.name,
                            value: item.name,
                            id: item.id_product
                        };
                    }));
                }
            });
        },
        minLength: 3,
        select: function(event, ui) {
            // Check if product already exists
            if ($('#product_' + ui.item.id).length > 0) {
                showErrorMessage('{l s='This product is already associated' d='Modules.Webixapreview.Admin'}');
                return false;
            }

            // Add product to table
            var imageOptions = '';
            {if isset($inspiration_images) && $inspiration_images}
                {foreach from=$inspiration_images item=image}
                    imageOptions += '<option value="{$image.id_inspiration_image}">{l s='Image' d='Modules.Webixapreview.Admin'} #{$image.id_inspiration_image} {if $image.main}({l s='Main' d='Modules.Webixapreview.Admin'}){/if}</option>';
                {/foreach}
            {/if}

            var productRow = '<tr id="product_' + ui.item.id + '" class="product-row">' +
                '<td>' + ui.item.id + '</td>' +
                '<td>' + ui.item.label + '</td>' +
                '<td><input type="number" name="associated_products[' + ui.item.id + '][x]" value="50" min="0" max="100" class="form-control position-input" /></td>' +
                '<td><input type="number" name="associated_products[' + ui.item.id + '][y]" value="50" min="0" max="100" class="form-control position-input" /></td>' +
                '<td><select name="associated_products[' + ui.item.id + '][image_id]" class="form-control product-image-select">' +
                '<option value="0">{l s='Select image' d='Modules.Webixapreview.Admin'}</option>' +
                imageOptions +
                '</select></td>' +
                '<td><button type="button" class="btn btn-default remove-product-btn"><i class="icon-trash"></i> {l s='Remove' d='Modules.Webixapreview.Admin'}</button></td>' +
                '</tr>';

            $('#associated-products-table tbody').append(productRow);
            $('#product_autocomplete_input').val('');
            return false;
        }
    });

    // Remove product
    $(document).on('click', '.remove-product-btn', function() {
        $(this).closest('tr').remove();
    });

    // Set main image
    $(document).on('click', '.set-main-image', function() {
        var imageId = $(this).data('id');
        $('#main_image_id').val(imageId);

        // Update UI
        $('.image-item').removeClass('main-image');
        $('.image-item[data-id="' + imageId + '"]').addClass('main-image');

        // Update labels
        $('.image-item .label-success').remove();
        $('.image-item[data-id="' + imageId + '"] .panel-heading').prepend(
            '<span class="label label-success">{l s='Main' d='Modules.Webixapreview.Admin'}</span> '
        );

        showSuccessMessage('{l s='Main image updated' d='Modules.Webixapreview.Admin'}');
    });

    // Delete image
    $(document).on('click', '.delete-image', function() {
        if (confirm('{l s='Are you sure you want to delete this image?' d='Modules.Webixapreview.Admin'}')) {
            var imageId = $(this).data('id');
            var imageItem = $(this).closest('.image-item');

            $.ajax({
                url: '{$link->getAdminLink('AdminInspiration')|escape:'javascript':'UTF-8'}',
                type: 'POST',
                data: {
                    ajax: 1,
                    action: 'DeleteImage',
                    id_inspiration_image: imageId
                },
                success: function(response) {
                    var data = JSON.parse(response);
                    if (data.success) {
                        imageItem.remove();
                        showSuccessMessage('{l s='Image deleted successfully' d='Modules.Webixapreview.Admin'}');
                    } else {
                        showErrorMessage('{l s='Failed to delete image' d='Modules.Webixapreview.Admin'}');
                    }
                },
                error: function() {
                    showErrorMessage('{l s='Failed to delete image' d='Modules.Webixapreview.Admin'}');
                }
            });
        }
    });
});
</script>

<style>
#associated-products-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f8f8f8;
}
.position-input {
    width: 80px;
}
.image-item.main-image .panel {
    border: 2px solid #72C279;
}
.image-list {
    margin-top: 10px;
}
</style>
