<?php

/**
 * 2018-2024 Webixa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018-2024 Webixa
 * @license   Webixa proprietary license
 */

class WebixaContentHook extends ObjectModel
{
    public $hook;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'webixa_content_hook',
        'primary' => 'id_webixa_content_hook',
        'multilang' => false,
        'fields' => [
            'hook' => ['type' => self::TYPE_STRING, 'validate' => 'isHookName', 'size' => 128, 'required' => true],
        ],
    ];

    public function delete()
    {
        if (parent::delete()) {
            return static::cleanPositions($this->hook);
        }

        return false;
    }

    public static function updatePositions(array $positions)
    {
        $query = 'UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '_block' . '` SET `position` = CASE `' . WebixaContentBlock::$definition['primary'] . '` ';

        $hookId = false;
        foreach ($positions as $pos => $args) {
            preg_match('/tr_(\d+)_(\d+)_\d+/', $args, $matches);
            if (!empty($matches[2])) {
                $hookId = (int) $matches[1];
                $query .= 'WHEN ' . $matches[2] . ' THEN ' . $pos . ' ';
            }
        }
        if (!$hookId) {
            return false;
        }

        $query .= 'ELSE `position` END WHERE `' . static::$definition['primary'] . '`=' . $hookId;

        return Db::getInstance()->execute($query);
    }

    public static function cleanPositions($hook = false)
    {
        $hooks = array_map(
            static function ($row) {
                return $row[static::$definition['primary']];
            },
            Db::getInstance()->executeS('
                SELECT ' . static::$definition['primary'] . ' FROM `' . _DB_PREFIX_ . static::$definition['table'] . '` WHERE 1
                ' . ($hook && Validate::isHookName($hook) ? ' AND `hook` LIKE "' . pSQL($hook) . '"' : '') . '
            ')
        );

        $return = true;
        foreach ($hooks as $hookId) {
            $return &= Db::getInstance()->execute('
                UPDATE `' . _DB_PREFIX_ . static::$definition['table'] . '_block` psi1
                JOIN (
                    SELECT `' . static::$definition['primary'] . '`, `' . WebixaContentBlock::$definition['primary'] . '`, @i := @i+1 new_position
                    FROM `' . _DB_PREFIX_ . static::$definition['table'] . '_block`, (select @i:=-1) temp
                    WHERE `' . static::$definition['primary'] . '` = ' . (int)$hookId . '
                    ORDER BY position asc
                ) psi2 ON psi1.`' . static::$definition['primary'] . '` = psi2.`' . static::$definition['primary'] . '` AND
                    psi1.`' . WebixaContentBlock::$definition['primary'] . '` = psi2.`' . WebixaContentBlock::$definition['primary'] . '`
                SET psi1.position = psi2.new_position
            ');
        }

        return $return;
    }

    public static function getLastPosition($hookId)
    {
        $sql = '
		SELECT MAX(`position`)
		FROM `' . _DB_PREFIX_ . static::$definition['table'] . '_block`
		WHERE `' . static::$definition['primary'] . '` = ' . (int)$hookId;
        $value = Db::getInstance()->getValue($sql);

        return null === $value ? 0 : (int) $value + 1;
    }

    public static function getByHookName($hook)
    {
        $sql = '
		SELECT `' . static::$definition['primary'] . '`, `hook`
		FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`
		WHERE `hook`="' . pSQL($hook) . '"';

        $id = (int)Db::getInstance()->getValue($sql);

        return !empty($id) ? new WebixaContentHook($id) : false;
    }

    public static function getAll()
    {
        $sql = '
		SELECT `' . static::$definition['primary'] . '`, `hook`
		FROM `' . _DB_PREFIX_ . static::$definition['table'] . '`
		WHERE 1';

        return Db::getInstance()->executeS($sql);
    }
}
