<?php

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_1_0_2($module)
{
    $sql = [];
    $charset = 'utf8';
    if (version_compare(_PS_VERSION_, '1.7.7', '>=')) {
        $charset = 'utf8mb4';
    }
    $mysql_row_format = '';
    if (_MYSQL_ENGINE_ == 'InnoDB') {
        $mysql_row_format = ' ROW_FORMAT=DYNAMIC';
    }

    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner` (
        `id_webixa_content_banner` INT(11) unsigned NOT NULL AUTO_INCREMENT,
        `id_webixa_content_block` INT(11) unsigned NOT NULL,
        `show_on_home` TINYINT(1) unsigned NOT NULL,
        `show_to_guest` TINYINT(1) unsigned NOT NULL,
        `show_to_logged` TINYINT(1) unsigned NOT NULL,
        `has_group_restrictions` TINYINT(1) unsigned NOT NULL,
        `has_category_restrictions` TINYINT(1) unsigned NOT NULL,
        `position` INT(11) unsigned NOT NULL,
        `active` TINYINT(1) unsigned NOT NULL,
        PRIMARY KEY (`id_webixa_content_banner`),
        INDEX (`id_webixa_content_block`, `position`),
        INDEX (`show_on_home`),
        INDEX (`show_to_guest`),
        INDEX (`show_to_logged`),
        INDEX (`has_group_restrictions`),
        INDEX (`has_category_restrictions`),
        INDEX (`active`),
        FOREIGN KEY (`id_webixa_content_block`)
            REFERENCES `' . _DB_PREFIX_ . 'webixa_content_block`(`id_webixa_content_block`)
            ON DELETE CASCADE
    ) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_group_restriction` (
        `id_webixa_content_banner` INT(11) unsigned NOT NULL,
        `id_group` INT(11) unsigned NOT NULL,
        PRIMARY KEY (`id_webixa_content_banner`, `id_group`),
        FOREIGN KEY (`id_webixa_content_banner`)
            REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_group`)
            REFERENCES `' . _DB_PREFIX_ . 'group`(`id_group`)
            ON DELETE CASCADE
    ) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_category_restriction` (
        `id_webixa_content_banner` INT(11) unsigned NOT NULL,
        `id_category` INT(11) unsigned NOT NULL,
        PRIMARY KEY (`id_webixa_content_banner`, `id_category`),
        FOREIGN KEY (`id_webixa_content_banner`)
            REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_category`)
            REFERENCES `' . _DB_PREFIX_ . 'category`(`id_category`)
            ON DELETE CASCADE
    ) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_shop` (
        `id_webixa_content_banner` INT(11) unsigned NOT NULL,
        `id_shop` INT(11) NOT NULL,
        PRIMARY KEY (`id_webixa_content_banner`, `id_shop`),
        FOREIGN KEY (`id_webixa_content_banner`)
            REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_shop`)
            REFERENCES `' . _DB_PREFIX_ . 'shop`(`id_shop`)
            ON DELETE CASCADE
    ) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'webixa_content_banner_lang` (
        `id_webixa_content_banner` INT(11) unsigned NOT NULL,
        `id_lang` INT(11) NOT NULL,
        `alt` VARCHAR(128) NOT NULL DEFAULT "",
        `link` VARCHAR(512) NOT NULL DEFAULT "",
        PRIMARY KEY (`id_webixa_content_banner`, `id_lang`),
        INDEX (`alt`),
        FOREIGN KEY (`id_webixa_content_banner`)
            REFERENCES `' . _DB_PREFIX_ . 'webixa_content_banner`(`id_webixa_content_banner`)
            ON DELETE CASCADE,
        FOREIGN KEY (`id_lang`)
            REFERENCES `' . _DB_PREFIX_ . 'lang`(`id_lang`)
            ON DELETE CASCADE
    ) ENGINE=' . _MYSQL_ENGINE_ . $mysql_row_format . ' DEFAULT CHARSET=' . $charset;

    foreach ($sql as $query) {
        if (Db::getInstance()->execute($query) == false) {
            throw new Exception('Install DB failed');
        }
    }

    return $module->installAdminTabs();
}
