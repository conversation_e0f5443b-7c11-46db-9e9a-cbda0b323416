/**
 * Inspiration admin JavaScript
 */
$(document).ready(function() {
    // Product search and selection
    initProductSearch();
    
    // Image management
    initImageManagement();
    
    // Product positioning
    initProductPositioning();
});

/**
 * Initialize product search functionality
 */
function initProductSearch() {
    // Initialize product search autocomplete
    $('#associated_products').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: inspiration_ajax_url,
                dataType: 'json',
                data: {
                    ajax: 1,
                    action: 'GetProducts',
                    q: request.term
                },
                success: function(data) {
                    response($.map(data, function(item) {
                        return {
                            label: item.name,
                            value: item.name,
                            id: item.id_product
                        };
                    }));
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            // Add product to the list
            addProductToList(ui.item.id, ui.item.label);
            
            // Clear the input
            setTimeout(function() {
                $('#associated_products').val('');
            }, 100);
            
            return false;
        }
    });
}

/**
 * Add a product to the product list
 */
function addProductToList(id_product, name) {
    // Check if product already exists
    if ($('#product-' + id_product).length > 0) {
        showNotification('Product already added', 'warning');
        return;
    }
    
    // Create product element
    var productHtml = '<div id="product-' + id_product + '" class="product-item" data-id-product="' + id_product + '">' +
        '<div class="product-info">' +
        '<span class="product-name">' + name + '</span>' +
        '<button type="button" class="btn btn-default btn-sm position-product">Position</button>' +
        '<button type="button" class="btn btn-danger btn-sm remove-product">Remove</button>' +
        '</div>' +
        '<input type="hidden" name="associated_products[' + id_product + '][x]" value="50">' +
        '<input type="hidden" name="associated_products[' + id_product + '][y]" value="50">' +
        '</div>';
    
    // Add to product list
    $('#product-list').append(productHtml);
    
    // Bind events
    bindProductEvents();
}

/**
 * Bind events to product items
 */
function bindProductEvents() {
    // Remove product
    $('.remove-product').off('click').on('click', function() {
        $(this).closest('.product-item').remove();
    });
    
    // Position product
    $('.position-product').off('click').on('click', function() {
        var productItem = $(this).closest('.product-item');
        var id_product = productItem.data('id-product');
        
        // Show positioning modal
        showPositioningModal(id_product);
    });
}

/**
 * Initialize image management
 */
function initImageManagement() {
    // Set main image
    $('.set-main-image').off('click').on('click', function() {
        var id_image = $(this).data('id-image');
        $('#main_image_id').val(id_image);
        
        // Update UI
        $('.image-item').removeClass('main-image');
        $(this).closest('.image-item').addClass('main-image');
    });
    
    // Delete image
    $('.delete-image').off('click').on('click', function() {
        var id_image = $(this).data('id-image');
        var imageItem = $(this).closest('.image-item');
        
        if (confirm('Are you sure you want to delete this image?')) {
            $.ajax({
                url: inspiration_ajax_url,
                type: 'POST',
                dataType: 'json',
                data: {
                    ajax: 1,
                    action: 'DeleteImage',
                    id_inspiration_image: id_image
                },
                success: function(response) {
                    if (response.success) {
                        imageItem.remove();
                        showNotification('Image deleted successfully', 'success');
                    } else {
                        showNotification('Failed to delete image', 'error');
                    }
                },
                error: function() {
                    showNotification('Failed to delete image', 'error');
                }
            });
        }
    });
}

/**
 * Initialize product positioning
 */
function initProductPositioning() {
    // Initialize positioning modal
    if ($('#positioning-modal').length === 0) {
        $('body').append(
            '<div id="positioning-modal" class="modal fade" tabindex="-1" role="dialog">' +
            '<div class="modal-dialog modal-lg" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>' +
            '<h4 class="modal-title">Position Product</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<div class="image-container position-relative">' +
            '<img src="" id="positioning-image" class="img-responsive">' +
            '<div id="product-marker" class="product-marker">+</div>' +
            '</div>' +
            '<div class="image-selector mt-3">' +
            '<label>Select Image:</label>' +
            '<select id="image-selector" class="form-control"></select>' +
            '</div>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>' +
            '<button type="button" class="btn btn-primary" id="save-position">Save Position</button>' +
            '</div>' +
            '</div>' +
            '</div>' +
            '</div>'
        );
    }
}

/**
 * Show positioning modal for a product
 */
function showPositioningModal(id_product) {
    var modal = $('#positioning-modal');
    var imageSelector = $('#image-selector');
    var currentProduct = $('#product-' + id_product);
    var posX = currentProduct.find('input[name="associated_products[' + id_product + '][x]"]').val();
    var posY = currentProduct.find('input[name="associated_products[' + id_product + '][y]"]').val();
    
    // Clear image selector
    imageSelector.empty();
    
    // Load images
    $.ajax({
        url: inspiration_ajax_url,
        type: 'GET',
        dataType: 'json',
        data: {
            ajax: 1,
            action: 'GetInspirationImages',
            id_inspiration: inspiration_id
        },
        success: function(images) {
            if (images && images.length > 0) {
                // Add images to selector
                $.each(images, function(index, image) {
                    imageSelector.append('<option value="' + image.id_inspiration_image + '" data-url="' + inspiration_img_dir + image.image + '">Image ' + (index + 1) + (image.main ? ' (Main)' : '') + '</option>');
                });
                
                // Set first image
                updatePositioningImage();
                
                // Set initial marker position
                updateMarkerPosition(posX, posY);
                
                // Show modal
                modal.modal('show');
                
                // Bind events
                bindPositioningEvents(id_product);
            } else {
                showNotification('No images available. Please upload at least one image.', 'warning');
            }
        },
        error: function() {
            showNotification('Failed to load images', 'error');
        }
    });
}

/**
 * Update the positioning image
 */
function updatePositioningImage() {
    var selectedOption = $('#image-selector option:selected');
    var imageUrl = selectedOption.data('url');
    $('#positioning-image').attr('src', imageUrl);
}

/**
 * Update marker position
 */
function updateMarkerPosition(x, y) {
    var marker = $('#product-marker');
    var container = $('.image-container');
    var image = $('#positioning-image');
    
    // Wait for image to load
    image.on('load', function() {
        var imgWidth = image.width();
        var imgHeight = image.height();
        
        // Calculate position
        var posX = (x / 100) * imgWidth;
        var posY = (y / 100) * imgHeight;
        
        // Update marker position
        marker.css({
            left: posX + 'px',
            top: posY + 'px'
        });
    });
    
    // If image is already loaded
    if (image[0].complete) {
        image.trigger('load');
    }
}

/**
 * Bind events for positioning
 */
function bindPositioningEvents(id_product) {
    // Image selector change
    $('#image-selector').off('change').on('change', function() {
        updatePositioningImage();
    });
    
    // Click on image to position marker
    $('#positioning-image').off('click').on('click', function(e) {
        var container = $('.image-container');
        var marker = $('#product-marker');
        var imgOffset = $(this).offset();
        var imgWidth = $(this).width();
        var imgHeight = $(this).height();
        
        // Calculate position
        var posX = e.pageX - imgOffset.left;
        var posY = e.pageY - imgOffset.top;
        
        // Update marker position
        marker.css({
            left: posX + 'px',
            top: posY + 'px'
        });
    });
    
    // Save position
    $('#save-position').off('click').on('click', function() {
        var marker = $('#product-marker');
        var image = $('#positioning-image');
        var imgWidth = image.width();
        var imgHeight = image.height();
        
        // Get marker position
        var posX = parseInt(marker.css('left'));
        var posY = parseInt(marker.css('top'));
        
        // Calculate percentage
        var percentX = Math.round((posX / imgWidth) * 100);
        var percentY = Math.round((posY / imgHeight) * 100);
        
        // Update hidden inputs
        $('#product-' + id_product).find('input[name="associated_products[' + id_product + '][x]"]').val(percentX);
        $('#product-' + id_product).find('input[name="associated_products[' + id_product + '][y]"]').val(percentY);
        
        // Close modal
        $('#positioning-modal').modal('hide');
        
        showNotification('Position saved', 'success');
    });
}

/**
 * Show notification
 */
function showNotification(message, type) {
    $.growl({
        message: message
    }, {
        type: type,
        allow_dismiss: true,
        placement: {
            from: 'top',
            align: 'right'
        },
        delay: 4000,
        animate: {
            enter: 'animated fadeInDown',
            exit: 'animated fadeOutUp'
        }
    });
}
