blockwishlist_configuration:
  path: blockwishlist/configuration
  methods: [GET, POST]
  defaults:
    _controller: PrestaShop\Module\BlockWishList\Controller\WishlistConfigurationAdminController::configurationAction
    _legacy_controller: 'WishlistConfigurationAdminController'
    _legacy_link: 'WishlistConfigurationAdminController'

blockwishlist_statistics:
  path: blockwishlist/statistics
  methods: [GET]
  defaults:
    _controller: PrestaShop\Module\BlockWishList\Controller\WishlistConfigurationAdminController::statisticsAction
    _legacy_controller: 'WishlistStatisticsAdminController'
    _legacy_link: 'WishlistStatisticsAdminController'

blockwishlist_statistics_reset:
  path: blockwishlist/statistics/reset
  methods: [POST]
  defaults:
    _controller: PrestaShop\Module\BlockWishList\Controller\WishlistConfigurationAdminController::resetStatisticsCacheAction
