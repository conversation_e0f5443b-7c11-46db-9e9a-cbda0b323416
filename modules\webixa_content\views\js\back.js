$(document).ready(function(){
  $('#image-images-thumbnails a.btn, #image_mobile-images-thumbnails a.btn').on('click', function(e){
      e.preventDefault();
      var wrapper = $(this).closest('div.form-group');
      $.post($(this).attr('href'), {}, function(result){
        console.log(result);
        console.log(result.success);
          if(result.success){
              wrapper.remove();
          }else{
              alert(result.msg);
          }
      });
  });
});