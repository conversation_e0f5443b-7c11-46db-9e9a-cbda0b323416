
<div class="panel">
    <div class="panel-heading">
        <i class="icon-shield"></i>
        {l s='Block Bot Management' mod='blockbot'}
    </div>
    
    <!-- Navigation Tabs -->
    <div class="nav-tabs-container" style="margin-bottom: 20px;">
        <ul class="nav nav-tabs" role="tablist">
            <li style="margin-right: 5px;">
                <a href="{$admin_url}&tab=ip_stats" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-list"></i>
                    {l s='IP Statistics' mod='blockbot'}
                </a>
            </li>
            <li style="margin-right: 5px;">
                <a href="{$admin_url}&tab=useragent_stats" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-user"></i>
                    {l s='User Agent Statistics' mod='blockbot'}
                </a>
            </li>
            <li class="active">
                <a href="{$admin_url}&tab=settings" class="btn btn-default" style="border-radius: 4px;">
                    <i class="icon-cogs"></i>
                    {l s='Settings' mod='blockbot'}
                </a>
            </li>
        </ul>
    </div>

    <div class="panel-body">
        <h3>{l s='Block Bot Settings' mod='blockbot'}</h3>
        
        <form method="post" action="{$admin_url}&tab=settings" class="form-horizontal">
            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Enable or disable the BlockBot module functionality' mod='blockbot'}">
                        {l s='Enable BlockBot' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" name="enabled" id="enabled_on" value="1" {if $enabled}checked="checked"{/if}>
                        <label for="enabled_on">{l s='Yes' mod='blockbot'}</label>
                        <input type="radio" name="enabled" id="enabled_off" value="0" {if !$enabled}checked="checked"{/if}>
                        <label for="enabled_off">{l s='No' mod='blockbot'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">
                        {l s='When enabled, the module will log all visits and can automatically block suspicious traffic.' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Enable automatic blocking when thresholds are exceeded' mod='blockbot'}">
                        {l s='Automatic Blocking' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" name="auto_block" id="auto_block_on" value="1" {if $auto_block}checked="checked"{/if}>
                        <label for="auto_block_on">{l s='Yes' mod='blockbot'}</label>
                        <input type="radio" name="auto_block" id="auto_block_off" value="0" {if !$auto_block}checked="checked"{/if}>
                        <label for="auto_block_off">{l s='No' mod='blockbot'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">
                        {l s='When enabled, IPs and User Agents exceeding thresholds will be automatically blocked.' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Number of visits from the same IP address that triggers automatic blocking alert' mod='blockbot'}">
                        {l s='IP Blocking Threshold' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <div class="input-group">
                        <input type="number" name="threshold_ip" value="{$threshold_ip}" class="form-control" min="1" max="1000">
                        <span class="input-group-addon">{l s='visits' mod='blockbot'}</span>
                    </div>
                    <p class="help-block">
                        {l s='Number of page loads from the same IP within 24 hours to trigger automatic blocking. Default: 200 (≈8 visits/hour)' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Number of visits from the same User Agent that triggers automatic blocking alert' mod='blockbot'}">
                        {l s='User Agent Blocking Threshold' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <div class="input-group">
                        <input type="number" name="threshold_useragent" value="{$threshold_useragent}" class="form-control" min="1" max="1000">
                        <span class="input-group-addon">{l s='visits' mod='blockbot'}</span>
                    </div>
                    <p class="help-block">
                        {l s='Number of page loads from the same User Agent within 24 hours to trigger automatic blocking. Default: 200 (same as IP)' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Number of rapid requests from same IP to trigger immediate blocking' mod='blockbot'}">
                        {l s='Rapid-Fire IP Threshold' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <div class="input-group">
                        <input type="number" name="rapid_fire_ip" value="{$rapid_fire_ip}" class="form-control" min="1" max="100">
                        <span class="input-group-addon">{l s='requests/min' mod='blockbot'}</span>
                    </div>
                    <p class="help-block">
                        {l s='Number of requests from the same IP within 1 minute to trigger immediate blocking. Default: 30 (anti-DDoS)' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Number of rapid requests from same User Agent to trigger immediate blocking' mod='blockbot'}">
                        {l s='Rapid-Fire User Agent Threshold' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <div class="input-group">
                        <input type="number" name="rapid_fire_agent" value="{$rapid_fire_agent}" class="form-control" min="1" max="100">
                        <span class="input-group-addon">{l s='requests/min' mod='blockbot'}</span>
                    </div>
                    <p class="help-block">
                        {l s='Number of requests from the same User Agent within 1 minute to trigger immediate blocking. Default: 30 (same as IP)' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Block requests with empty User Agent strings' mod='blockbot'}">
                        {l s='Block Empty User Agents' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" name="block_empty_agent" id="block_empty_agent_on" value="1" {if $block_empty_agent}checked="checked"{/if}>
                        <label for="block_empty_agent_on">{l s='Yes' mod='blockbot'}</label>
                        <input type="radio" name="block_empty_agent" id="block_empty_agent_off" value="0" {if !$block_empty_agent}checked="checked"{/if}>
                        <label for="block_empty_agent_off">{l s='No' mod='blockbot'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">
                        {l s='When enabled, requests with empty User Agent will be blocked with "bad request" message.' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Number of months after which old logs will be automatically deleted' mod='blockbot'}">
                        {l s='Auto-cleanup Period' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <div class="input-group">
                        <input type="number" name="cleanup_months" value="{$cleanup_months}" class="form-control" min="1" max="12">
                        <span class="input-group-addon">{l s='months' mod='blockbot'}</span>
                    </div>
                    <p class="help-block">
                        {l s='Logs older than this period will be automatically deleted by cron job. Default: 3 months' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Enable email notifications for suspicious traffic' mod='blockbot'}">
                        {l s='Email Notifications' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" name="email_notifications" id="email_notifications_on" value="1" {if $email_notifications}checked="checked"{/if}>
                        <label for="email_notifications_on">{l s='Yes' mod='blockbot'}</label>
                        <input type="radio" name="email_notifications" id="email_notifications_off" value="0" {if !$email_notifications}checked="checked"{/if}>
                        <label for="email_notifications_off">{l s='No' mod='blockbot'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">
                        {l s='When enabled, email alerts will be sent when suspicious traffic is detected.' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-lg-3">
                    <span class="label-tooltip" data-toggle="tooltip" title="{l s='Email addresses to receive notifications (comma separated)' mod='blockbot'}">
                        {l s='Notification Email Addresses' mod='blockbot'}
                    </span>
                </label>
                <div class="col-lg-9">
                    <textarea name="email_addresses" class="form-control" rows="3" placeholder="<EMAIL>, <EMAIL>">{$email_addresses}</textarea>
                    <p class="help-block">
                        {l s='Enter email addresses separated by commas. Default: shop admin email.' mod='blockbot'}
                    </p>
                </div>
            </div>

            <div class="panel-footer">
                <button type="submit" name="saveSettings" class="btn btn-default pull-right">
                    <i class="process-icon-save"></i>
                    {l s='Save Settings' mod='blockbot'}
                </button>
            </div>
        </form>

        <hr>

        {if $suspicious_alerts}
        <div class="alert alert-warning">
            <h4><i class="icon-warning"></i> {l s='Suspicious Activity Detected' mod='blockbot'}</h4>
            <p>{l s='The following entries have exceeded the configured thresholds:' mod='blockbot'}</p>
            <ul>
                {foreach $suspicious_alerts as $alert}
                <li>
                    {if $alert.type == 'ip'}
                        <strong>{l s='IP:' mod='blockbot'}</strong> {$alert.value}
                        ({$alert.count} {l s='visits, threshold:' mod='blockbot'} {$alert.threshold})
                    {else}
                        <strong>{l s='User Agent:' mod='blockbot'}</strong> {$alert.value|truncate:80:"..."}
                        ({$alert.count} {l s='visits, threshold:' mod='blockbot'} {$alert.threshold})
                    {/if}
                </li>
                {/foreach}
            </ul>
            <form method="post" action="{$admin_url}&tab=settings" style="margin-top: 15px;">
                <button type="submit" name="autoBlockSuspicious" class="btn btn-warning"
                        onclick="return confirm('{l s='Are you sure you want to automatically block all suspicious entries?' mod='blockbot'}');">
                    <i class="icon-ban-circle"></i>
                    {l s='Block All Suspicious Entries' mod='blockbot'}
                </button>
            </form>
        </div>
        {/if}

        <h3>{l s='Log Management' mod='blockbot'}</h3>

        <div class="row" style="margin-bottom: 20px;">
            <div class="col-md-6">
                <form method="post" action="{$admin_url}&tab=settings" style="display: inline-block; margin-right: 10px;">
                    <button type="submit" name="cleanOldLogs" class="btn btn-warning"
                            onclick="return confirm('{l s='Are you sure you want to clean old logs?' mod='blockbot'}');">
                        <i class="icon-trash"></i>
                        {l s='Clean Old Logs Now' mod='blockbot'}
                    </button>
                </form>
                {if $last_cleanup}
                    <small class="text-muted">{l s='Last cleanup:' mod='blockbot'} {$last_cleanup}</small>
                {else}
                    <small class="text-muted">{l s='Never cleaned' mod='blockbot'}</small>
                {/if}
            </div>
            <div class="col-md-6">
                <form method="post" action="{$admin_url}&tab=settings" style="display: inline-block;">
                    <button type="submit" name="generateCronKey" class="btn btn-info">
                        <i class="icon-key"></i>
                        {l s='Generate New Cron Key' mod='blockbot'}
                    </button>
                </form>
            </div>
        </div>

        <h3>{l s='Automatic Cleanup (Cron Job)' mod='blockbot'}</h3>

        <div class="alert alert-info">
            <h4><i class="icon-info-circle"></i> {l s='Cron Job Setup' mod='blockbot'}</h4>
            <p>{l s='To automatically clean old logs, set up a cron job on your server:' mod='blockbot'}</p>

            <h5>{l s='Command Line (recommended):' mod='blockbot'}</h5>
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0;">0 2 * * * /usr/bin/php {$smarty.server.DOCUMENT_ROOT}/modules/blockbot/cron/cleanup.php</pre>

            <h5>{l s='Web-based (alternative):' mod='blockbot'}</h5>
            {if $cron_key}
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0;">0 2 * * * /usr/bin/wget -q -O - "{$smarty.server.HTTP_HOST}/modules/blockbot/cron/cleanup.php?key={$cron_key}"</pre>
            {else}
            <p class="text-warning">{l s='Please generate a cron key first using the button above.' mod='blockbot'}</p>
            {/if}

            <small>
                <strong>{l s='Explanation:' mod='blockbot'}</strong><br>
                - <code>0 2 * * *</code> = {l s='Run daily at 2:00 AM' mod='blockbot'}<br>
                - {l s='Adjust the path to match your server configuration' mod='blockbot'}<br>
                - {l s='The cron job will delete logs older than the configured period' mod='blockbot'}
            </small>
        </div>

        <h3>{l s='Monitoring Information' mod='blockbot'}</h3>
        
        <div class="alert alert-info">
            <h4><i class="icon-info-circle"></i> {l s='How BlockBot Works' mod='blockbot'}</h4>
            <ul>
                <li>{l s='The module logs all visits to your store including IP address, User Agent, referrer and timestamp.' mod='blockbot'}</li>
                <li>{l s='Visits are analyzed and grouped by IP address and User Agent to identify suspicious patterns.' mod='blockbot'}</li>
                <li>{l s='When you block an IP or User Agent, rules are added to .htaccess and robots.txt files.' mod='blockbot'}</li>
                <li>{l s='Backup copies of .htaccess and robots.txt are created before any modifications.' mod='blockbot'}</li>
                <li>{l s='You can unblock IPs and User Agents at any time from the respective tabs.' mod='blockbot'}</li>
            </ul>
        </div>

        <div class="alert alert-warning">
            <h4><i class="icon-warning"></i> {l s='Important Notes' mod='blockbot'}</h4>
            <ul>
                <li>{l s='Blocking legitimate search engine bots may negatively impact your SEO.' mod='blockbot'}</li>
                <li>{l s='Always review the User Agent before blocking to avoid blocking legitimate visitors.' mod='blockbot'}</li>
                <li>{l s='Logs are stored in database tables and cleaned automatically via cron job.' mod='blockbot'}</li>
                <li>{l s='Blocked entries are stored in database and added to both .htaccess (server-level blocking) and robots.txt (bot guidance).' mod='blockbot'}</li>
                <li>{l s='Set up the cron job to automatically clean old logs and maintain database performance.' mod='blockbot'}</li>
            </ul>
        </div>

        <h3>{l s='Database Management' mod='blockbot'}</h3>
        
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">{l s='Database Statistics' mod='blockbot'}</h4>
                    </div>
                    <div class="panel-body">
                        <table class="table table-condensed">
                            <tr>
                                <td>{l s='Total logs:' mod='blockbot'}</td>
                                <td><strong>{$log_stats.total_logs|number_format}</strong></td>
                            </tr>
                            <tr>
                                <td>{l s='Unique IPs:' mod='blockbot'}</td>
                                <td><strong>{$log_stats.unique_ips|number_format}</strong></td>
                            </tr>
                            <tr>
                                <td>{l s='Unique User Agents:' mod='blockbot'}</td>
                                <td><strong>{$log_stats.unique_agents|number_format}</strong></td>
                            </tr>
                            {if $log_stats.first_log}
                            <tr>
                                <td>{l s='First log:' mod='blockbot'}</td>
                                <td><strong>{$log_stats.first_log}</strong></td>
                            </tr>
                            <tr>
                                <td>{l s='Last log:' mod='blockbot'}</td>
                                <td><strong>{$log_stats.last_log}</strong></td>
                            </tr>
                            {/if}
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">{l s='Database Tables' mod='blockbot'}</h4>
                    </div>
                    <div class="panel-body">
                        <p>{l s='BlockBot uses the following database tables:' mod='blockbot'}</p>
                        <ul style="font-size: 12px;">
                            <li><code>ps_blockbot_logs</code> - {l s='Stores all visit logs' mod='blockbot'}</li>
                            <li><code>ps_blockbot_blocked</code> - {l s='Stores blocked IPs and User Agents' mod='blockbot'}</li>
                        </ul>
                        <small>
                            {l s='Backup files for .htaccess and robots.txt are still created automatically.' mod='blockbot'}<br>
                            {l s='Format:' mod='blockbot'} .htaccess.blockbot.backup.YYYY-MM-DD-HH-MM-SS
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
