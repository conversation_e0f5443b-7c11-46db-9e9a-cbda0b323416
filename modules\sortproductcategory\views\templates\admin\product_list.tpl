<link rel="stylesheet" type="text/css" href="{$module_url}views/css/admin.css">
<script src="{$module_url}views/js/admin.js"></script>

<div class="panel">
    <div class="panel-heading">
        <i class="icon-list"></i>
        {l s='Zarządzanie kolejnością produktów' mod='sortproductcategory'} - {$category->name}
        <div class="panel-heading-action">
            <a href="{$admin_url}&tab=category_select" class="btn btn-default btn-sm">
                <i class="icon-arrow-left"></i>
                {l s='Powrót do wyboru kategorii' mod='sortproductcategory'}
            </a>
        </div>
    </div>
    
    <div class="panel-body">
        <!-- Search and filters -->
        <div class="row">
            <div class="col-lg-3">
                <div class="form-group">
                    <label>{l s='Wyszukaj produkty:' mod='sortproductcategory'}</label>
                    <input type="text" id="search-input" class="form-control" value="{$search}" placeholder="{l s='Nazwa lub kod produktu...' mod='sortproductcategory'}">
                </div>
            </div>
            <div class="col-lg-2">
                <div class="form-group">
                    <label>{l s='Sortuj według:' mod='sortproductcategory'}</label>
                    <select id="order-by" class="form-control" style="width: 120px;">
                        <option value="position" {if $order_by == 'position'}selected{/if}>{l s='Pozycja' mod='sortproductcategory'}</option>
                        <option value="name" {if $order_by == 'name'}selected{/if}>{l s='Nazwa' mod='sortproductcategory'}</option>
                        <option value="reference" {if $order_by == 'reference'}selected{/if}>{l s='Kod' mod='sortproductcategory'}</option>
                        <option value="price" {if $order_by == 'price'}selected{/if}>{l s='Cena' mod='sortproductcategory'}</option>
                    </select>
                </div>
            </div>
            <div class="col-lg-1">
                <div class="form-group">
                    <label>{l s='Kierunek:' mod='sortproductcategory'}</label>
                    <select id="order-way" class="form-control" style="width: 100px;">
                        <option value="ASC" {if $order_way == 'ASC'}selected{/if}>{l s='Rosnąco' mod='sortproductcategory'}</option>
                        <option value="DESC" {if $order_way == 'DESC'}selected{/if}>{l s='Malejąco' mod='sortproductcategory'}</option>
                    </select>
                </div>
            </div>
            <div class="col-lg-1">
                <div class="form-group">
                    <label>{l s='Na stronie:' mod='sortproductcategory'}</label>
                    <select id="items-per-page" class="form-control" style="width: 80px;">
                        <option value="25" {if $limit == 25}selected{/if}>25</option>
                        <option value="50" {if $limit == 50}selected{/if}>50</option>
                        <option value="100" {if $limit == 100}selected{/if}>100</option>
                        <option value="200" {if $limit == 200}selected{/if}>200</option>
                        <option value="500" {if $limit == 500}selected{/if}>500</option>
                        <option value="1000" {if $limit == 1000}selected{/if}>1000</option>
                        <option value="1500" {if $limit == 1500}selected{/if}>1500</option>
                    </select>
                </div>
            </div>
            <div class="col-lg-5">
                <div class="form-group">
                    <label style="visibility: hidden;">Akcje</label>
                    <div>
                        <button type="button" class="btn btn-primary unified-btn" onclick="searchProducts()">
                            <i class="icon-search"></i>
                            {l s='Szukaj' mod='sortproductcategory'}
                        </button>
                        <button type="button" class="btn btn-warning unified-btn" onclick="cleanPositions()" style="margin-left: 5px;">
                            <i class="icon-refresh"></i>
                            {l s='Uporządkuj pozycje' mod='sortproductcategory'}
                        </button>
                        <button type="button" class="btn btn-info unified-btn" onclick="toggleAdvancedSearch()" style="margin-left: 5px;">
                            <i class="icon-filter"></i>
                            {l s='Filtry zaawansowane' mod='sortproductcategory'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Save button at top -->
        <div class="row" style="margin-bottom: 15px;">
            <div class="col-lg-12 text-center">
                <button type="button" id="save-changes-btn-top" class="btn btn-success btn-lg save-changes-btn" style="display: none;" onclick="saveAllChanges()">
                    <i class="icon-save"></i>
                    <span id="save-btn-text-top">Zapisz zmiany</span>
                </button>
            </div>
        </div>

        <!-- Advanced search panel -->
        <div class="row" id="advanced-search-panel" style="display: none; margin-top: 15px;">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>{l s='Zaawansowane wyszukiwanie' mod='sortproductcategory'}</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>{l s='Tylko aktywne produkty:' mod='sortproductcategory'}</label>
                                    <input type="checkbox" class="advanced-filter" name="active_only">
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>{l s='Cena od:' mod='sortproductcategory'}</label>
                                    <input type="number" class="form-control advanced-filter" name="min_price" step="0.01">
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>{l s='Cena do:' mod='sortproductcategory'}</label>
                                    <input type="number" class="form-control advanced-filter" name="max_price" step="0.01">
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>{l s='Min. ilość:' mod='sortproductcategory'}</label>
                                    <input type="number" class="form-control advanced-filter" name="min_quantity">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <button type="button" class="btn btn-primary unified-btn" onclick="applyAdvancedFilters()">
                                    <i class="icon-check"></i>
                                    {l s='Zastosuj filtry' mod='sortproductcategory'}
                                </button>
                                <button type="button" class="btn btn-default unified-btn" onclick="clearAdvancedFilters()" style="margin-left: 5px;">
                                    <i class="icon-eraser"></i>
                                    {l s='Wyczyść' mod='sortproductcategory'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-info">
                    <strong>{l s='Instrukcje:' mod='sortproductcategory'}</strong>
                    <ul>
                        <li>{l s='Wpisz nową pozycję w polu i zmiany będą śledzone' mod='sortproductcategory'}</li>
                        <li>{l s='Użyj przycisku "Zapisz zmiany" aby zapisać wszystkie zmiany jednocześnie' mod='sortproductcategory'}</li>
                        <li>{l s='Maksymalnie 100 zmian na raz - zapisz przed dodaniem kolejnych' mod='sortproductcategory'}</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Products table -->
        <div class="table-responsive">
            <div class="row" style="margin-bottom: 10px;">
                <div class="col-lg-6">
                    <h4>{l s='Lista produktów' mod='sortproductcategory'} ({$total_products})</h4>
                </div>
                <div class="col-lg-6 text-right">
                    <span id="changes-counter" style="display: none;" class="label label-warning">
                        <i class="icon-edit"></i> <span id="changes-count">0</span> {l s='zmian oczekuje' mod='sortproductcategory'}
                    </span>
                </div>
            </div>
            <table class="table table-striped" id="products-table">
                <thead>
                    <tr>
                        <th width="30">
                            <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                        </th>
                        <th width="80">{l s='Pozycja' mod='sortproductcategory'}</th>
                        <th width="80">{l s='ID' mod='sortproductcategory'}</th>
                        <th>{l s='Nazwa produktu' mod='sortproductcategory'}</th>
                        <th width="120">{l s='Kod' mod='sortproductcategory'}</th>
                        <th width="80">{l s='Cena' mod='sortproductcategory'}</th>
                        <th width="60">{l s='Status' mod='sortproductcategory'}</th>
                        <th width="30">{l s='Akcje' mod='sortproductcategory'}</th>
                    </tr>
                </thead>
                <tbody id="sortable-products">
                    {foreach $products as $index => $product}
                        <tr data-product-id="{$product.id_product}" data-position="{$product.position}">
                            <td>
                                <input type="checkbox" class="product-checkbox" value="{$product.id_product}">
                            </td>
                            <td class="position-cell">
                                <input type="number" class="form-control input-sm new-position"
                                       value="{$product.position + 1}"
                                       min="1"
                                       max="{$total_products}"
                                       tabindex="{$index + 1}"
                                       onchange="updateSinglePositionDebounced({$product.id_product}, this.value - 1)">
                            </td>
                            <td>{$product.id_product}</td>
                            <td>
                                <strong>{$product.name}</strong>
                            </td>
                            <td>{$product.reference}</td>
                            <td>{$product.price|string_format:"%.2f"} zł</td>
                            <td>
                                {if $product.active}
                                    <span class="label label-success">{l s='Aktywny' mod='sortproductcategory'}</span>
                                {else}
                                    <span class="label label-danger">{l s='Nieaktywny' mod='sortproductcategory'}</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="btn btn-xs btn-primary"
                                        onclick="updateSinglePosition({$product.id_product}, document.querySelector('tr[data-product-id=\'{$product.id_product}\'] .new-position').value - 1)"
                                        title="{l s='Aktualizuj pozycję' mod='sortproductcategory'}">
                                    <i class="icon-check"></i>
                                </button>
                            </td>
                        </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {if $total_pages > 1}
            <div class="row">
                <div class="col-lg-12">
                    <nav aria-label="Paginacja produktów">
                        <ul class="pagination">
                            {if isset($pagination.prev)}
                                <li><a href="{$pagination.prev}">&laquo; {l s='Poprzednia' mod='sortproductcategory'}</a></li>
                            {/if}
                            
                            {if isset($pagination.pages)}
                                {foreach $pagination.pages as $page_info}
                                    <li {if $page_info.current}class="active"{/if}>
                                        <a href="{$page_info.url}">{$page_info.page}</a>
                                    </li>
                                {/foreach}
                            {/if}
                            
                            {if isset($pagination.next)}
                                <li><a href="{$pagination.next}">{l s='Następna' mod='sortproductcategory'} &raquo;</a></li>
                            {/if}
                        </ul>
                    </nav>
                </div>
            </div>
        {/if}
        
        <!-- Bulk operations -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>{l s='Operacje grupowe' mod='sortproductcategory'}</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-3">
                                <input type="number" id="bulk-start-position" class="form-control" placeholder="{l s='Pozycja początkowa' mod='sortproductcategory'}" min="1">
                            </div>
                            <div class="col-lg-3">
                                <button type="button" class="btn btn-success unified-btn" onclick="bulkMoveToPosition()">
                                    <i class="icon-move"></i>
                                    {l s='Przenieś zaznaczone' mod='sortproductcategory'}
                                </button>
                            </div>
                            <div class="col-lg-6">
                                <span class="help-block">
                                    {l s='Zaznacz produkty i podaj pozycję początkową, aby przenieść je grupowo' mod='sortproductcategory'}<br>
                                    <small>{l s='Po operacji grupowej pozycje zostaną automatycznie uporządkowane' mod='sortproductcategory'}</small>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
var currentCategoryId = {$id_category};
var adminUrl = '{$admin_url}';
var adminToken = '{$admin_token}';

// Debounce function for position updates
var updateTimeouts = {};
var activeRequests = {};
var isReloading = false;
var pendingChanges = {};
var maxChanges = 100;

function updateSinglePositionDebounced(productId, newPosition) {
    // Clear existing timeout for this product
    if (updateTimeouts[productId]) {
        clearTimeout(updateTimeouts[productId]);
    }

    // Set new timeout to track the change
    updateTimeouts[productId] = setTimeout(function() {
        trackPositionChange(productId, newPosition);
        delete updateTimeouts[productId];
    }, 300); // Wait 300ms after user stops typing
}

function trackPositionChange(productId, newPosition) {
    if (newPosition < 0) {
        alert('{l s='Pozycja nie może być mniejsza niż 0' mod='sortproductcategory'}');
        return;
    }

    // Check if we're at the limit
    if (Object.keys(pendingChanges).length >= maxChanges && !pendingChanges[productId]) {
        alert('Maksymalna liczba zmian to ' + maxChanges + '. Zapisz obecne zmiany przed dodaniem kolejnych.');
        return;
    }

    // Track the change
    pendingChanges[productId] = newPosition;

    // Update visual feedback
    var input = document.querySelector('tr[data-product-id="' + productId + '"] .new-position');
    if (input) {
        input.style.backgroundColor = '#fff3cd'; // Yellow - pending change
        input.setAttribute('data-changed', 'true');
    }

    // Update save button
    updateSaveButton();
}

function updateSaveButton() {
    var saveBtn = document.getElementById('save-changes-btn');
    var saveBtnTop = document.getElementById('save-changes-btn-top');
    var saveBtnTextTop = document.getElementById('save-btn-text-top');
    var changesCount = Object.keys(pendingChanges).length;
    var changesCounter = document.getElementById('changes-counter');
    var changesCountSpan = document.getElementById('changes-count');

    // Update counter in table header
    if (changesCounter && changesCountSpan) {
        if (changesCount > 0) {
            changesCountSpan.textContent = changesCount;
            changesCounter.style.display = 'inline';
        } else {
            changesCounter.style.display = 'none';
        }
    }

    // Update top button
    if (saveBtnTop && saveBtnTextTop) {
        if (changesCount > 0) {
            saveBtnTextTop.textContent = 'Zapisz zmiany (' + changesCount + '/' + maxChanges + ')';
            saveBtnTop.style.display = 'inline-block';

            // Change color based on number of changes
            if (changesCount >= maxChanges * 0.8) {
                saveBtnTop.className = 'btn btn-warning btn-lg save-changes-btn pulse-animation'; // Orange when near limit with pulse
            } else {
                saveBtnTop.className = 'btn btn-success btn-lg save-changes-btn'; // Green normally
            }
        } else {
            saveBtnTop.style.display = 'none';
        }
    }

    // Create floating save button if it doesn't exist
    if (!saveBtn) {
        saveBtn = document.createElement('button');
        saveBtn.id = 'save-changes-btn';
        saveBtn.type = 'button';
        saveBtn.className = 'btn btn-success btn-lg save-changes-btn save-changes-btn-floating';
        saveBtn.style.position = 'fixed';
        saveBtn.style.bottom = '20px';
        saveBtn.style.right = '20px';
        saveBtn.style.zIndex = '9999';
        saveBtn.style.display = 'none';
        saveBtn.onclick = saveAllChanges;
        document.body.appendChild(saveBtn);
    }

    // Update floating button
    if (changesCount > 0) {
        saveBtn.innerHTML = '<i class="icon-save"></i> Zapisz zmiany (' + changesCount + '/' + maxChanges + ')';
        saveBtn.style.display = 'block';

        // Change color based on number of changes
        if (changesCount >= maxChanges * 0.8) {
            saveBtn.className = 'btn btn-warning btn-lg save-changes-btn save-changes-btn-floating'; // Orange when near limit
        } else {
            saveBtn.className = 'btn btn-success btn-lg save-changes-btn save-changes-btn-floating'; // Green normally
        }
    } else {
        saveBtn.style.display = 'none';
    }
}

function saveAllChanges() {
    if (Object.keys(pendingChanges).length === 0) {
        return;
    }

    // Prevent multiple saves
    if (isReloading) {
        return;
    }

    var saveBtn = document.getElementById('save-changes-btn');
    var saveBtnTop = document.getElementById('save-changes-btn-top');
    var saveBtnTextTop = document.getElementById('save-btn-text-top');

    // Disable both buttons
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="icon-refresh"></i> Zapisywanie...';
    }
    if (saveBtnTop) {
        saveBtnTop.disabled = true;
        if (saveBtnTextTop) {
            saveBtnTextTop.textContent = 'Zapisywanie...';
        }
    }

    // Use bulk update for all pending changes
    fetch(adminUrl + '&ajax=1&action=bulkUpdatePositions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'id_category=' + currentCategoryId + '&positions=' + encodeURIComponent(JSON.stringify(pendingChanges)) + '&token=' + adminToken
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Clear pending changes
            pendingChanges = {};

            // Update all changed inputs to success color and keep them visible
            var changedInputs = document.querySelectorAll('.new-position[data-changed="true"]');
            changedInputs.forEach(function(input) {
                input.style.backgroundColor = '#d4edda'; // Success color - keep visible
                input.setAttribute('data-saved', 'true'); // Mark as saved but keep color
                input.removeAttribute('data-changed');
            });

            // Hide save button
            updateSaveButton();

            // Show success message
            showMessage('Wszystkie zmiany zostały zapisane!', 'success');

            // Reload after delay to show updated positions
            setTimeout(function() {
                if (!isReloading) {
                    isReloading = true;
                    location.reload();
                }
            }, 1500);
        } else {
            // Re-enable buttons on error
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="icon-save"></i> Zapisz zmiany (' + Object.keys(pendingChanges).length + '/' + maxChanges + ')';
            }
            if (saveBtnTop) {
                saveBtnTop.disabled = false;
                if (saveBtnTextTop) {
                    saveBtnTextTop.textContent = 'Zapisz zmiany (' + Object.keys(pendingChanges).length + '/' + maxChanges + ')';
                }
            }
            alert(data.message || 'Błąd podczas zapisywania zmian');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Re-enable buttons on error
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="icon-save"></i> Zapisz zmiany (' + Object.keys(pendingChanges).length + '/' + maxChanges + ')';
        }
        if (saveBtnTop) {
            saveBtnTop.disabled = false;
            if (saveBtnTextTop) {
                saveBtnTextTop.textContent = 'Zapisz zmiany (' + Object.keys(pendingChanges).length + '/' + maxChanges + ')';
            }
        }
        alert('Błąd podczas zapisywania zmian');
    });
}

function showMessage(message, type) {
    var messageDiv = document.getElementById('status-message');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'status-message';
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.minWidth = '300px';
        messageDiv.style.padding = '15px';
        messageDiv.style.borderRadius = '4px';
        messageDiv.style.display = 'none';
        document.body.appendChild(messageDiv);
    }

    messageDiv.className = 'alert alert-' + (type === 'success' ? 'success' : 'danger');
    messageDiv.innerHTML = '<i class="icon-' + (type === 'success' ? 'check' : 'warning') + '"></i> ' + message;
    messageDiv.style.display = 'block';

    // Auto-hide after 3 seconds
    setTimeout(function() {
        messageDiv.style.display = 'none';
    }, 3000);
}

// Search functionality
function searchProducts() {
    // Check for unsaved changes
    if (Object.keys(pendingChanges).length > 0) {
        if (!confirm('Masz niezapisane zmiany. Czy chcesz kontynuować bez zapisywania?')) {
            return;
        }
    }

    var search = document.getElementById('search-input').value;
    var orderBy = document.getElementById('order-by').value;
    var orderWay = document.getElementById('order-way').value;
    var itemsPerPage = document.getElementById('items-per-page').value;

    window.location.href = adminUrl + '&tab=product_list&id_category=' + currentCategoryId +
                          '&search=' + encodeURIComponent(search) +
                          '&order_by=' + orderBy +
                          '&order_way=' + orderWay +
                          '&limit=' + itemsPerPage;
}

function toggleAdvancedSearch() {
    var panel = document.getElementById('advanced-search-panel');
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
    } else {
        panel.style.display = 'none';
    }
}

function applyAdvancedFilters() {
    // This would be implemented to apply advanced filters
    // For now, just show a message
    showMessage('Zaawansowane filtry będą dostępne w przyszłej wersji', 'info');
}

function clearAdvancedFilters() {
    var filters = document.querySelectorAll('.advanced-filter');
    filters.forEach(function(filter) {
        if (filter.type === 'checkbox') {
            filter.checked = false;
        } else {
            filter.value = '';
        }
    });
}



// Enter key search
document.getElementById('search-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchProducts();
    }
});

// Select all functionality
function toggleSelectAll() {
    var selectAll = document.getElementById('select-all');
    var checkboxes = document.querySelectorAll('.product-checkbox');
    
    checkboxes.forEach(function(checkbox) {
        checkbox.checked = selectAll.checked;
    });
}

// Update single position
function updateSinglePosition(productId, newPosition) {
    if (newPosition < 0) {
        alert('{l s='Pozycja nie może być mniejsza niż 0' mod='sortproductcategory'}');
        return;
    }

    // Prevent multiple reloads
    if (isReloading) {
        return;
    }

    // Cancel existing request for this product
    if (activeRequests[productId]) {
        activeRequests[productId].abort();
    }

    // Create AbortController for this request
    const controller = new AbortController();
    activeRequests[productId] = controller;

    // Show loading indicator
    var input = document.querySelector('tr[data-product-id="' + productId + '"] .new-position');
    if (input) {
        input.disabled = true;
        input.style.backgroundColor = '#f0f0f0';
    }

    // AJAX call to update position
    fetch(adminUrl + '&ajax=1&action=updatePosition', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'id_product=' + productId + '&id_category=' + currentCategoryId + '&new_position=' + newPosition + '&token=' + adminToken,
        signal: controller.signal
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        delete activeRequests[productId];

        if (data.success) {
            // Update the input value to reflect the change
            if (input) {
                input.style.backgroundColor = '#d4edda'; // Success color
                input.disabled = false;

                // Reset color after 2 seconds
                setTimeout(function() {
                    input.style.backgroundColor = '';
                }, 2000);
            }

            // Reload after a short delay to show success
            setTimeout(function() {
                if (!isReloading) {
                    isReloading = true;
                    location.reload();
                }
            }, 300);
        } else {
            if (input) {
                input.style.backgroundColor = '#f8d7da'; // Error color
                input.disabled = false;

                // Reset color after 3 seconds
                setTimeout(function() {
                    input.style.backgroundColor = '';
                }, 3000);
            }
            alert(data.message || '{l s='Błąd podczas aktualizacji pozycji' mod='sortproductcategory'}');
        }
    })
    .catch(error => {
        delete activeRequests[productId];

        if (error.name === 'AbortError') {
            // Request was cancelled, this is normal
            console.log('Request cancelled for product', productId);
        } else {
            console.error('Error:', error);
            if (input) {
                input.style.backgroundColor = '#f8d7da'; // Error color
                input.disabled = false;
            }
            alert('{l s='Błąd podczas aktualizacji pozycji' mod='sortproductcategory'}');
        }
    });
}

// Bulk move to position
function bulkMoveToPosition() {
    // Prevent multiple operations
    if (isReloading) {
        return;
    }

    var selectedProducts = [];
    var checkboxes = document.querySelectorAll('.product-checkbox:checked');
    var startPosition = parseInt(document.getElementById('bulk-start-position').value);

    if (checkboxes.length === 0) {
        alert('{l s='Proszę zaznaczyć produkty do przeniesienia' mod='sortproductcategory'}');
        return;
    }

    if (isNaN(startPosition) || startPosition < 1) {
        alert('{l s='Proszę podać prawidłową pozycję początkową (min. 1)' mod='sortproductcategory'}');
        return;
    }

    // Cancel all pending individual updates
    Object.keys(activeRequests).forEach(function(productId) {
        if (activeRequests[productId]) {
            activeRequests[productId].abort();
            delete activeRequests[productId];
        }
    });

    // Clear all timeouts
    Object.keys(updateTimeouts).forEach(function(productId) {
        clearTimeout(updateTimeouts[productId]);
        delete updateTimeouts[productId];
    });

    checkboxes.forEach(function(checkbox) {
        selectedProducts.push(parseInt(checkbox.value));
    });

    // Prepare positions object
    var positions = {};
    selectedProducts.forEach(function(productId, index) {
        positions[productId] = (startPosition - 1) + index;
    });

    // Show loading state
    var bulkButton = document.querySelector('button[onclick="bulkMoveToPosition()"]');
    if (bulkButton) {
        bulkButton.disabled = true;
        bulkButton.innerHTML = '<i class="icon-refresh"></i> {l s='Przetwarzanie...' mod='sortproductcategory'}';
    }

    // Disable selected checkboxes
    checkboxes.forEach(function(checkbox) {
        checkbox.disabled = true;
    });

    // AJAX call to bulk update positions
    fetch(adminUrl + '&ajax=1&action=bulkUpdatePositions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'id_category=' + currentCategoryId + '&positions=' + encodeURIComponent(JSON.stringify(positions)) + '&token=' + adminToken
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // After bulk update, clean positions to avoid conflicts
            cleanPositions();
        } else {
            alert(data.message || '{l s='Błąd podczas aktualizacji pozycji' mod='sortproductcategory'}');
            resetLoadingStates();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{l s='Błąd podczas aktualizacji pozycji' mod='sortproductcategory'}');
        resetLoadingStates();
    });
}

// Clean positions
function cleanPositions() {
    // Prevent multiple operations
    if (isReloading) {
        return;
    }

    if (confirm('{l s='Czy na pewno chcesz uporządkować pozycje? Produkty zostaną ponumerowane od 1.' mod='sortproductcategory'}')) {
        fetch(adminUrl + '&ajax=1&action=cleanPositions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id_category=' + currentCategoryId + '&token=' + adminToken
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{l s='Błąd podczas porządkowania pozycji' mod='sortproductcategory'}');
                resetLoadingStates();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{l s='Błąd podczas porządkowania pozycji' mod='sortproductcategory'}');
            resetLoadingStates();
        });
    }
}

// Reset loading states
function resetLoadingStates() {
    isReloading = false;

    // Reset bulk button
    var bulkButton = document.querySelector('button[onclick="bulkMoveToPosition()"]');
    if (bulkButton) {
        bulkButton.disabled = false;
        bulkButton.innerHTML = '<i class="icon-move"></i> {l s='Przenieś zaznaczone' mod='sortproductcategory'}';
    }

    // Reset checkboxes
    var checkboxes = document.querySelectorAll('.product-checkbox');
    checkboxes.forEach(function(checkbox) {
        checkbox.disabled = false;
    });

    // Reset input fields
    var inputs = document.querySelectorAll('.new-position');
    inputs.forEach(function(input) {
        input.disabled = false;
        input.style.backgroundColor = '';
    });
}

// Warn before leaving page with unsaved changes
window.addEventListener('beforeunload', function(e) {
    if (Object.keys(pendingChanges).length > 0) {
        var message = 'Masz niezapisane zmiany. Czy na pewno chcesz opuścić stronę?';
        e.returnValue = message;
        return message;
    }
});

// Initialize the module when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (typeof SortProductCategory !== 'undefined') {
        SortProductCategory.init({$id_category}, '{$admin_url}');
    }
});
</script>
