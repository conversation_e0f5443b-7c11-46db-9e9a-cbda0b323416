/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function () {

	var $products_list_head = $('#products_list').find('.main_head');
	var $products_list_body = $('#products_list').find('.main_body');

	$products_list_body.on('refresh', refreshProductList);
	
	refreshProductList();
	
	function refreshProductList() {
		$products_list_head.on('change', '.multi', function () {
			var $scope = $(this);
			var $name = $scope.attr('send-name');
			$products_list_body.find('.to-send[send-name="' + $name + '"]').each(function () {
				var $element = $(this);
				var $tr = $element.closest('.element-row');
				var $check = $tr.find('.check_single');
				if ($check.prop('checked') && $tr.hasClass('combination-row'))
				{
					if ($element.is(':checkbox'))
					{
						if ($element.hasClass('no-checkbox'))
						{
							if ($element.val() == $scope.val())
								$element.prop('checked', $scope.prop('checked')).trigger('change');
						} else
							$element.prop('checked', $scope.prop('checked')).trigger('change');
					} else
						$element.val($scope.val()).trigger('change');
				}
			});
		});

		$products_list_body.on('change', '.to-send[send-name="price"]', function () {
			var $netto_el = $(this);
			var $brutto_el = $netto_el.closest('.combination-row').find('.to-send[send-name="price_brutto"]');
			var $p_id = $netto_el.closest('.combination-row').attr('p-id');
			var $tax_el = $products_list_body.find('.product-row[p-id="' + $p_id + '"]').find('.multi-com[send-name="tax"]');
			var $rate = $tax_el.find('option:selected').attr('extra') ? parseFloat($tax_el.find('option:selected').attr('extra')) : 0;
			var $netto = $netto_el.val() ? parseFloat($netto_el.val().replace(',','.')) : 0;
			$brutto_el.val(($netto + ($netto * ($rate / 100))).toFixed(2));
		});

		$products_list_body.on('change', '.to-send[send-name="price_brutto"]', function () {
			
			var $brutto_el = $(this);
			var $netto_el = $brutto_el.closest('.combination-row').find('.to-send[send-name="price"]');
			var $p_id = $brutto_el.closest('.combination-row').attr('p-id');
			var $tax_el = $products_list_body.find('.product-row[p-id="' + $p_id + '"]').find('.multi-com[send-name="tax"]');
			var $rate = $tax_el.find('option:selected').attr('extra') ? parseFloat($tax_el.find('option:selected').attr('extra')) : 0;
			var $brutto = $brutto_el.val() ? parseFloat($brutto_el.val().replace(',','.')) : 0;
			$netto_el.val((($brutto * 100) / (100 + $rate)).toFixed(6));
		});

		$products_list_body.on('change', '.multi-com[send-name="price"]', function () {
			var $netto_el = $(this);
			var $brutto_el = $netto_el.closest('.product-row').find('.multi-com[send-name="price_brutto"]');
			var $p_id = $netto_el.closest('.product-row').attr('p-id');
			var $tax_el = $products_list_body.find('.product-row[p-id="' + $p_id + '"]').find('.multi-com[send-name="tax"]');
			var $rate = $tax_el.find('option:selected').attr('extra') ? parseFloat($tax_el.find('option:selected').attr('extra')) : 0;
			var $netto = $netto_el.val() ? parseFloat($netto_el.val().replace(',','.')) : 0;
			$brutto_el.val(($netto + ($netto * ($rate / 100))).toFixed(2));
		});

		$products_list_body.on('change', '.multi-com[send-name="price_brutto"]', function () {
			var $brutto_el = $(this);
			var $netto_el = $brutto_el.closest('.product-row').find('.multi-com[send-name="price"]');
			var $p_id = $brutto_el.closest('.product-row').attr('p-id');
			var $tax_el = $products_list_body.find('.product-row[p-id="' + $p_id + '"]').find('.multi-com[send-name="tax"]');
			var $rate = $tax_el.find('option:selected').attr('extra') ? parseFloat($tax_el.find('option:selected').attr('extra')) : 0;
			var $brutto = $brutto_el.val() ? parseFloat($brutto_el.val().replace(',','.')) : 0;
			$netto_el.val((($brutto * 100) / (100 + $rate)).toFixed(6));
		});

		$products_list_body.on('change', '.multi-com[send-name="tax"]', function () {
			var $p_id = $(this).closest('.product-row').attr('p-id');
			$products_list_body.find('.combination-row[p-id="' + $p_id + '"] .to-send[send-name="price"]').each(function () {
				$(this).trigger('change');
			});

			$products_list_body.find('.product-row[p-id="' + $p_id + '"] .multi-com[send-name="price"]').each(function () {
				var $element = $(this);
				if ($element.val())
					$element.trigger('change');
			});
		});

		$products_list_body.on('change', '.multi-com', function () {
			var $scope = $(this);
			var $tr = $scope.closest('.element-row');
			var $p_id = $tr.attr('p-id');
			var $name = $scope.attr('send-name');
			$products_list_body.find('.combination-row[p-id="' + $p_id + '"]').each(function () {
				var $tr_com = $(this);
				var $check = $tr_com.find('.check_single');
				if ($check.prop('checked'))
				{
					var $element = $tr_com.find('.to-send[send-name="' + $name + '"]');
					if ($element.is(':checkbox'))
						$element.prop('checked', $scope.prop('checked')).trigger('change');
					else
						$element.val($scope.val()).trigger('change');
				}
			});
		});
	}
});