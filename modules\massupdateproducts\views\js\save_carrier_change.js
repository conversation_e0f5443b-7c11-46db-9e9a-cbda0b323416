/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware
 */

$(function() {
    var $save = $('#page-header-desc-configuration-save');
    var $saving_mask = $('.saving-mask');
    var $saving_loading = $('.saving-loading');
    var $products = $('#massupdateproducts-products');
    var $carriers = {};

    var load = function() {
	var $dataSend = {};
	var $data = $products.data('products');
	var $i = 0;
	$.each($data, function(key, value) {
	    $dataSend[$i++] = value.id_product;
	});

	return $dataSend;
    };

    var prepare = function($shift, $elements, $dataSend) {
	var $data = {};
	var $counter = $shift * $elements;
	var $i = 0;
	var $j = 0;
	if ($dataSend)
	    $.each($dataSend, function(key, value) {
		if ($counter == $i)
		{
		    $data[$j++] = value;
		}
		else
		    $i++;
		if ($j == $elements)
		    return false;
	    });
	return $data;
    };

    var save = function($shift, $elements, $dataSend) {
	$.ajax({
	    data: {
		dataSend: prepare($shift, $elements, $dataSend),
		is_ajax: true,
		save_mass: true,
		carriers: $carriers
	    },
	    type: 'POST',
	    dataType: 'json',
	    success: function($response) {
		if ($response.end)
		{
		    $massupdateproductsProcess = false;
		    $.notify(allProductsSave, 'success', {
			autoHideDelay: 2000
		    });
		    $saving_mask.hide();
		    $saving_loading.hide();
		}
		else
		{
		    if ($response.result)
			$.each($response.result, function(key, value) {
			    $.notify(value.message, value.error ? 'error' : 'success', {
				autoHideDelay: 2000
			    });
			});

		    save($shift + 1, $elements, $dataSend);
		}
	    },
	    error: function() {
		$saving_mask.hide();
		$saving_loading.hide();
		$massupdateproductsProcess = false;
	    }
	});
    };

    $save.on('click', function() {
	if (massProcess())
	    return false;

	$saving_mask.show();
	$saving_loading.show();
	$massupdateproductsProcess = true;

	$('html, body').animate({
	    scrollTop: ($products.offset().top - 150)
	}, 1000);

	save(0, 1, load());
    });

    $('.carriers').on('change', function() {
	var $i = 0;
	$carriers = {};
	$('.carriers').each(function() {
	    var $carrier = $(this);
	    if ($carrier.prop('checked'))
		$carriers[$i++] = parseInt($carrier.attr('name').substr(9));
	});

	console.log($carriers);
    });
});