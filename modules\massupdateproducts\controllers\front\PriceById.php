<?php

class massupdateproductsPriceByIdModuleFrontController extends ModuleFrontController
{

    public function __construct()
    {
        if (Tools::getValue('action') == 'getProductInfo') {
            $min = (int)$_REQUEST['minId'];
            $max = (int)$_REQUEST['maxId'];
            $this->getProducts((int)$min, (int)$max);
        }

        if (Tools::getValue('action') == 'setProductPrice') {
            $this->setProductNewPrice();
        }
    }

    public function getProducts($minId, $maxId)
    {
        $return = DB::getInstance()->executeS('SELECT `id_product` FROM `'._DB_PREFIX_.'product` WHERE `id_product` >= '.(int)$minId.' AND `id_product` <= '.(int)$maxId);
        echo json_encode($return);
        exit();
    }

    private function setProductNewPrice()
    {
        $id_product = (int)Tools::getValue('id');
        $kindDiscount = (int)Tools::getValue('kind');
        $typeDiscount = (int)Tools::getValue('type');
        $typePrice = (int)Tools::getValue('price');
        $amount = Tools::getValue('amount');
        $context = Context::getContext();

        $product = new Product((int)$id_product, false, (int)$context->cookie->id_lang);
        $taxRuleGroup = $product->id_tax_rules_group;
        $id_country = $context->country->id;
        $tax = Db::getInstance()->getValue('SELECT t.`rate` FROM `'._DB_PREFIX_.'tax` t LEFT JOIN `'._DB_PREFIX_.'tax_rule` tr ON t.`id_tax` = tr.`id_tax` WHERE tr.`id_country` = '.(int)$id_country.' AND tr.`id_tax_rules_group` = '.(int)$taxRuleGroup);
        $vat = 1 + ($tax / 100);

        if ($typePrice == 1) {
            // cena netto
            $productPrice = $product->price;
            if ($kindDiscount == 1) {
                // kwotowo
                if ($typeDiscount == 1) {
                    // obniżka
                    $productPrice -= $amount;
                } else {
                    // podwyżka
                    $productPrice += $amount;
                }
            } else {
                // procentowo
                if ($typeDiscount == 1) {
                    // obniżka
                    $productPrice = $productPrice / (1+ ($amount / 100));
                } else {
                    // podwyżka
                    $productPrice = $productPrice * (1+ ($amount / 100));
                }
            }
            $productPrice = number_format($productPrice, 6, '.', '');
        } else {
            //cena brutto
            $productPrice = $product->price; // netto
            $productPrice2 = $productPrice * $vat; // brutto
            if ($kindDiscount == 1) {
                // kwotowo
                if ($typeDiscount == 1) {
                    // obniżka
                    $productPrice2 -= $amount;
                } else {
                    // podwyżka
                    $productPrice2 += $amount;
                }
            } else {
                // procentowo
                if ($typeDiscount == 1) {
                    // obniżka
                    $productPrice2 = $productPrice2 / (1+ ($amount / 100));
                } else {
                    // podwyżka
                    $productPrice2 = $productPrice2 * (1+ ($amount / 100));
                }
            }
            $productPrice2 = number_format($productPrice2, 6, '.', '');
            $productPrice = number_format($productPrice2 / $vat, 6, '.', '');
        }
        $status = true;
        if ($productPrice < 0.01) {
            $status = false;
        } else {
            $product->price = $productPrice;
            $product->update();
        }

        $return['newPrice'] = $productPrice;
        $return['price'] = $product->price;
        $return['newPrice2'] = number_format($productPrice * $vat, 2, '.', '');
        $return['price2'] = number_format($product->price * $vat, 2, '.', '');
        $return['productName'] = $product->name;
        $return['id'] = $product->id;
        $return['reference'] = $product->reference;
        $return['status'] = $status;
        echo json_encode($return);
        exit();
    }

}
