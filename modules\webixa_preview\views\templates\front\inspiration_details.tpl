{extends file='page.tpl'}

{block name='page_content'}
  <section id="inspiration-details" class="container mx-auto px-4 py-6">

    <div class="relative mb-6 inspiration-slider-container">
      {if isset($inspiration_images) && $inspiration_images|count > 0}
        <div class="inspiration-slider relative" data-inspiration-id="{$inspiration->id}">
          <div class="slider-container overflow-hidden relative rounded-lg shadow-md">
            <div class="slider-track flex transition-transform duration-300">
              {foreach from=$inspiration_images item=image name=images}
                <div class="slider-slide w-full flex-shrink-0 {if $smarty.foreach.images.first}active{/if}" data-slide-index="{$smarty.foreach.images.index}">
                  <div class="relative aspect-ratio-container">
                    <img src="{if $smarty.foreach.images.first}{$image.url}{else}data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=={/if}"
                         data-src="{$image.url}"
                         alt="{$inspiration->title|escape:'htmlall':'UTF-8'}"
                         class="w-full h-full object-contain {if !$smarty.foreach.images.first}lazy-load{/if}">

                    {if isset($image.products) && $image.products}
                      {foreach from=$image.products item=product}
                        <div class="inspiration-hotspot"
                             style="left: {$product.position_x}%; top: {$product.position_y}%;"
                             data-id-product="{$product.id_product}"
                             data-toggle="tooltip"
                             data-placement="auto"
                             data-html="true"
                             data-animation="true"
                             data-trigger="manual"
                             data-container="body"
                             data-content='<div class="tooltip-product-content">
                               <div class="d-flex align-items-start">
                                 {if isset($product.cover) && $product.cover}
                                   <img src="{$product.cover.small.url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="tooltip-product-image">
                                 {elseif isset($product.image_url)}
                                   <img src="{$product.image_url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="tooltip-product-image">
                                 {/if}
                                 <div class="tooltip-product-info">
                                   <h5 class="tooltip-product-name">{$product.name|escape:'htmlall':'UTF-8'}</h5>
                                   {if isset($product.show_price) && $product.show_price}
                                     <div class="tooltip-product-price">{$product.price}</div>
                                   {elseif isset($product.price)}
                                     <div class="tooltip-product-price">{$product.price}</div>
                                   {/if}
                                   <div class="tooltip-product-actions">
                                     <form method="post" action="{url entity='cart' params=['add' => 1, 'id_product' => $product.id_product, 'token' => $static_token]}" style="margin: 0;">
                                       <input type="hidden" name="id_product" value="{$product.id_product}">
                                       <input type="hidden" name="qty" value="1">
                                       <button class="tooltip-btn tooltip-btn-primary" data-button-action="add-to-cart" type="submit">
                                         {l s='Add to cart' d='Shop.Theme.Actions'}
                                       </button>
                                     </form>
                                   </div>
                                 </div>
                               </div>
                             </div>'>
                          <div class="hotspot-cross">
                            <span>+</span>
                          </div>
                        </div>
                      {/foreach}
                    {/if}
                  </div>
                </div>
              {/foreach}
            </div>

            {if $inspiration_images|count > 1}
              <a href="#" class="slider-prev" id="slider-prev" onclick="return false;">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
              </a>
              <a href="#" class="slider-next" id="slider-next" onclick="return false;">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </a>
            {/if}
            <div class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
              {if $inspiration->date_add}
                <span class="block mb-1">{l s='Published on:' d='Modules.Webixapreview.Shop'} {$inspiration->date_add|date_format:"%Y-%m-%d"}</span>
              {/if}
              {if isset($inspiration_categories) && $inspiration_categories}
                <span class="block">
                  {l s='Categories:' d='Modules.Webixapreview.Shop'}
                  {foreach from=$inspiration_categories item=category name=catLoop}
                    <a href="{url entity='module' name='webixa_preview' controller='inspiration' params=['category_rewrite' => $category.link_rewrite]}" class="hover:underline">{$category.name|escape:'htmlall':'UTF-8'}</a>{if !$smarty.foreach.catLoop.last}, {/if}
                  {/foreach}
                </span>
              {/if}
            </div>
          </div>
        </div>
      {elseif $inspiration_image_url}
        <div class="relative inspiration-image-container rounded-lg shadow-md">
          <div class="aspect-ratio-container">
            <img src="{$inspiration_image_url}" alt="{$inspiration->title|escape:'htmlall':'UTF-8'}" class="w-full h-full object-contain rounded-lg">

          {if isset($inspiration_products) && $inspiration_products}
            {foreach from=$inspiration_products item=product}
              <div class="inspiration-hotspot"
                   style="left: {$product.inspiration_position_x}%; top: {$product.inspiration_position_y}%;"
                   data-id-product="{$product.id_product}"
                   data-toggle="tooltip"
                   data-placement="auto"
                   data-html="true"
                   data-animation="true"
                   data-trigger="manual"
                   data-container="body"
                   data-content='<div class="tooltip-product-content">
                     <div class="d-flex align-items-start">
                       {if isset($product.cover) && $product.cover}
                         <img src="{$product.cover.small.url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="tooltip-product-image">
                       {elseif isset($product.image_url)}
                         <img src="{$product.image_url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="tooltip-product-image">
                       {/if}
                       <div class="tooltip-product-info">
                         <h5 class="tooltip-product-name">{$product.name|escape:'htmlall':'UTF-8'}</h5>
                         {if isset($product.show_price) && $product.show_price}
                           <div class="tooltip-product-price">{$product.price}</div>
                         {elseif isset($product.price)}
                           <div class="tooltip-product-price">{$product.price}</div>
                         {/if}
                         <div class="tooltip-product-actions">
                           <form method="post" action="{url entity='cart' params=['add' => 1, 'id_product' => $product.id_product, 'token' => $static_token]}" style="margin: 0;">
                             <input type="hidden" name="id_product" value="{$product.id_product}">
                             <input type="hidden" name="qty" value="1">
                             <button class="tooltip-btn tooltip-btn-primary" data-button-action="add-to-cart" type="submit">
                               {l s='Add to cart' d='Shop.Theme.Actions'}
                             </button>
                           </form>
                         </div>
                       </div>
                     </div>
                   </div>'>
                <div class="hotspot-cross">
                  <span>+</span>
                </div>
                  <div class="flex items-start space-x-3">
                    {if $product.cover}
                      <img src="{$product.cover.small.url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" loading="lazy" class="w-16 h-16 object-cover flex-shrink-0 border border-gray-200 rounded">
                    {elseif isset($product.image_url)}
                      <img src="{$product.image_url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" loading="lazy" class="w-16 h-16 object-cover flex-shrink-0 border border-gray-200 rounded">
                    {/if}
                    <div class="flex-grow">
                      <h5 class="popup-product-name text-sm font-semibold mb-1 line-clamp-2">{$product.name|escape:'htmlall':'UTF-8'}</h5>
                      {if isset($product.show_price) && $product.show_price}
                        <div class="popup-product-price text-xs font-bold text-primary mb-2">{$product.price}</div>
                      {elseif isset($product.price)}
                        <div class="popup-product-price text-xs font-bold text-primary mb-2">{$product.price}</div>
                      {/if}
                      <div class="mt-2">
                        <a href="{url entity='product' id=$product.id_product}" class="block text-xs underline text-gray-600 hover:text-gray-800 mb-2">
                          {l s='View details' d='Shop.Theme.Actions'}
                        </a>

                        {if isset($allow_add_to_cart) && $allow_add_to_cart && isset($product.add_to_cart_url) && $product.add_to_cart_url && isset($product.customizable) && !$product.customizable}
                          <form action="{url entity='cart' params=['add' => 1, 'id_product' => $product.id_product, 'token' => $static_token]}" method="post">
                            <input type="hidden" name="id_product" value="{$product.id_product}">
                            <input type="hidden" name="qty" value="1">
                            {if isset($product.id_product_attribute) && $product.id_product_attribute}
                              <input type="hidden" name="id_product_attribute" value="{$product.id_product_attribute}">
                            {/if}
                            <button class="w-full text-xs px-2 py-1 rounded transition duration-200 {if isset($product.available_for_order) && $product.available_for_order}bg-primary text-white hover:bg-primary-dark{else}bg-gray-300 text-gray-500 cursor-not-allowed{/if}"
                                    data-button-action="add-to-cart"
                                    type="submit"
                                    {if isset($product.available_for_order) && !$product.available_for_order}disabled{/if}>
                              {if isset($product.available_for_order) && $product.available_for_order}
                                {l s='Add to cart' d='Shop.Theme.Actions'}
                              {else}
                                {l s='Out of stock' d='Shop.Theme.Actions'}
                              {/if}
                            </button>
                          </form>
                        {elseif isset($product.customizable) && $product.customizable}
                          <a class="block w-full text-center text-xs px-2 py-1 rounded bg-primary text-white hover:bg-primary-dark transition duration-200" href="{$product.url}">
                            {l s='Customize' d='Shop.Theme.Actions'}
                          </a>
                        {else}
                          <a href="{url entity='product' id=$product.id_product}" class="block w-full text-center text-xs px-2 py-1 rounded bg-primary text-white hover:bg-primary-dark transition duration-200">
                            {l s='View details' d='Shop.Theme.Actions'}
                          </a>
                        {/if}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            {/foreach}
          {/if}

          <div class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs p-2 rounded">
            {if $inspiration->date_add}
              <span class="block mb-1">{l s='Published on:' d='Modules.Webixapreview.Shop'} {$inspiration->date_add|date_format:"%Y-%m-%d"}</span>
            {/if}
            {if isset($inspiration_categories) && $inspiration_categories}
              <span class="block">
                {l s='Categories:' d='Modules.Webixapreview.Shop'}
                {foreach from=$inspiration_categories item=category name=catLoop}
                  <a href="{url entity='module' name='webixa_preview' controller='inspiration' params=['category_rewrite' => $category.link_rewrite]}" class="hover:underline">{$category.name|escape:'htmlall':'UTF-8'}</a>{if !$smarty.foreach.catLoop.last}, {/if}
                {/foreach}
              </span>
            {/if}
          </div>
          </div>
        </div>
      {/if}
    </div>

    <h1 class="text-3xl font-bold mb-4">{$inspiration->title|escape:'htmlall':'UTF-8'}</h1>

    {if isset($inspiration_images) && $inspiration_images|count > 0 && isset($inspiration_images[0].products) && $inspiration_images[0].products|count > 0}
      <div class="products-in-current-image mt-6 mb-8">
        <h3 class="text-xl font-semibold mb-4 pb-2 border-b">{l s='Products in this image' d='Modules.Webixapreview.Shop'}</h3>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
          {foreach from=$inspiration_images[0].products item=product name=products}
            <div class="product-item-small border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 hover:border-primary">
              <a href="{url entity='product' id=$product.id_product}" class="block h-full">
                <div class="relative pb-[100%]">
                  {if isset($product.cover) && isset($product.cover.bySize)}
                    <img src="{$product.cover.bySize.home_default.url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="absolute inset-0 w-full h-full object-cover">
                  {elseif isset($product.image_url)}
                    <img src="{$product.image_url}" alt="{$product.name|escape:'htmlall':'UTF-8'}" class="absolute inset-0 w-full h-full object-cover">
                  {else}
                    <div class="absolute inset-0 w-full h-full bg-gray-200 flex items-center justify-center">
                      <span class="text-gray-500 text-xs">{l s='No image' d='Shop.Theme.Global'}</span>
                    </div>
                  {/if}
                </div>
                <div class="p-3">
                  <h4 class="text-sm font-medium line-clamp-2 mb-2">{$product.name|escape:'htmlall':'UTF-8'}</h4>
                  <div class="text-sm font-bold text-primary mb-2">{$product.price}</div>
                  <div class="text-center">
                    <form action="{url entity='cart' params=['add' => 1, 'id_product' => $product.id_product, 'token' => $static_token]}" method="post" class="add-to-cart-form">
                      <input type="hidden" name="id_product" value="{$product.id_product}">
                      <input type="hidden" name="qty" value="1">
                      <button class="w-full text-xs px-2 py-1 rounded bg-primary text-white hover:bg-primary-dark transition duration-200"
                              data-button-action="add-to-cart"
                              type="submit">
                        {l s='Add to cart' d='Shop.Theme.Actions'}
                      </button>
                    </form>
                  </div>
                </div>
              </a>
            </div>
          {/foreach}
        </div>
      </div>
    {/if}

    {if $inspiration->description}
      <div class="prose max-w-none mb-8">
        {$inspiration->description nofilter}
      </div>
    {/if}

    {if isset($related_inspirations) && $related_inspirations}
      <section class="inspiration-related mt-10 pt-6 border-t border-gray-200">
        <h3 class="text-xl font-semibold mb-4">{l s='Related Inspirations' d='Modules.Webixapreview.Shop'}</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
           {foreach from=$related_inspirations item=related_inspiration}
             <article class="inspiration-item group relative border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300" data-id-inspiration="{$related_inspiration.id_inspiration}">
               <a href="{$related_inspiration.url}" class="inspiration-link block">
                 {if $related_inspiration.image_url}
                   <img src="{$related_inspiration.image_url}" alt="{$related_inspiration.title|escape:'htmlall':'UTF-8'}" loading="lazy" class="w-full h-40 object-cover transition-transform duration-300 group-hover:scale-105">
                 {else}
                   <div class="w-full h-40 bg-gray-200 flex items-center justify-center text-gray-500 italic">
                       <span>{$related_inspiration.title|escape:'htmlall':'UTF-8'}</span>
                   </div>
                 {/if}
                 <div class="absolute inset-x-0 bottom-0 bg-black bg-opacity-60 p-2 text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                   <h4 class="inspiration-title text-white font-semibold text-sm">{$related_inspiration.title|escape:'htmlall':'UTF-8'}</h4>
                 </div>
               </a>
             </article>
           {/foreach}
        </div>
      </section>
    {/if}


    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            var currentIndex = 0;
            var sliderTrack = document.querySelector('.slider-track');
            var slides = document.querySelectorAll('.slider-slide');
            var prevBtn = document.getElementById('slider-prev');
            var nextBtn = document.getElementById('slider-next');

            if (prevBtn) prevBtn.style.display = 'flex';
            if (nextBtn) nextBtn.style.display = 'flex';

            if (!sliderTrack || slides.length <= 1) return;

            function goToSlide(index) {
                if (index < 0) index = slides.length - 1;
                if (index >= slides.length) index = 0;

                currentIndex = index;

                sliderTrack.style.transform = 'translateX(-' + (currentIndex * 100) + '%)';

                for (var i = 0; i < slides.length; i++) {
                    if (i === currentIndex) {
                        slides[i].classList.add('active');

                        var lazyImg = slides[i].querySelector('img.lazy-load');
                        if (lazyImg && lazyImg.dataset.src) {
                            lazyImg.src = lazyImg.dataset.src;
                            lazyImg.classList.remove('lazy-load');
                        }
                    } else {
                        slides[i].classList.remove('active');
                    }
                }

                var currentSlide = slides[currentIndex];
                var hotspots = currentSlide.querySelectorAll('.inspiration-hotspot');
                var productsSection = document.querySelector('.products-in-current-image');

                if (productsSection) {
                    var title = productsSection.querySelector('h3');
                    if (title) {
                        title.textContent = 'Products in this image (' + hotspots.length + ')';
                    }
                }
            }

            if (prevBtn) {
                prevBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    goToSlide(currentIndex - 1);
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    goToSlide(currentIndex + 1);
                });
            }

            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft') {
                    goToSlide(currentIndex - 1);
                } else if (e.key === 'ArrowRight') {
                    goToSlide(currentIndex + 1);
                }
            });

            goToSlide(0);
        });
    </script>


    <style>
        .slider-container {
            position: relative;
            overflow: hidden;
            width: 100%;
            max-height: 600px;
        }

        .slider-track {
            display: flex;
            transition: transform 0.5s ease;
            width: 100%;
            height: 100%;
        }

        .slider-slide {
            flex: 0 0 100%;
            width: 100%;
            height: auto;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slider-slide img {
            max-width: 100%;
            max-height: 600px;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .aspect-ratio-container {
            position: relative;
            width: 100%;
            padding-bottom: 56.25%;
            overflow: hidden;
        }

        .aspect-ratio-container img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            background-color: #f8f9fa;
        }
        .slider-prev,
        .slider-next {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(255, 255, 255, 0.9);
            border: 2px solid #2fb5d2;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex !important;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
            opacity: 0.9;
            text-decoration: none;
            visibility: visible !important;
        }

        .slider-prev {
            left: 20px;
        }

        .slider-next {
            right: 20px;
        }

        .slider-prev:hover,
        .slider-next:hover {
            background-color: #2fb5d2;
            color: white;
            transform: translateY(-50%) scale(1.1);
            opacity: 1;
        }

        .slider-prev svg,
        .slider-next svg {
            width: 24px;
            height: 24px;
            stroke-width: 3;
        }
        
        .inspiration-hotspot {
          position: absolute;
          transform: translate(-50%, -50%);
          z-index: 100;
          cursor: pointer;
          width: 30px;
          height: 30px;
          pointer-events: auto;
        }

        .slider-slide {
          position: relative;
        }

        .slider-slide .relative {
          position: relative;
        }

        .hotspot-cross {
          position: absolute;
          top: 0;
          left: 0;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.9);
          border: 3px solid #2fb5d2;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 18px;
          color: #2fb5d2;
          transition: all 0.3s ease;
          z-index: 500;
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(47, 181, 210, 0.7);
          }

          70% {
            transform: scale(1.1);
            box-shadow: 0 0 0 10px rgba(47, 181, 210, 0);
          }

          100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(47, 181, 210, 0);
          }
        }

        .inspiration-hotspot:hover .hotspot-cross {
          transform: scale(1.2) !important;
          background-color: #2fb5d2 !important;
          color: white !important;
          animation: none;
        }










        .products-in-current-image .grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          gap: 1rem;
        }

        .product-item-small {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .product-item-small .relative {
          flex: 0 0 auto;
        }

        .product-item-small .p-2 {
          flex: 1 1 auto;
          display: flex;
          flex-direction: column;
        }

        .product-item-small h4 {
          flex: 1 1 auto;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* Custom tooltip styles */
        .custom-tooltip {
            background: white !important;
            color: #333 !important;
            border: 1px solid #ddd !important;
            border-radius: 8px !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
            padding: 0 !important;
        }

        .custom-tooltip .tooltip-product-content {
            padding: 15px;
        }

        .custom-tooltip .d-flex {
            display: flex !important;
        }

        .custom-tooltip .align-items-start {
            align-items: flex-start !important;
        }

        .tooltip-product-image {
            width: 64px;
            height: 64px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .tooltip-product-info {
            flex: 1;
        }

        .tooltip-product-name {
            font-size: 14px;
            font-weight: 600;
            color: #232323;
            line-height: 1.2;
            margin-bottom: 8px;
        }

        .tooltip-product-price {
            color: #2fb5d2;
            font-weight: 700;
            font-size: 12px;
            margin-bottom: 12px;
        }

        .tooltip-product-actions {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .tooltip-btn {
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            transition: all 0.2s;
            display: block;
            border: none;
            cursor: pointer;
        }

        .tooltip-btn-primary {
            background-color: #2fb5d2;
            color: white;
        }

        .tooltip-btn-primary:hover {
            background-color: #2592a9;
            text-decoration: none;
            color: white;
        }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Use custom tooltips
        initCustomTooltips();
    });

    // Custom tooltip implementation
    function initCustomTooltips() {
        const hotspots = document.querySelectorAll('[data-toggle="tooltip"]');

        hotspots.forEach(function(hotspot) {
            let tooltip = null;
            let hideTimeout = null;

            function showTooltip() {
                if (tooltip) return; // Already showing

                // Clear any pending hide
                if (hideTimeout) {
                    clearTimeout(hideTimeout);
                    hideTimeout = null;
                }

                // Create tooltip
                tooltip = document.createElement('div');
                tooltip.className = 'custom-tooltip fade show';

                // Get and parse the HTML content
                const htmlContent = hotspot.getAttribute('data-content') || hotspot.getAttribute('title');
                tooltip.innerHTML = htmlContent;
                tooltip.style.cssText = `
                    position: fixed;
                    z-index: 99999;
                    background: white;
                    color: #333;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                    font-size: 12px;
                    max-width: 300px;
                    pointer-events: auto;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    padding: 0;
                `;

                document.body.appendChild(tooltip);

                // Position tooltip intelligently
                const rect = hotspot.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();
                const margin = 15;

                let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
                let top = rect.top - tooltipRect.height - margin;

                // Adjust if tooltip goes off screen
                if (left < margin) left = margin;
                if (left + tooltipRect.width > window.innerWidth - margin) {
                    left = window.innerWidth - tooltipRect.width - margin;
                }
                if (top < margin) {
                    top = rect.bottom + margin;
                }

                tooltip.style.left = left + 'px';
                tooltip.style.top = top + 'px';

                // Show tooltip
                setTimeout(() => {
                    if (tooltip) tooltip.style.opacity = '1';
                }, 10);

                // Add hover listeners to tooltip
                tooltip.addEventListener('mouseenter', function() {
                    if (hideTimeout) {
                        clearTimeout(hideTimeout);
                        hideTimeout = null;
                    }
                });

                tooltip.addEventListener('mouseleave', function() {
                    hideTooltip();
                });
            }

            function hideTooltip() {
                if (hideTimeout) return; // Already hiding

                hideTimeout = setTimeout(() => {
                    if (tooltip) {
                        tooltip.style.opacity = '0';
                        setTimeout(() => {
                            if (tooltip && tooltip.parentNode) {
                                tooltip.parentNode.removeChild(tooltip);
                                tooltip = null;
                            }
                        }, 300);
                    }
                    hideTimeout = null;
                }, 100);
            }

            hotspot.addEventListener('mouseenter', showTooltip);
            hotspot.addEventListener('mouseleave', hideTooltip);
        });
    }

    // Function to add product to cart from tooltip
    function addToCartFromTooltip(productId) {
        // Create a hidden form with the working button structure
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = prestashop.urls.pages.cart || (prestashop.urls.base_url + 'cart');
        form.style.display = 'none';

        // Add hidden inputs
        const addInput = document.createElement('input');
        addInput.type = 'hidden';
        addInput.name = 'add';
        addInput.value = '1';
        form.appendChild(addInput);

        const productInput = document.createElement('input');
        productInput.type = 'hidden';
        productInput.name = 'id_product';
        productInput.value = productId;
        form.appendChild(productInput);

        const qtyInput = document.createElement('input');
        qtyInput.type = 'hidden';
        qtyInput.name = 'qty';
        qtyInput.value = '1';
        form.appendChild(qtyInput);

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = 'token';
        tokenInput.value = prestashop.static_token;
        form.appendChild(tokenInput);

        // Create the button with the exact same attributes as the working one
        const button = document.createElement('button');
        button.className = 'w-full text-xs px-2 py-1 rounded bg-primary text-white hover:bg-primary-dark transition duration-200';
        button.setAttribute('data-button-action', 'add-to-cart');
        button.type = 'submit';
        button.textContent = 'Add to cart';
        form.appendChild(button);

        document.body.appendChild(form);

        // Submit the form
        form.submit();
    }
    </script>

  </section>
{/block}
