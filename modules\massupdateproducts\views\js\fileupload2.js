/**
 * 2010-2014 prestahelp.com
 * 
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license Shareware

 */

(function () {
    $.fn.fileupload2 = function ($types) {
        return this.each(function () {
            var $scope = $(this);
            var $tbody = $scope.find('tbody');
            var files = $scope.find('.file-upload');
            var $products_list = $('#products_list').find('.main_body');
            files.on('change', function (event) {
                var $files = event.target.files;
                event.stopPropagation();
                event.preventDefault();
                $.each($files, function (key, value)
                {
                    if ($.inArray(value.type, $types) >= 0)
                    {
                        var $line = null;
                        var $load = null;
                        var $image_after_upload = null;
                        var $tr = null;
                        var $str = '<tr attr-q="' + key + '"><td><i class="fa fa-refresh icon-spin icon-fw load-upload"></i></td><td>' + value.name + '</td><td class="image-after-upload"></td><td>' + value.type + '</td><td>' + parseInt(value.size / 1024) + ' KB</td><td><i style="cursor: pointer;" class="fa fa-minus-circle upload-cancel"></i></td></tr>';
                        $tbody.append($str);
                        $tr = $tbody.find('tr[attr-q="' + key + '"]');
                        $line = $tr.find('.upload-cancel');
                        $load = $tr.find('.load-upload');
                        $image_after_upload = $tr.find('.image-after-upload');
                        var data = new FormData();
                        data.append(0, value);
                        var ajaxObj = $.ajax({
                            url: urlUpload,
                            dataType: 'json',
                            data: data,
                            cache: false,
                            processData: false,
                            contentType: false,
                            type: 'POST',
                            success: function (data) {
                                var $td = $line.closest('td');
                                
                                $line.remove();
                                $td.append('');
                                $load.remove();
                                $image_after_upload.append(data.field);
                                $tr.attr('attr-q', data.file_name);
                                var $html = $tr.prop('outerHTML');
                                $tr.find('.image-show-content-tools').remove();
                                $tr.find('.image-show-content-picture-legend').remove();
                                $products_list.trigger('addnewfile', $html);
                            }
                        });
                        $line.data('ajaxObj', ajaxObj);

                        $line.on('click', function () {
                            var $scope = $(this);
                            if ($scope.data('ajaxObj')) {
                                $scope.data('ajaxObj').abort();
                                $scope.closest('tr').remove();
                            }
                        });

                        $tr.on('click', '.upload-remove', function () {
                            $tr.remove();
                            $scope.trigger('removenewfile', $tr);
                        });
                    }
                });
            });
        });
    };
})($);