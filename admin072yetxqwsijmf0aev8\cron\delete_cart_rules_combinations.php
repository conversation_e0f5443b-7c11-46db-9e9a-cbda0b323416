<?php

require dirname(__FILE__) . '/../../config/config.inc.php';

$sql = [];
$sql[] = '
    CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'cart_rule_combination_to_delete` (
    `id_cart_rule_2` int(10) unsigned NOT NULL,
    KEY `id_cart_rule_2` (`id_cart_rule_2`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
';

$sql[] = '
    TRUNCATE `' . _DB_PREFIX_ . 'cart_rule_combination_to_delete`
';

$sql[] = '
    INSERT INTO `' . _DB_PREFIX_ . 'cart_rule_combination_to_delete` (`id_cart_rule_2`)
    SELECT
    vcrc.id_cart_rule_1 as id_cart_rule_2
    FROM ' . _DB_PREFIX_ . 'cart_rule_combination vcrc
    LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule vcr ON(vcrc.id_cart_rule_1=vcr.id_cart_rule)
    WHERE vcr.id_cart_rule IS NULL
    GROUP BY vcrc.id_cart_rule_1
    LIMIT 10000
';

$sql[] = '
    DELETE ' . _DB_PREFIX_ . 'cart_rule_combination FROM ' . _DB_PREFIX_ . 'cart_rule_combination
    WHERE id_cart_rule_1 IN(SELECT id_cart_rule_2 as id_cart_rule_1 FROM `' . _DB_PREFIX_ . 'cart_rule_combination_to_delete`)
';

$sql[] = '
    DELETE ' . _DB_PREFIX_ . 'cart_rule_combination FROM ' . _DB_PREFIX_ . 'cart_rule_combination
    WHERE id_cart_rule_2 IN(SELECT id_cart_rule_2 FROM `' . _DB_PREFIX_ . 'cart_rule_combination_to_delete`)
';

$db = Db::getInstance();
foreach ($sql as $query) {
    $db->execute($query);
}

return 1;
